// 产品类型定义
export interface Product {
  id: string;
  name: string;
  category: string;
  amazonUrl: string;
  images: string[]; // 产品图片URL数组
  features?: string; // 产品特性描述
  rating: number; // 产品评分 (0.0-5.0)
  createdAt: Date;
  updatedAt: Date;
}

// 文章类型定义
export interface Article {
  id: string;
  title: string;
  content: string;
  excerpt: string;
  slug: string;
  productId: string;
  product?: Product;
  coverImage?: string; // 文章封面图片URL
  videoUrl?: string; // 产品展示视频URL
  metaTitle: string;
  metaDescription: string;
  keywords: string[];
  published: boolean;
  publishedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

// 产品分类
export enum ProductCategory {
  VIBRATORS = 'vibrators',
  BULLETS = 'bullets', 
  NIPPLE_CLAMPS = 'nipple-clamps',
  NIPPLE_SUCKERS = 'nipple-suckers',
  COCK_RINGS = 'cock-rings',
  MASTURBATORS = 'masturbators'
}

// 产品分类类型定义
export interface Category {
  id: string;
  name: string;
  slug: string;
  description?: string;
  createdAt: Date;
}

// SEO元数据
export interface SEOData {
  title: string;
  description: string;
  keywords: string[];
  ogImage?: string;
  canonical?: string;
}

// =====================================================
// 测验系统类型定义
// =====================================================

// 问题选项
export interface QuizOption {
  id: string;
  text: string;
  value: string;
  emoji?: string;
  score?: Record<string, number>; // 维度评分 { dimension: score }
}

// 问题类型枚举
export enum QuestionType {
  SINGLE_CHOICE = 'single_choice',
  MULTIPLE_CHOICE = 'multiple_choice',
  SLIDER = 'slider',
  RATING = 'rating'
}

// 测验问题
export interface QuizQuestion {
  id: string;
  text: string;
  type: QuestionType;
  options?: QuizOption[];
  weight?: number; // 问题权重，默认为1
  required?: boolean; // 是否必答，默认为true
  // 滑块和评分问题的特殊属性
  min?: number;
  max?: number;
  step?: number;
  labels?: string[]; // 用于评分问题的标签
}

// 结果组
export interface QuizResultGroup {
  id: string;
  title: string;
  description: string;
  emoji: string;
  // 维度评分范围 { dimension: [min, max] }
  dimensionScores: Record<string, [number, number]>;
  // 关联的产品分类
  productCategories: string[];
  // 推荐的具体产品ID（可选）
  recommendedProducts?: string[];
}

// 测验配置
export interface QuizConfig {
  id: string;
  title: string;
  description: string;
  questions: QuizQuestion[];
  resultGroups: QuizResultGroup[];
  dimensions: string[]; // 评分维度列表
  active: boolean; // 是否启用
  createdAt: Date;
  updatedAt: Date;
}

// 用户测验回答
export interface QuizAnswer {
  questionId: string;
  value: string | string[] | number; // 支持不同类型的答案
}

// 测验会话
export interface QuizSession {
  id: string;
  quizConfigId: string;
  answers: QuizAnswer[];
  result?: QuizResult;
  startedAt: Date;
  completedAt?: Date;
  userId?: string; // 可选的用户ID
  sessionId: string; // 浏览器会话ID
}

// 测验结果
export interface QuizResult {
  resultGroupId: string;
  resultGroup: QuizResultGroup;
  dimensionScores: Record<string, number>; // 计算出的维度得分
  recommendedProducts: Product[]; // 推荐的产品列表
  confidence: number; // 推荐置信度 (0-1)
}

// 产品分组规则
export interface ProductGroupRule {
  id: string;
  name: string;
  description: string;
  emoji: string;
  rules: {
    experienceLevels: string[]; // 经验级别
    usageTypes: string[]; // 使用类型
    experienceTypes: string[]; // 体验类型
    categories: string[]; // 产品分类
  };
  priority: number; // 优先级，数字越大优先级越高
  active: boolean;
  createdAt: Date;
  updatedAt: Date;
}

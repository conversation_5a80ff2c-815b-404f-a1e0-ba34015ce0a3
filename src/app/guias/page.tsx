import Link from 'next/link';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';

export default function GuiasPage() {
  const guides = [
    {
      title: 'Guía para Principiantes: Có<PERSON> Empezar Tu Viaje de Placer',
      description: 'Una guía completa para quienes se inician en el mundo del bienestar íntimo. Aprende los conceptos básicos, precauciones importantes y cómo elegir tu primer producto.',
      emoji: '📚',
      href: '/guias/principiantes',
      category: 'Principiantes',
      readTime: '10 min',
      topics: ['Conceptos básicos', 'Primeros pasos', 'Seguridad', 'Elección de productos'],
    },
    {
      title: 'Ciencia de Materiales: Cómo Elegir Productos Seguros',
      description: 'Análisis profundo de los materiales utilizados en productos íntimos. Aprende a identificar materiales seguros y evitar aquellos que pueden ser dañinos.',
      emoji: '🔬',
      href: '/guias/materiales',
      category: 'Seguridad',
      readTime: '15 min',
      topics: ['Silicona médica', 'Materiales tóxicos', 'Certificaciones', 'Cuidado y limpieza'],
    },
    {
      title: 'Interacción en Pareja: Cómo Mejorar la Intimidad',
      description: 'Consejos profesionales para incorporar productos íntimos en la relación de pareja. Comunicación, consentimiento y exploración conjunta.',
      emoji: '💑',
      href: '/guias/parejas',
      category: 'Relaciones',
      readTime: '12 min',
      topics: ['Comunicación', 'Consentimiento', 'Exploración', 'Intimidad'],
    },
    {
      title: 'Guía de Limpieza y Mantenimiento',
      description: 'Aprende las mejores prácticas para mantener tus productos íntimos limpios, seguros y en óptimas condiciones durante más tiempo.',
      emoji: '🧼',
      href: '/guias/cuidado',
      category: 'Mantenimiento',
      readTime: '8 min',
      topics: ['Limpieza diaria', 'Almacenamiento', 'Desinfección', 'Vida útil'],
    },
    {
      title: 'Anatomía y Placer: Conoce Tu Cuerpo',
      description: 'Una guía educativa sobre anatomía íntima y zonas erógenas. Información científica presentada de manera accesible y respetuosa.',
      emoji: '🧠',
      href: '/guias/anatomia',
      category: 'Educación',
      readTime: '20 min',
      topics: ['Anatomía femenina', 'Anatomía masculina', 'Zonas erógenas', 'Respuesta sexual'],
    },
    {
      title: 'Mitos y Realidades del Bienestar Íntimo',
      description: 'Desmontamos los mitos más comunes sobre productos íntimos y sexualidad, basándonos en evidencia científica y experiencia profesional.',
      emoji: '🔍',
      href: '/guias/mitos',
      category: 'Educación',
      readTime: '18 min',
      topics: ['Mitos comunes', 'Evidencia científica', 'Tabúes', 'Educación sexual'],
    },
  ];

  const categories = ['Todos', 'Principiantes', 'Seguridad', 'Relaciones', 'Mantenimiento', 'Educación'];

  return (
    <div className="min-h-screen bg-white">
      <Navbar />

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-pink-50 to-purple-50 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="flex justify-center items-center mb-6">
              <div className="text-6xl mr-4">📚</div>
              <div className="text-2xl font-medium text-pink-600">Guía Profesional</div>
            </div>
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              Guías de{' '}
              <span className="bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent">
                Bienestar Íntimo
              </span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              Consejos de compra profesionales y guías de uso detalladas para que cada elección sea segura, 
              informada y satisfactoria.
            </p>
          </div>
        </div>
      </section>

      {/* Categories Filter */}
      <section className="py-8 bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-wrap justify-center gap-4">
            {categories.map((category) => (
              <button
                key={category}
                className="px-6 py-2 rounded-full border-2 border-pink-200 text-pink-600 hover:bg-pink-600 hover:text-white transition-all"
              >
                {category}
              </button>
            ))}
          </div>
        </div>
      </section>

      {/* Guides Grid */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {guides.map((guide) => (
              <div
                key={guide.href}
                className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all transform hover:scale-105"
              >
                <div className="h-48 bg-gradient-to-br from-blue-100 to-indigo-100 flex items-center justify-center">
                  <div className="text-8xl">{guide.emoji}</div>
                </div>
                <div className="p-6">
                  <div className="flex items-center justify-between mb-3">
                    <span className="bg-pink-100 text-pink-600 px-3 py-1 rounded-full text-sm font-medium">
                      {guide.category}
                    </span>
                    <span className="text-gray-500 text-sm">
                      📖 {guide.readTime}
                    </span>
                  </div>
                  
                  <h3 className="text-xl font-semibold text-gray-900 mb-3 line-clamp-2">
                    {guide.title}
                  </h3>
                  
                  <p className="text-gray-600 mb-4 line-clamp-3">
                    {guide.description}
                  </p>
                  
                  <div className="mb-4">
                    <div className="flex flex-wrap gap-2">
                      {guide.topics.slice(0, 3).map((topic, index) => (
                        <span
                          key={index}
                          className="bg-gray-100 text-gray-600 px-2 py-1 rounded text-xs"
                        >
                          {topic}
                        </span>
                      ))}
                      {guide.topics.length > 3 && (
                        <span className="bg-gray-100 text-gray-600 px-2 py-1 rounded text-xs">
                          +{guide.topics.length - 3} más
                        </span>
                      )}
                    </div>
                  </div>
                  
                  <Link
                    href={guide.href}
                    className="inline-flex items-center space-x-2 bg-pink-600 hover:bg-pink-700 text-white px-4 py-2 rounded-lg font-medium transition-colors w-full justify-center"
                  >
                    <span>Leer Guía</span>
                    <span>→</span>
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-gradient-to-br from-pink-50 to-purple-50 py-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h3 className="text-3xl font-bold text-gray-900 mb-4">
            ¿Necesitas Recomendaciones Personalizadas?
          </h3>
          <p className="text-lg text-gray-600 mb-8">
            Prueba nuestro quiz interactivo para obtener recomendaciones específicas basadas en tus preferencias.
          </p>
          <Link
            href="/quiz"
            className="inline-flex items-center space-x-3 bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-700 hover:to-purple-700 text-white px-8 py-4 rounded-xl font-medium transition-all transform hover:scale-105 shadow-lg"
          >
            <span>🎯</span>
            <span>Hacer Quiz Personalizado</span>
            <span>✨</span>
          </Link>
        </div>
      </section>

      <Footer />
    </div>
  );
}

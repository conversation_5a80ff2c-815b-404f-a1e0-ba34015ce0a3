import { NextResponse } from 'next/server';
import { productService, articleService } from '@/lib/database';
import { siteConfig } from '@/lib/seo';

export async function GET() {
  try {
    // 获取所有数据
    const [products, articles] = await Promise.all([
      productService.getAll(),
      articleService.getPublished()
    ]);

    const baseUrl = siteConfig.url;
    const currentDate = new Date().toISOString();

    let sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:video="http://www.google.com/schemas/sitemap-video/1.1">`;

    // 为每篇有视频的文章添加视频信息
    articles.forEach(article => {
      if (article.videoUrl) {
        const product = products.find(p => p.id === article.productId);
        
        sitemap += `
  <url>
    <loc>${baseUrl}/articulo/${article.slug}</loc>
    <video:video>
      <video:thumbnail_loc>${article.coverImage || (product?.images?.[0] || '')}</video:thumbnail_loc>
      <video:title>${article.title}</video:title>
      <video:description>${article.excerpt || article.title}</video:description>
      <video:content_loc>${article.videoUrl}</video:content_loc>
      <video:duration>300</video:duration>
      <video:publication_date>${article.publishedAt || article.createdAt}</video:publication_date>
      <video:family_friendly>no</video:family_friendly>
      <video:restriction relationship="allow">MX</video:restriction>
      <video:platform relationship="allow">web</video:platform>
      <video:requires_subscription>no</video:requires_subscription>
      <video:live>no</video:live>
    </video:video>
  </url>`;
      }
    });

    sitemap += `
</urlset>`;

    return new NextResponse(sitemap, {
      status: 200,
      headers: {
        'Content-Type': 'application/xml',
        'Cache-Control': 'public, max-age=3600, s-maxage=3600'
      }
    });

  } catch (error) {
    console.error('Error generating video sitemap:', error);
    return new NextResponse('Error generating video sitemap', { status: 500 });
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { sitemapCache } from '@/lib/sitemap-cache';

export async function GET(request: NextRequest) {
  try {
    // 获取缓存统计信息
    const stats = sitemapCache.getStats();
    
    return NextResponse.json({
      success: true,
      stats,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error getting cache stats:', error);
    
    return NextResponse.json({
      success: false,
      message: '获取缓存统计失败',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const key = url.searchParams.get('key');
    
    if (key) {
      // 清除特定缓存
      sitemapCache.clear(key);
      return NextResponse.json({
        success: true,
        message: `缓存 ${key} 已清除`,
        timestamp: new Date().toISOString()
      });
    } else {
      // 清除所有缓存
      sitemapCache.clearAll();
      return NextResponse.json({
        success: true,
        message: '所有 sitemap 缓存已清除',
        timestamp: new Date().toISOString()
      });
    }

  } catch (error) {
    console.error('Error clearing cache:', error);
    
    return NextResponse.json({
      success: false,
      message: '清除缓存失败',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    // 清理过期缓存
    sitemapCache.cleanup();
    
    const stats = sitemapCache.getStats();
    
    return NextResponse.json({
      success: true,
      message: '过期缓存已清理',
      stats,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error cleaning up cache:', error);
    
    return NextResponse.json({
      success: false,
      message: '清理缓存失败',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

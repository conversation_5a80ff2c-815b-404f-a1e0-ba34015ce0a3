import { NextRequest, NextResponse } from 'next/server';
import { SearchEngineSubmitter, defaultSearchEngineConfig } from '@/lib/search-engine-submission';
import { siteConfig } from '@/lib/seo';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { engines = ['ping'], sitemapUrl } = body;

    const finalSitemapUrl = sitemapUrl || `${siteConfig.url}/sitemap.xml`;
    
    // 创建提交器实例
    const submitter = new SearchEngineSubmitter(defaultSearchEngineConfig);
    
    const results = [];

    // 根据请求的引擎类型进行提交
    if (engines.includes('google') && defaultSearchEngineConfig.google?.apiKey) {
      const googleResult = await submitter.submitToGoogle(finalSitemapUrl);
      results.push({ engine: 'Google', ...googleResult });
    }

    if (engines.includes('bing') && defaultSearchEngineConfig.bing?.apiKey) {
      const bingResult = await submitter.submitToBing(finalSitemapUrl);
      results.push({ engine: 'Bing', ...bingResult });
    }

    // 如果请求 ping 或者没有配置 API
    if (engines.includes('ping') || results.length === 0) {
      const pingResults = await submitter.pingSearchEngines(finalSitemapUrl);
      pingResults.forEach((result, index) => {
        results.push({
          engine: index === 0 ? 'Google (Ping)' : 'Bing (Ping)',
          ...result
        });
      });
    }

    // 统计结果
    const successCount = results.filter(r => r.success).length;
    const totalCount = results.length;

    return NextResponse.json({
      success: successCount > 0,
      message: `提交完成: ${successCount}/${totalCount} 成功`,
      results,
      summary: {
        total: totalCount,
        successful: successCount,
        failed: totalCount - successCount,
        sitemapUrl: finalSitemapUrl
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Sitemap submission error:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Sitemap 提交失败',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const sitemapUrl = url.searchParams.get('sitemapUrl') || `${siteConfig.url}/sitemap.xml`;
    
    // 检查配置状态
    const configStatus = {
      google: {
        apiConfigured: !!defaultSearchEngineConfig.google?.apiKey,
        enabled: defaultSearchEngineConfig.google?.enabled || false
      },
      bing: {
        apiConfigured: !!defaultSearchEngineConfig.bing?.apiKey,
        enabled: defaultSearchEngineConfig.bing?.enabled || false
      },
      pingAvailable: true // ping 总是可用的
    };

    // 如果配置了 Google API，检查状态
    let googleStatus = null;
    if (configStatus.google.apiConfigured) {
      const submitter = new SearchEngineSubmitter(defaultSearchEngineConfig);
      googleStatus = await submitter.checkGoogleSubmissionStatus(sitemapUrl);
    }

    return NextResponse.json({
      success: true,
      config: configStatus,
      googleStatus,
      availableEngines: [
        ...(configStatus.google.apiConfigured ? ['google'] : []),
        ...(configStatus.bing.apiConfigured ? ['bing'] : []),
        'ping'
      ],
      sitemapUrl,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Submission status check error:', error);
    
    return NextResponse.json({
      success: false,
      message: '获取提交状态失败',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { productService, articleService, categoryService } from '@/lib/database';
import { siteConfig } from '@/lib/seo';
import { SitemapValidator } from '@/lib/sitemap-validator';
import sitemap from '@/app/sitemap';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { validateUrls = false, maxConcurrent = 5 } = body;

    const validator = new SitemapValidator();
    
    // 获取当前 sitemap 数据
    const sitemapData = await sitemap();
    
    // 1. 验证 sitemap 格式
    const formatValidation = validator.validateSitemapFormat(sitemapData);
    
    let urlValidation = null;
    
    // 2. 如果请求验证 URL 可访问性
    if (validateUrls && formatValidation.isValid) {
      const urls = sitemapData.map(entry => entry.url);
      const urlResults = await validator.validateUrlAccessibility(urls, {
        maxConcurrent,
        timeout: 8000
      });
      
      urlValidation = {
        results: urlResults,
        summary: {
          total: urlResults.length,
          accessible: urlResults.filter(r => r.isValid).length,
          inaccessible: urlResults.filter(r => !r.isValid).length,
          averageResponseTime: urlResults
            .filter(r => r.responseTime)
            .reduce((sum, r) => sum + (r.responseTime || 0), 0) / urlResults.length
        }
      };
    }

    // 3. 验证生成的 XML 格式
    const xmlResponse = await fetch(`${siteConfig.url}/sitemap.xml`);
    const xmlContent = await xmlResponse.text();
    const xmlValidation = validator.validateXmlFormat(xmlContent);

    return NextResponse.json({
      success: true,
      validation: {
        format: formatValidation,
        xml: xmlValidation,
        urls: urlValidation
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Sitemap validation error:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Sitemap 验证失败',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const checkUrls = url.searchParams.get('checkUrls') === 'true';
    
    const validator = new SitemapValidator();
    
    // 获取当前 sitemap 数据
    const sitemapData = await sitemap();
    
    // 基本格式验证
    const formatValidation = validator.validateSitemapFormat(sitemapData);
    
    // 快速统计信息
    const stats = {
      totalUrls: sitemapData.length,
      hasErrors: formatValidation.errors.length > 0,
      hasWarnings: formatValidation.warnings.length > 0,
      errorCount: formatValidation.errors.length,
      warningCount: formatValidation.warnings.length
    };

    return NextResponse.json({
      success: true,
      stats,
      validation: formatValidation,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Sitemap validation check error:', error);
    
    return NextResponse.json({
      success: false,
      message: '获取验证信息失败',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

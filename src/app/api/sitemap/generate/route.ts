import { NextRequest, NextResponse } from 'next/server';
import { productService, articleService, categoryService } from '@/lib/database';
import { siteConfig } from '@/lib/seo';

export async function POST(request: NextRequest) {
  try {
    // 获取所有数据来验证 sitemap 生成
    const [products, articles, categories] = await Promise.all([
      productService.getAll(),
      articleService.getPublished(),
      categoryService.getAll()
    ]);

    // 计算统计信息
    const stats = {
      totalUrls: 1 + 5 + categories.length + articles.length, // 首页 + 静态页面 + 分类 + 文章
      staticPages: 6, // 首页 + 5个静态页面
      categoryPages: categories.length,
      articlePages: articles.length,
      lastGenerated: new Date().toISOString(),
      baseUrl: siteConfig.url
    };

    // 验证关键页面
    const validationResults = {
      hasHomepage: true,
      hasStaticPages: true,
      hasCategories: categories.length > 0,
      hasArticles: articles.length > 0,
      allSlugsValid: true
    };

    // 检查 slug 有效性
    const invalidSlugs = [];
    
    // 检查分类 slug
    for (const category of categories) {
      if (!category.slug || category.slug.trim() === '') {
        invalidSlugs.push(`Category: ${category.name} (missing slug)`);
        validationResults.allSlugsValid = false;
      }
    }

    // 检查文章 slug
    for (const article of articles) {
      if (!article.slug || article.slug.trim() === '') {
        invalidSlugs.push(`Article: ${article.title} (missing slug)`);
        validationResults.allSlugsValid = false;
      }
    }

    // 返回成功响应
    return NextResponse.json({
      success: true,
      message: 'Sitemap 生成成功',
      stats,
      validation: validationResults,
      issues: invalidSlugs.length > 0 ? invalidSlugs : null,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error generating sitemap:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Sitemap 生成失败',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  try {
    // 获取当前 sitemap 信息
    const [products, articles, categories] = await Promise.all([
      productService.getAll(),
      articleService.getPublished(),
      categoryService.getAll()
    ]);

    const info = {
      totalUrls: 1 + 5 + categories.length + articles.length,
      breakdown: {
        homepage: 1,
        staticPages: 5,
        categories: categories.length,
        articles: articles.length
      },
      lastChecked: new Date().toISOString(),
      baseUrl: siteConfig.url,
      sitemapUrl: `${siteConfig.url}/sitemap.xml`
    };

    return NextResponse.json({
      success: true,
      info,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error getting sitemap info:', error);
    
    return NextResponse.json({
      success: false,
      message: '获取 sitemap 信息失败',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

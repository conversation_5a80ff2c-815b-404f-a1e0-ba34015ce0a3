import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import Link from 'next/link';
import { ExternalLink, Calendar, User } from 'lucide-react';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import SEOHead from '@/components/SEOHead';
import { ArticleCoverCard } from '@/components/ArticleCover';
import { categoryService, productService, articleService } from '@/lib/database';
import { generateCategorySEO } from '@/lib/seo';
import { getArticleCoverImage } from '@/lib/utils';
import { generateAmazonMexicoUrl } from '@/lib/asinUtils';

interface CategoryPageProps {
  params: Promise<{
    slug: string;
  }>;
}

// 生成静态参数
export async function generateStaticParams() {
  const categories = await categoryService.getAll();
  return categories.map((category) => ({
    slug: category.slug,
  }));
}

// 生成元数据
export async function generateMetadata({ params }: CategoryPageProps): Promise<Metadata> {
  const { slug } = await params;
  const category = await categoryService.getBySlug(slug);
  
  if (!category) {
    return {
      title: 'Categoría no encontrada',
    };
  }

  return generateCategorySEO(category.name, category.slug);
}

export default async function CategoryPage({ params }: CategoryPageProps) {
  const { slug } = await params;
  const category = await categoryService.getBySlug(slug);

  if (!category) {
    notFound();
  }

  // 获取该分类的产品和文章
  const [products, articles] = await Promise.all([
    productService.getByCategory(slug),
    articleService.getPublished()
  ]);

  // 过滤出与该分类相关的文章
  const categoryArticles = articles.filter(article =>
    article.product && article.product.category === slug
  );

  // 生成结构化数据
  const jsonLd = {
    '@context': 'https://schema.org',
    '@type': 'CollectionPage',
    name: `${category.name} - Tu Tienda Íntima`,
    description: category.description,
    url: `${process.env.NEXT_PUBLIC_SITE_URL}/categoria/${category.slug}`,
    mainEntity: {
      '@type': 'ItemList',
      numberOfItems: categoryArticles.length,
      itemListElement: categoryArticles.map((article, index) => ({
        '@type': 'ListItem',
        position: index + 1,
        item: {
          '@type': 'Article',
          headline: article.title,
          description: article.excerpt,
          url: `${process.env.NEXT_PUBLIC_SITE_URL}/articulo/${article.slug}`,
          author: {
            '@type': 'Organization',
            name: 'Tu Tienda Íntima'
          }
        }
      }))
    }
  };

  return (
    <div className="min-h-screen bg-white">
      <SEOHead jsonLd={jsonLd} />
      <Navbar />
      
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-pink-50 to-purple-50 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              {category.name}
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {category.description}
            </p>
          </div>
        </div>
      </section>

      {/* Articles Section */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {categoryArticles.length > 0 ? (
            <>
              <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">
                Artículos y Reseñas sobre {category.name}
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {categoryArticles.map((article) => (
                  <article
                    key={article.id}
                    className="group article-card bg-white/80 rounded-2xl shadow-sm border border-gray-100 overflow-hidden hover:border-pink-200 transition-all duration-300 hover:-translate-y-1"
                  >
                    {/* Article Cover */}
                    <div className="relative aspect-square overflow-hidden">
                      <ArticleCoverCard
                        src={getArticleCoverImage(article)}
                        alt={article.title}
                        className="w-full h-full group-hover:scale-105 transition-transform duration-300"
                      />
                      {article.product && (
                        <div className="absolute top-3 right-3 bg-white/90 backdrop-blur-sm rounded-full px-2.5 py-1 text-xs font-medium text-gray-700 shadow-sm">
                          Producto
                        </div>
                      )}
                    </div>

                    {/* Article Content */}
                    <div className="p-5">
                      {/* Meta Info */}
                      <div className="flex items-center gap-3 text-xs text-gray-500 mb-3">
                        <div className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          <time dateTime={article.publishedAt?.toISOString()}>
                            {article.publishedAt?.toLocaleDateString('es-MX', {
                              day: 'numeric',
                              month: 'short',
                              year: 'numeric'
                            })}
                          </time>
                        </div>
                        <div className="flex items-center gap-1">
                          <User className="h-3 w-3" />
                          <span>Tu Tienda Íntima</span>
                        </div>
                      </div>

                      {/* Title */}
                      <h3 className="text-lg font-bold text-gray-900 mb-2 line-clamp-2 group-hover:text-pink-600 transition-colors leading-tight">
                        <Link href={`/articulo/${article.slug}`} className="block">
                          {article.title}
                        </Link>
                      </h3>

                      {/* Excerpt */}
                      <p className="text-gray-600 text-sm mb-3 line-clamp-2 leading-relaxed">
                        {article.excerpt}
                      </p>

                      {/* Keywords */}
                      {article.keywords.length > 0 && (
                        <div className="flex flex-wrap gap-1 mb-4">
                          {article.keywords.slice(0, 2).map((keyword, index) => (
                            <span
                              key={index}
                              className="bg-pink-50 text-pink-700 px-2 py-0.5 rounded-full text-xs font-medium"
                            >
                              {keyword}
                            </span>
                          ))}
                          {article.keywords.length > 2 && (
                            <span className="text-gray-400 text-xs px-2 py-0.5">
                              +{article.keywords.length - 2}
                            </span>
                          )}
                        </div>
                      )}

                      {/* Product Info */}
                      {article.product && (
                        <div className="bg-gradient-to-r from-pink-50 to-purple-50 rounded-xl p-3 mb-4 border border-pink-100">
                          <div className="flex items-start justify-between gap-2">
                            <div className="flex-1 min-w-0">
                              <p className="font-semibold text-gray-900 text-sm line-clamp-1">
                                {article.product.name}
                              </p>
                              <p className="text-gray-600 text-xs capitalize mt-0.5">
                                {article.product.category.replace('-', ' ')}
                              </p>
                            </div>
                            <a
                              href={generateAmazonMexicoUrl(article.product.asin)}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="flex-shrink-0 bg-pink-600 hover:bg-pink-700 text-white px-3 py-1.5 rounded-lg text-xs font-medium transition-colors inline-flex items-center gap-1"
                            >
                              Amazon
                              <ExternalLink className="h-3 w-3" />
                            </a>
                          </div>
                        </div>
                      )}

                      {/* Read More Button */}
                      <Link
                        href={`/articulo/${article.slug}`}
                        className="inline-flex items-center gap-2 text-pink-600 hover:text-pink-700 font-semibold text-sm group/link"
                      >
                        Leer artículo completo
                        <ExternalLink className="h-4 w-4 group-hover/link:translate-x-0.5 transition-transform" />
                      </Link>
                    </div>
                  </article>
                ))}
              </div>
            </>
          ) : (
            <div className="text-center py-16">
              <div className="text-6xl mb-4">📝</div>
              <h2 className="text-2xl font-bold text-gray-900 mb-4">
                Próximamente más contenido
              </h2>
              <p className="text-gray-600 max-w-2xl mx-auto">
                Estamos trabajando en crear artículos informativos y reseñas detalladas sobre productos en la categoría {category.name}.
                Vuelve pronto para descubrir contenido útil y recomendaciones expertas.
              </p>
            </div>
          )}
        </div>
      </section>

      <Footer />
    </div>
  );
}

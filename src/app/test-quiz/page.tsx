'use client';

import { useState, useEffect } from 'react';
import { QuizConfig, QuizAnswer, QuizResult } from '@/types';
import { QuizEngine, QuizService } from '@/lib/quizEngine';
import { ProductGroupingService } from '@/lib/productGrouping';

export default function TestQuizPage() {
  const [quizConfig, setQuizConfig] = useState<QuizConfig | null>(null);
  const [answers, setAnswers] = useState<QuizAnswer[]>([]);
  const [result, setResult] = useState<QuizResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 创建一个模拟的测验配置
  useEffect(() => {
    const mockConfig: QuizConfig = {
      id: 'test-quiz',
      title: 'Test Quiz',
      description: 'A test quiz for development',
      questions: [
        {
          id: 'experience_level',
          text: '¿Cuál es tu nivel de experiencia?',
          type: 'single_choice',
          weight: 1.5,
          options: [
            {
              id: 'beginner',
              text: 'Soy completamente nuevo/a',
              value: 'beginner',
              emoji: '🌱',
              score: { experience: 1, intensity: 1 }
            },
            {
              id: 'intermediate',
              text: 'Tengo algo de experiencia',
              value: 'intermediate',
              emoji: '🌿',
              score: { experience: 2, intensity: 2 }
            },
            {
              id: 'advanced',
              text: 'Soy muy experimentado/a',
              value: 'advanced',
              emoji: '🌳',
              score: { experience: 3, intensity: 3 }
            }
          ]
        },
        {
          id: 'usage_type',
          text: '¿Prefieres usar productos solo/a o en pareja?',
          type: 'single_choice',
          weight: 1.2,
          options: [
            {
              id: 'solo',
              text: 'Solo/a',
              value: 'solo',
              emoji: '🧘',
              score: { social: 1 }
            },
            {
              id: 'couple',
              text: 'En pareja',
              value: 'couple',
              emoji: '💕',
              score: { social: 3 }
            },
            {
              id: 'both',
              text: 'Ambos',
              value: 'both',
              emoji: '🤝',
              score: { social: 2 }
            }
          ]
        }
      ],
      resultGroups: [
        {
          id: 'beginner_gentle',
          title: 'Perfecto para Comenzar',
          description: 'Productos suaves y fáciles de usar, ideales para principiantes.',
          emoji: '🌸',
          dimensionScores: {
            experience: [1, 1.5],
            intensity: [1, 1.5],
            social: [1, 3]
          },
          productCategories: ['vibradores', 'balas-vibradoras']
        },
        {
          id: 'intermediate_balanced',
          title: 'Experiencia Equilibrada',
          description: 'Productos versátiles para usuarios con experiencia intermedia.',
          emoji: '⚖️',
          dimensionScores: {
            experience: [1.5, 2.5],
            intensity: [1.5, 2.5],
            social: [1, 3]
          },
          productCategories: ['vibradores', 'masturbadores']
        }
      ],
      dimensions: ['experience', 'intensity', 'social'],
      active: true,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    setQuizConfig(mockConfig);
  }, []);

  const handleAnswer = (questionId: string, value: string) => {
    const newAnswers = [...answers];
    const existingIndex = newAnswers.findIndex(a => a.questionId === questionId);
    
    if (existingIndex >= 0) {
      newAnswers[existingIndex] = { questionId, value };
    } else {
      newAnswers.push({ questionId, value });
    }
    
    setAnswers(newAnswers);
  };

  const processQuiz = async () => {
    if (!quizConfig) return;

    try {
      setLoading(true);
      setError(null);

      const quizEngine = new QuizEngine(quizConfig);
      
      // 验证答案
      const validation = quizEngine.validateAnswers(answers);
      if (!validation.valid) {
        setError(`Missing answers for: ${validation.missingQuestions.join(', ')}`);
        return;
      }

      // 计算结果
      const quizResult = await quizEngine.processQuizCompletion(answers);
      if (!quizResult) {
        setError('Failed to process quiz results');
        return;
      }

      // 获取增强的产品推荐
      const enhancedProducts = await ProductGroupingService.getRecommendedProductsByQuizResult(
        quizResult,
        6
      );

      const finalResult: QuizResult = {
        ...quizResult,
        recommendedProducts: enhancedProducts.length > 0 ? enhancedProducts : quizResult.recommendedProducts
      };

      setResult(finalResult);

    } catch (err) {
      console.error('Error processing quiz:', err);
      setError('Error processing quiz results');
    } finally {
      setLoading(false);
    }
  };

  const reset = () => {
    setAnswers([]);
    setResult(null);
    setError(null);
  };

  if (!quizConfig) {
    return <div className="p-8">Loading quiz configuration...</div>;
  }

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Quiz System Test</h1>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            {error}
          </div>
        )}

        {result ? (
          <div className="bg-white rounded-lg shadow-lg p-8">
            <h2 className="text-2xl font-bold text-green-600 mb-4">
              {result.resultGroup.emoji} {result.resultGroup.title}
            </h2>
            <p className="text-gray-700 mb-6">{result.resultGroup.description}</p>
            
            <div className="mb-6">
              <h3 className="text-lg font-semibold mb-3">Dimension Scores:</h3>
              <div className="grid grid-cols-2 gap-4">
                {Object.entries(result.dimensionScores).map(([dimension, score]) => (
                  <div key={dimension} className="bg-gray-50 p-3 rounded">
                    <div className="font-medium">{dimension}</div>
                    <div className="text-blue-600 font-bold">{score.toFixed(2)}</div>
                  </div>
                ))}
              </div>
            </div>

            <div className="mb-6">
              <h3 className="text-lg font-semibold mb-3">
                Recommended Products ({result.recommendedProducts.length}):
              </h3>
              {result.recommendedProducts.length > 0 ? (
                <div className="space-y-3">
                  {result.recommendedProducts.map((product, index) => (
                    <div key={product.id} className="bg-gray-50 p-4 rounded">
                      <h4 className="font-medium">{product.name}</h4>
                      <p className="text-sm text-gray-600">Category: {product.category}</p>
                      <a 
                        href={product.amazonUrl} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:underline text-sm"
                      >
                        View on Amazon
                      </a>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-600">No products found</p>
              )}
            </div>

            <div className="mb-6">
              <h3 className="text-lg font-semibold mb-2">Confidence:</h3>
              <div className="bg-blue-100 text-blue-800 px-3 py-1 rounded inline-block">
                {(result.confidence * 100).toFixed(1)}%
              </div>
            </div>

            <button
              onClick={reset}
              className="bg-gray-600 text-white px-6 py-2 rounded hover:bg-gray-700"
            >
              Reset Quiz
            </button>
          </div>
        ) : (
          <div className="bg-white rounded-lg shadow-lg p-8">
            <h2 className="text-xl font-semibold mb-6">Answer the Questions:</h2>
            
            {quizConfig.questions.map((question) => (
              <div key={question.id} className="mb-8">
                <h3 className="text-lg font-medium mb-4">{question.text}</h3>
                <div className="space-y-2">
                  {question.options?.map((option) => (
                    <button
                      key={option.id}
                      onClick={() => handleAnswer(question.id, option.value)}
                      className={`w-full text-left p-4 rounded border-2 transition-all ${
                        answers.find(a => a.questionId === question.id)?.value === option.value
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <span className="text-2xl mr-3">{option.emoji}</span>
                      {option.text}
                    </button>
                  ))}
                </div>
              </div>
            ))}

            <div className="flex gap-4">
              <button
                onClick={processQuiz}
                disabled={loading || answers.length < quizConfig.questions.length}
                className="bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? 'Processing...' : 'Get Results'}
              </button>
              
              <button
                onClick={reset}
                className="bg-gray-600 text-white px-6 py-2 rounded hover:bg-gray-700"
              >
                Reset
              </button>
            </div>

            <div className="mt-4 text-sm text-gray-600">
              Answered: {answers.length} / {quizConfig.questions.length}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

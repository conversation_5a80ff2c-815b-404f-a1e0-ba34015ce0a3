'use client';

import { useState, useEffect } from 'react';
import { Database, FileText, BarChart3, Users, Shield, RefreshCw, Wifi } from 'lucide-react';
import Link from 'next/link';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import AdminPanel from '@/components/AdminPanel';
import { statsService } from '@/lib/database';
import { useRealtimeStats, RealtimeIndicator } from '@/lib/realtime';
import RealtimeActivityLog from '@/components/RealtimeActivityLog';

interface StatsData {
  totalProducts: number;
  totalArticles: number;
  publishedArticles: number;
  unpublishedArticles: number;
  totalCategories: number;
  productsByCategory: Array<{ category: string; count: number; slug: string }>;
  recentProducts: number;
  recentArticles: number;
  totalPageViews: number;
  totalAmazonClicks: number;
  uniqueVisitors: number;
  publishRate: string;
  avgProductsPerCategory: string;
  conversionRate: string;
}

export default function AdminPage() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [password, setPassword] = useState('');
  const [showAdminPanel, setShowAdminPanel] = useState(false);
  const [error, setError] = useState('');
  const [stats, setStats] = useState<StatsData | null>(null);
  const [loading, setLoading] = useState(false);
  const [dateRange, setDateRange] = useState<'7d' | '30d' | '90d' | 'all'>('30d');

  // Sitemap 相关状态
  const [sitemapLoading, setSitemapLoading] = useState(false);
  const [sitemapMessage, setSitemapMessage] = useState<string | null>(null);
  const [sitemapError, setSitemapError] = useState<string | null>(null);
  const [showSitemapInfo, setShowSitemapInfo] = useState(false);
  const [sitemapInfo, setSitemapInfo] = useState<any>(null);
  const [cacheStats, setCacheStats] = useState<any>(null);
  const [showCacheInfo, setShowCacheInfo] = useState(false);

  // Sitemap 验证相关状态
  const [validationLoading, setValidationLoading] = useState(false);
  const [validationResults, setValidationResults] = useState<any>(null);
  const [showValidationResults, setShowValidationResults] = useState(false);

  // 搜索引擎提交相关状态
  const [submissionLoading, setSubmissionLoading] = useState(false);
  const [submissionResults, setSubmissionResults] = useState<any>(null);
  const [showSubmissionResults, setShowSubmissionResults] = useState(false);

  // 实时数据
  const { stats: realtimeStats, isConnected: realtimeConnected, forceRefresh } = useRealtimeStats();

  // 获取日期范围
  const getDateRangeFilter = (range: '7d' | '30d' | '90d' | 'all') => {
    if (range === 'all') return undefined;

    const end = new Date();
    const start = new Date();

    switch (range) {
      case '7d':
        start.setDate(start.getDate() - 7);
        break;
      case '30d':
        start.setDate(start.getDate() - 30);
        break;
      case '90d':
        start.setDate(start.getDate() - 90);
        break;
    }

    return { start, end };
  };

  // 加载统计数据
  const loadStats = async () => {
    try {
      setLoading(true);
      const dateFilter = getDateRangeFilter(dateRange);
      const statsData = await statsService.getBasicStats(dateFilter);
      setStats(statsData);
    } catch (error) {
      console.error('Error loading stats:', error);
    } finally {
      setLoading(false);
    }
  };

  // 处理 sitemap 生成
  const handleGenerateSitemap = async () => {
    setSitemapLoading(true);
    setSitemapMessage(null);
    setSitemapError(null);

    try {
      const response = await fetch('/api/sitemap/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (data.success) {
        setSitemapMessage(`Sitemap 生成成功！包含 ${data.stats.totalUrls} 个 URL`);

        // 如果有验证问题，显示警告
        if (data.issues && data.issues.length > 0) {
          setSitemapError(`警告：发现 ${data.issues.length} 个问题：${data.issues.slice(0, 3).join(', ')}`);
        }
      } else {
        setSitemapError(data.message || 'Sitemap 生成失败');
      }
    } catch (error) {
      setSitemapError('网络错误，请稍后重试');
      console.error('Sitemap generation error:', error);
    } finally {
      setSitemapLoading(false);

      // 3秒后清除消息
      setTimeout(() => {
        setSitemapMessage(null);
        setSitemapError(null);
      }, 3000);
    }
  };

  // 查看 sitemap 信息
  const handleViewSitemap = async () => {
    try {
      const response = await fetch('/api/sitemap/generate', {
        method: 'GET',
      });

      const data = await response.json();

      if (data.success) {
        setSitemapInfo(data.info);
        setShowSitemapInfo(true);
      } else {
        setSitemapError('获取 sitemap 信息失败');
      }
    } catch (error) {
      setSitemapError('网络错误，请稍后重试');
      console.error('Sitemap info error:', error);
    }
  };

  // 获取缓存统计信息
  const handleViewCache = async () => {
    try {
      const response = await fetch('/api/sitemap/cache');
      const data = await response.json();

      if (data.success) {
        setCacheStats(data.stats);
        setShowCacheInfo(true);
      } else {
        setSitemapError('获取缓存信息失败');
      }
    } catch (error) {
      setSitemapError('网络错误，请稍后重试');
      console.error('Cache info error:', error);
    }
  };

  // 清除缓存
  const handleClearCache = async () => {
    try {
      const response = await fetch('/api/sitemap/cache', {
        method: 'DELETE',
      });

      const data = await response.json();

      if (data.success) {
        setSitemapMessage('缓存已清除');
        // 刷新缓存统计
        if (showCacheInfo) {
          handleViewCache();
        }
      } else {
        setSitemapError(data.message || '清除缓存失败');
      }
    } catch (error) {
      setSitemapError('网络错误，请稍后重试');
      console.error('Clear cache error:', error);
    }
  };

  // 验证 sitemap
  const handleValidateSitemap = async (validateUrls = false) => {
    setValidationLoading(true);
    setSitemapMessage(null);
    setSitemapError(null);

    try {
      const response = await fetch('/api/sitemap/validate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          validateUrls,
          maxConcurrent: 5
        })
      });

      const data = await response.json();

      if (data.success) {
        setValidationResults(data.validation);
        setShowValidationResults(true);

        const { format, xml, urls } = data.validation;
        let message = `验证完成！`;

        if (format.errors.length > 0) {
          setSitemapError(`发现 ${format.errors.length} 个错误`);
        } else if (format.warnings.length > 0) {
          setSitemapMessage(`${message} 发现 ${format.warnings.length} 个警告`);
        } else {
          setSitemapMessage(`${message} 格式正确`);
        }

        if (urls && urls.summary) {
          setSitemapMessage(prev =>
            `${prev || message} URL 检查: ${urls.summary.accessible}/${urls.summary.total} 可访问`
          );
        }
      } else {
        setSitemapError(data.message || '验证失败');
      }
    } catch (error) {
      setSitemapError('网络错误，请稍后重试');
      console.error('Validation error:', error);
    } finally {
      setValidationLoading(false);

      // 3秒后清除消息
      setTimeout(() => {
        setSitemapMessage(null);
        setSitemapError(null);
      }, 3000);
    }
  };

  // 提交到搜索引擎
  const handleSubmitToSearchEngines = async (engines = ['ping']) => {
    setSubmissionLoading(true);
    setSitemapMessage(null);
    setSitemapError(null);

    try {
      const response = await fetch('/api/sitemap/submit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          engines
        })
      });

      const data = await response.json();

      if (data.success) {
        setSubmissionResults(data);
        setShowSubmissionResults(true);
        setSitemapMessage(`提交完成: ${data.summary.successful}/${data.summary.total} 成功`);
      } else {
        setSitemapError(data.message || '提交失败');
      }
    } catch (error) {
      setSitemapError('网络错误，请稍后重试');
      console.error('Submission error:', error);
    } finally {
      setSubmissionLoading(false);

      // 3秒后清除消息
      setTimeout(() => {
        setSitemapMessage(null);
        setSitemapError(null);
      }, 3000);
    }
  };

  // 认证成功后加载数据
  useEffect(() => {
    if (isAuthenticated) {
      loadStats();
    }
  }, [isAuthenticated]);

  // 时间范围变化时重新加载数据
  useEffect(() => {
    if (isAuthenticated) {
      loadStats();
    }
  }, [dateRange]);

  // Simple password authentication (in production, use proper auth)
  const handleLogin = (e: React.FormEvent) => {
    e.preventDefault();
    // Simple password check (in production, use proper authentication)
    if (password === 'admin123') {
      setIsAuthenticated(true);
      setError('');
    } else {
      setError('密码错误');
    }
  };

  // 构建统计卡片数据
  const getStatsCards = () => {
    if (!stats) return [];

    // 如果有实时数据，优先使用实时数据更新访问量和转化率
    const displayStats = {
      ...stats,
      ...(realtimeStats && {
        totalPageViews: realtimeStats.totalPageViews,
        totalAmazonClicks: realtimeStats.totalAmazonClicks,
        uniqueVisitors: realtimeStats.uniqueVisitors,
        conversionRate: realtimeStats.conversionRate
      })
    };

    return [
      {
        icon: FileText,
        title: '文章',
        value: displayStats.publishedArticles.toString(),
        description: '已发布文章',
        subValue: `总共 ${displayStats.totalArticles} 篇`,
        isRealtime: false
      },
      {
        icon: Database,
        title: '产品',
        value: displayStats.totalProducts.toString(),
        description: '数据库中的产品',
        subValue: `${displayStats.totalCategories} 个分类`,
        isRealtime: false
      },
      {
        icon: BarChart3,
        title: '访问量',
        value: displayStats.totalPageViews.toLocaleString(),
        description: '页面访问量',
        subValue: `${displayStats.uniqueVisitors} 独立访客`,
        isRealtime: true,
        lastUpdated: realtimeStats?.lastUpdated
      },
      {
        icon: Users,
        title: '转化率',
        value: `${displayStats.conversionRate}%`,
        description: 'Amazon点击转化',
        subValue: `${displayStats.totalAmazonClicks} 次点击`,
        isRealtime: true,
        lastUpdated: realtimeStats?.lastUpdated
      }
    ];
  };

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8">
          <div className="text-center mb-8">
            <Shield className="h-12 w-12 text-pink-600 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-gray-900">管理后台</h1>
            <p className="text-gray-600">请输入密码继续</p>
          </div>
          
          <form onSubmit={handleLogin}>
            <div className="mb-4">
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
                密码
              </label>
              <input
                type="password"
                id="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent text-gray-900 placeholder-gray-500"
                placeholder="请输入密码"
                required
              />
            </div>
            
            {error && (
              <div className="mb-4 text-red-600 text-sm">
                {error}
              </div>
            )}
            
            <button
              type="submit"
              className="w-full bg-pink-600 hover:bg-pink-700 text-white py-2 px-4 rounded-lg font-medium transition-colors"
            >
              登录
            </button>
          </form>

          <div className="mt-6 text-center text-sm text-gray-500">
            <p>演示密码: admin123</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex justify-between items-center mb-4">
            <div>
              <div className="flex items-center gap-4 mb-2">
                <h1 className="text-3xl font-bold text-gray-900">
                  管理后台
                </h1>
                <RealtimeIndicator />
              </div>
              <p className="text-gray-600">
                管理您的内容并监控网站性能
              </p>
            </div>
            <div className="flex items-center gap-3">
              <button
                onClick={forceRefresh}
                disabled={loading}
                className="flex items-center gap-2 bg-green-600 hover:bg-green-700 disabled:bg-green-400 text-white px-3 py-2 rounded-lg font-medium transition-colors text-sm"
                title="强制刷新实时数据"
              >
                <Wifi className="h-4 w-4" />
                实时刷新
              </button>
              <button
                onClick={loadStats}
                disabled={loading}
                className="flex items-center gap-2 bg-pink-600 hover:bg-pink-700 disabled:bg-pink-400 text-white px-4 py-2 rounded-lg font-medium transition-colors"
              >
                <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                刷新数据
              </button>
            </div>
          </div>

          {/* 时间范围选择器 */}
          <div className="flex items-center gap-4">
            <span className="text-sm font-medium text-gray-700">时间范围:</span>
            <div className="flex bg-gray-100 rounded-lg p-1">
              {[
                { key: '7d', label: '最近7天' },
                { key: '30d', label: '最近30天' },
                { key: '90d', label: '最近90天' },
                { key: 'all', label: '全部时间' }
              ].map((option) => (
                <button
                  key={option.key}
                  onClick={() => setDateRange(option.key as '7d' | '30d' | '90d' | 'all')}
                  className={`px-3 py-1 text-sm font-medium rounded-md transition-colors ${
                    dateRange === option.key
                      ? 'bg-pink-600 text-white'
                      : 'text-gray-600 hover:text-pink-600'
                  }`}
                >
                  {option.label}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {loading ? (
            // 加载状态
            Array.from({ length: 4 }).map((_, index) => (
              <div key={index} className="bg-white rounded-lg shadow p-6 animate-pulse">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="h-8 w-8 bg-gray-200 rounded"></div>
                  </div>
                  <div className="ml-4 flex-1">
                    <div className="h-4 bg-gray-200 rounded w-16 mb-2"></div>
                    <div className="h-8 bg-gray-200 rounded w-12"></div>
                  </div>
                </div>
                <div className="mt-2 h-4 bg-gray-200 rounded w-24"></div>
              </div>
            ))
          ) : (
            getStatsCards().map((stat, index) => (
              <div key={index} className={`bg-white rounded-lg shadow p-6 ${stat.isRealtime ? 'border-l-4 border-green-500' : ''}`}>
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <stat.icon className="h-8 w-8 text-pink-600" />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-500">
                        {stat.title}
                      </p>
                      <p className="text-2xl font-bold text-gray-900">
                        {stat.value}
                      </p>
                    </div>
                  </div>
                  {stat.isRealtime && (
                    <div className="flex items-center gap-1 text-green-600">
                      <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                      <span className="text-xs">实时</span>
                    </div>
                  )}
                </div>
                <p className="mt-2 text-sm text-gray-600">
                  {stat.description}
                </p>
                {stat.subValue && (
                  <p className="mt-1 text-xs text-gray-500">
                    {stat.subValue}
                  </p>
                )}
                {stat.isRealtime && stat.lastUpdated && (
                  <p className="mt-1 text-xs text-green-600">
                    最后更新: {stat.lastUpdated.toLocaleTimeString()}
                  </p>
                )}
              </div>
            ))
          )}
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              内容管理
            </h3>
            <div className="space-y-3">
              <button
                onClick={() => setShowAdminPanel(true)}
                className="w-full bg-pink-600 hover:bg-pink-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
              >
                打开管理面板
              </button>
              <button className="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg font-medium transition-colors">
                导入产品
              </button>
              <button className="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg font-medium transition-colors">
                导出数据
              </button>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              SEO和分析
            </h3>
            <div className="space-y-3">
              <button
                onClick={handleGenerateSitemap}
                disabled={sitemapLoading}
                className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center justify-center gap-2"
              >
                {sitemapLoading ? (
                  <>
                    <RefreshCw className="h-4 w-4 animate-spin" />
                    生成中...
                  </>
                ) : (
                  '生成站点地图'
                )}
              </button>

              {/* Sitemap 状态消息 */}
              {sitemapMessage && (
                <div className="bg-green-50 border border-green-200 text-green-800 px-3 py-2 rounded-lg text-sm">
                  {sitemapMessage}
                </div>
              )}
              {sitemapError && (
                <div className="bg-red-50 border border-red-200 text-red-800 px-3 py-2 rounded-lg text-sm">
                  {sitemapError}
                </div>
              )}

              <button
                onClick={handleViewSitemap}
                className="w-full bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
              >
                查看站点地图
              </button>

              <button
                onClick={handleViewCache}
                className="w-full bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
              >
                查看缓存状态
              </button>

              <button
                onClick={handleClearCache}
                className="w-full bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
              >
                清除缓存
              </button>

              <button
                onClick={() => handleValidateSitemap(false)}
                disabled={validationLoading}
                className="w-full bg-indigo-600 hover:bg-indigo-700 disabled:bg-indigo-400 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center justify-center gap-2"
              >
                {validationLoading ? (
                  <>
                    <RefreshCw className="h-4 w-4 animate-spin" />
                    验证中...
                  </>
                ) : (
                  '验证格式'
                )}
              </button>

              <button
                onClick={() => handleValidateSitemap(true)}
                disabled={validationLoading}
                className="w-full bg-yellow-600 hover:bg-yellow-700 disabled:bg-yellow-400 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center justify-center gap-2"
              >
                {validationLoading ? (
                  <>
                    <RefreshCw className="h-4 w-4 animate-spin" />
                    检查中...
                  </>
                ) : (
                  '检查 URL'
                )}
              </button>

              <button
                onClick={() => handleSubmitToSearchEngines(['ping'])}
                disabled={submissionLoading}
                className="w-full bg-teal-600 hover:bg-teal-700 disabled:bg-teal-400 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center justify-center gap-2"
              >
                {submissionLoading ? (
                  <>
                    <RefreshCw className="h-4 w-4 animate-spin" />
                    提交中...
                  </>
                ) : (
                  '提交搜索引擎'
                )}
              </button>

              <Link
                href="/admin/analytics"
                className="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg font-medium transition-colors text-center block"
              >
                查看分析
              </Link>
              <button className="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg font-medium transition-colors">
                优化SEO
              </button>
            </div>
          </div>


        </div>

        {/* Recent Activity */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">
              最近活动
            </h3>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between py-3 border-b border-gray-100">
                <div>
                  <p className="font-medium text-gray-900">新文章已发布</p>
                  <p className="text-sm text-gray-500">评测: 经典粉色震动器</p>
                </div>
                <span className="text-sm text-gray-500">2小时前</span>
              </div>
              <div className="flex items-center justify-between py-3 border-b border-gray-100">
                <div>
                  <p className="font-medium text-gray-900">产品已更新</p>
                  <p className="text-sm text-gray-500">Amazon价格已更新</p>
                </div>
                <span className="text-sm text-gray-500">5小时前</span>
              </div>
              <div className="flex items-center justify-between py-3">
                <div>
                  <p className="font-medium text-gray-900">SEO optimizado</p>
                  <p className="text-sm text-gray-500">Sitemap regenerado automáticamente</p>
                </div>
                <span className="text-sm text-gray-500">Hace 1 día</span>
              </div>
            </div>
          </div>
        </div>

        {/* Real-time Activity Log */}
        <div className="mt-8">
          <RealtimeActivityLog maxEvents={10} />
        </div>
      </div>

      {/* Admin Panel Modal */}
      {showAdminPanel && (
        <AdminPanel onClose={() => setShowAdminPanel(false)} />
      )}

      {/* Sitemap Info Modal */}
      {showSitemapInfo && sitemapInfo && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto">
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold text-gray-900">
                  站点地图信息
                </h3>
                <button
                  onClick={() => setShowSitemapInfo(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>

            <div className="px-6 py-4">
              <div className="grid grid-cols-2 gap-4 mb-6">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">{sitemapInfo.totalUrls}</div>
                  <div className="text-sm text-blue-800">总 URL 数量</div>
                </div>
                <div className="bg-green-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">{sitemapInfo.breakdown.articles}</div>
                  <div className="text-sm text-green-800">文章页面</div>
                </div>
                <div className="bg-purple-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-purple-600">{sitemapInfo.breakdown.categories}</div>
                  <div className="text-sm text-purple-800">分类页面</div>
                </div>
                <div className="bg-orange-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-orange-600">{sitemapInfo.breakdown.staticPages}</div>
                  <div className="text-sm text-orange-800">静态页面</div>
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">基本信息</h4>
                  <div className="bg-gray-50 p-3 rounded text-sm">
                    <div><strong>基础 URL:</strong> {sitemapInfo.baseUrl}</div>
                    <div><strong>Sitemap URL:</strong> <a href={sitemapInfo.sitemapUrl} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">{sitemapInfo.sitemapUrl}</a></div>
                    <div><strong>最后检查:</strong> {new Date(sitemapInfo.lastChecked).toLocaleString('zh-CN')}</div>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium text-gray-900 mb-2">页面分布</h4>
                  <div className="bg-gray-50 p-3 rounded text-sm space-y-1">
                    <div>首页: {sitemapInfo.breakdown.homepage} 个</div>
                    <div>静态页面: {sitemapInfo.breakdown.staticPages} 个</div>
                    <div>分类页面: {sitemapInfo.breakdown.categories} 个</div>
                    <div>文章页面: {sitemapInfo.breakdown.articles} 个</div>
                  </div>
                </div>
              </div>
            </div>

            <div className="px-6 py-4 border-t border-gray-200 flex justify-end">
              <button
                onClick={() => setShowSitemapInfo(false)}
                className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
              >
                关闭
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Cache Info Modal */}
      {showCacheInfo && cacheStats && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto">
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold text-gray-900">
                  缓存状态信息
                </h3>
                <button
                  onClick={() => setShowCacheInfo(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>

            <div className="px-6 py-4">
              <div className="grid grid-cols-3 gap-4 mb-6">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">{cacheStats.totalEntries}</div>
                  <div className="text-sm text-blue-800">总缓存条目</div>
                </div>
                <div className="bg-green-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">{cacheStats.validEntries}</div>
                  <div className="text-sm text-green-800">有效缓存</div>
                </div>
                <div className="bg-red-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-red-600">{cacheStats.expiredEntries}</div>
                  <div className="text-sm text-red-800">过期缓存</div>
                </div>
              </div>

              {cacheStats.cacheKeys.length > 0 && (
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">缓存键列表</h4>
                  <div className="bg-gray-50 p-3 rounded text-sm max-h-40 overflow-y-auto">
                    {cacheStats.cacheKeys.map((key: string, index: number) => (
                      <div key={index} className="py-1 border-b border-gray-200 last:border-b-0">
                        {key}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            <div className="px-6 py-4 border-t border-gray-200 flex justify-between">
              <button
                onClick={handleClearCache}
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
              >
                清除所有缓存
              </button>
              <button
                onClick={() => setShowCacheInfo(false)}
                className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
              >
                关闭
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Validation Results Modal */}
      {showValidationResults && validationResults && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold text-gray-900">
                  Sitemap 验证结果
                </h3>
                <button
                  onClick={() => setShowValidationResults(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>

            <div className="px-6 py-4">
              {/* 格式验证结果 */}
              <div className="mb-6">
                <h4 className="font-medium text-gray-900 mb-3">格式验证</h4>
                <div className="grid grid-cols-4 gap-4 mb-4">
                  <div className="bg-blue-50 p-3 rounded-lg">
                    <div className="text-lg font-bold text-blue-600">{validationResults.format.stats.totalUrls}</div>
                    <div className="text-sm text-blue-800">总 URL</div>
                  </div>
                  <div className="bg-green-50 p-3 rounded-lg">
                    <div className="text-lg font-bold text-green-600">{validationResults.format.stats.validUrls}</div>
                    <div className="text-sm text-green-800">有效 URL</div>
                  </div>
                  <div className="bg-red-50 p-3 rounded-lg">
                    <div className="text-lg font-bold text-red-600">{validationResults.format.errors.length}</div>
                    <div className="text-sm text-red-800">错误</div>
                  </div>
                  <div className="bg-yellow-50 p-3 rounded-lg">
                    <div className="text-lg font-bold text-yellow-600">{validationResults.format.warnings.length}</div>
                    <div className="text-sm text-yellow-800">警告</div>
                  </div>
                </div>

                {validationResults.format.errors.length > 0 && (
                  <div className="mb-4">
                    <h5 className="font-medium text-red-900 mb-2">错误</h5>
                    <div className="bg-red-50 p-3 rounded text-sm max-h-32 overflow-y-auto">
                      {validationResults.format.errors.map((error: string, index: number) => (
                        <div key={index} className="py-1 text-red-800">{error}</div>
                      ))}
                    </div>
                  </div>
                )}

                {validationResults.format.warnings.length > 0 && (
                  <div className="mb-4">
                    <h5 className="font-medium text-yellow-900 mb-2">警告</h5>
                    <div className="bg-yellow-50 p-3 rounded text-sm max-h-32 overflow-y-auto">
                      {validationResults.format.warnings.map((warning: string, index: number) => (
                        <div key={index} className="py-1 text-yellow-800">{warning}</div>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* URL 验证结果 */}
              {validationResults.urls && (
                <div className="mb-6">
                  <h4 className="font-medium text-gray-900 mb-3">URL 可访问性检查</h4>
                  <div className="grid grid-cols-3 gap-4 mb-4">
                    <div className="bg-green-50 p-3 rounded-lg">
                      <div className="text-lg font-bold text-green-600">{validationResults.urls.summary.accessible}</div>
                      <div className="text-sm text-green-800">可访问</div>
                    </div>
                    <div className="bg-red-50 p-3 rounded-lg">
                      <div className="text-lg font-bold text-red-600">{validationResults.urls.summary.inaccessible}</div>
                      <div className="text-sm text-red-800">不可访问</div>
                    </div>
                    <div className="bg-blue-50 p-3 rounded-lg">
                      <div className="text-lg font-bold text-blue-600">{Math.round(validationResults.urls.summary.averageResponseTime)}ms</div>
                      <div className="text-sm text-blue-800">平均响应时间</div>
                    </div>
                  </div>

                  {validationResults.urls.results.filter((r: any) => !r.isValid).length > 0 && (
                    <div>
                      <h5 className="font-medium text-red-900 mb-2">不可访问的 URL</h5>
                      <div className="bg-red-50 p-3 rounded text-sm max-h-40 overflow-y-auto">
                        {validationResults.urls.results
                          .filter((result: any) => !result.isValid)
                          .map((result: any, index: number) => (
                            <div key={index} className="py-1 text-red-800">
                              <strong>{result.url}</strong> - {result.error || `状态码: ${result.statusCode}`}
                            </div>
                          ))}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* XML 验证结果 */}
              <div>
                <h4 className="font-medium text-gray-900 mb-3">XML 格式验证</h4>
                <div className={`p-3 rounded-lg ${validationResults.xml.isValid ? 'bg-green-50' : 'bg-red-50'}`}>
                  <div className={`font-medium ${validationResults.xml.isValid ? 'text-green-900' : 'text-red-900'}`}>
                    {validationResults.xml.isValid ? '✓ XML 格式正确' : '✗ XML 格式有问题'}
                  </div>
                  {validationResults.xml.errors.length > 0 && (
                    <div className="mt-2 text-sm text-red-800">
                      {validationResults.xml.errors.map((error: string, index: number) => (
                        <div key={index}>{error}</div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>

            <div className="px-6 py-4 border-t border-gray-200 flex justify-end">
              <button
                onClick={() => setShowValidationResults(false)}
                className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
              >
                关闭
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Submission Results Modal */}
      {showSubmissionResults && submissionResults && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-3xl w-full mx-4 max-h-[80vh] overflow-y-auto">
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold text-gray-900">
                  搜索引擎提交结果
                </h3>
                <button
                  onClick={() => setShowSubmissionResults(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>

            <div className="px-6 py-4">
              {/* 提交摘要 */}
              <div className="mb-6">
                <h4 className="font-medium text-gray-900 mb-3">提交摘要</h4>
                <div className="grid grid-cols-3 gap-4 mb-4">
                  <div className="bg-blue-50 p-3 rounded-lg">
                    <div className="text-lg font-bold text-blue-600">{submissionResults.summary.total}</div>
                    <div className="text-sm text-blue-800">总提交数</div>
                  </div>
                  <div className="bg-green-50 p-3 rounded-lg">
                    <div className="text-lg font-bold text-green-600">{submissionResults.summary.successful}</div>
                    <div className="text-sm text-green-800">成功</div>
                  </div>
                  <div className="bg-red-50 p-3 rounded-lg">
                    <div className="text-lg font-bold text-red-600">{submissionResults.summary.failed}</div>
                    <div className="text-sm text-red-800">失败</div>
                  </div>
                </div>

                <div className="bg-gray-50 p-3 rounded text-sm">
                  <strong>Sitemap URL:</strong> {submissionResults.summary.sitemapUrl}
                </div>
              </div>

              {/* 详细结果 */}
              <div>
                <h4 className="font-medium text-gray-900 mb-3">详细结果</h4>
                <div className="space-y-3">
                  {submissionResults.results.map((result: any, index: number) => (
                    <div key={index} className={`p-3 rounded-lg border ${result.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>
                      <div className="flex items-center justify-between mb-2">
                        <div className="font-medium">
                          {result.engine}
                        </div>
                        <div className={`text-sm px-2 py-1 rounded ${result.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                          {result.success ? '成功' : '失败'}
                        </div>
                      </div>
                      <div className="text-sm text-gray-600">
                        {result.message}
                      </div>
                      {result.details && (
                        <div className="mt-2 text-xs text-gray-500">
                          <strong>详情:</strong> {JSON.stringify(result.details, null, 2)}
                        </div>
                      )}
                      <div className="text-xs text-gray-400 mt-1">
                        {new Date(result.timestamp).toLocaleString('zh-CN')}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            <div className="px-6 py-4 border-t border-gray-200 flex justify-end">
              <button
                onClick={() => setShowSubmissionResults(false)}
                className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
              >
                关闭
              </button>
            </div>
          </div>
        </div>
      )}

      <Footer />
    </div>
  );
}

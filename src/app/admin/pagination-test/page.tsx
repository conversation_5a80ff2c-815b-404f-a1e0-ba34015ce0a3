'use client';

import { useState, useEffect } from 'react';
import { productService, categoryService } from '@/lib/database';
import { Product, Category } from '@/types';
import Pagination from '@/components/Pagination';
import SearchAndFilter from '@/components/SearchAndFilter';
import { usePaginatedData } from '@/hooks/usePagination';

export default function PaginationTestPage() {
  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<'products' | 'categories'>('products');

  // 分页状态
  const productPagination = usePaginatedData({
    initialPageSize: 5, // 小一点的页面大小用于测试
    initialPage: 1
  });

  const categoryPagination = usePaginatedData({
    initialPageSize: 5,
    initialPage: 1
  });

  // 加载产品数据
  const loadProducts = async () => {
    try {
      setLoading(true);
      const result = await productService.getPaginated(
        productPagination.paginationParams,
        productPagination.searchTerm,
        productPagination.filters.category
      );
      
      setProducts(result.data);
      productPagination.setTotalItems(result.pagination.totalItems);
    } catch (error) {
      console.error('Error loading products:', error);
    } finally {
      setLoading(false);
    }
  };

  // 加载分类数据
  const loadCategories = async () => {
    try {
      setLoading(true);
      const result = await categoryService.getPaginated(
        categoryPagination.paginationParams,
        categoryPagination.searchTerm
      );
      
      setCategories(result.data);
      categoryPagination.setTotalItems(result.pagination.totalItems);
    } catch (error) {
      console.error('Error loading categories:', error);
    } finally {
      setLoading(false);
    }
  };

  // 监听分页变化
  useEffect(() => {
    if (activeTab === 'products') {
      loadProducts();
    } else {
      loadCategories();
    }
  }, [
    activeTab,
    productPagination.currentPage,
    productPagination.pageSize,
    productPagination.searchTerm,
    productPagination.filters,
    categoryPagination.currentPage,
    categoryPagination.pageSize,
    categoryPagination.searchTerm
  ]);

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-lg">
          {/* 头部 */}
          <div className="border-b px-6 py-4">
            <h1 className="text-2xl font-bold text-gray-900">分页功能测试</h1>
            <p className="text-gray-600 mt-1">测试管理后台的分页、搜索和过滤功能</p>
          </div>

          {/* 标签页 */}
          <div className="border-b">
            <div className="flex">
              <button
                onClick={() => setActiveTab('products')}
                className={`px-6 py-3 font-medium ${
                  activeTab === 'products'
                    ? 'border-b-2 border-pink-600 text-pink-600'
                    : 'text-gray-600 hover:text-pink-600'
                }`}
              >
                产品测试 ({productPagination.totalItems})
              </button>
              <button
                onClick={() => setActiveTab('categories')}
                className={`px-6 py-3 font-medium ${
                  activeTab === 'categories'
                    ? 'border-b-2 border-pink-600 text-pink-600'
                    : 'text-gray-600 hover:text-pink-600'
                }`}
              >
                分类测试 ({categoryPagination.totalItems})
              </button>
            </div>
          </div>

          {/* 内容区域 */}
          <div className="p-6">
            {activeTab === 'products' && (
              <div>
                <h3 className="text-lg font-semibold mb-4">产品分页测试</h3>
                
                {/* 搜索和过滤 */}
                <SearchAndFilter
                  searchTerm={productPagination.searchTerm}
                  onSearchChange={productPagination.setSearchTerm}
                  filters={productPagination.filters}
                  onFilterChange={productPagination.setFilter}
                  onClearFilters={productPagination.clearFilters}
                  filterOptions={[
                    {
                      key: 'category',
                      label: '分类',
                      type: 'select',
                      options: [
                        { value: 'all', label: '全部分类' },
                        { value: 'vibrators', label: '震动器' },
                        { value: 'dildos', label: '假阳具' },
                        { value: 'anal-toys', label: '肛门玩具' },
                        { value: 'couples-toys', label: '情侣玩具' },
                        { value: 'male-toys', label: '男性玩具' },
                        { value: 'accessories', label: '配件' }
                      ]
                    }
                  ]}
                  placeholder="搜索产品..."
                  className="mb-6"
                />

                {/* 产品列表 */}
                <div className="space-y-4">
                  {loading ? (
                    <div className="flex items-center justify-center py-12">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-pink-600"></div>
                      <span className="ml-3">加载中...</span>
                    </div>
                  ) : products.length === 0 ? (
                    <div className="text-center py-12 text-gray-500">
                      {productPagination.isFiltered ? '没有找到匹配的产品' : '暂无产品数据'}
                    </div>
                  ) : (
                    products.map((product, index) => (
                      <div key={product.id} className="border rounded-lg p-4">
                        <div className="flex justify-between items-start">
                          <div>
                            <h4 className="font-semibold">{product.name}</h4>
                            <p className="text-sm text-gray-600">分类: {product.category}</p>
                            <p className="text-xs text-gray-500">
                              页面位置: 第{productPagination.currentPage}页 第{index + 1}项
                            </p>
                          </div>
                          <div className="text-sm text-gray-500">
                            ID: {product.id.slice(0, 8)}...
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>

                {/* 分页组件 */}
                {productPagination.totalItems > 0 && (
                  <div className="mt-6">
                    <Pagination
                      currentPage={productPagination.currentPage}
                      totalPages={productPagination.paginationInfo.totalPages}
                      totalItems={productPagination.totalItems}
                      itemsPerPage={productPagination.pageSize}
                      onPageChange={productPagination.setPage}
                      onSizeChange={productPagination.setPageSize}
                      pageSizeOptions={[5, 10, 20, 50]}
                      showTotal={true}
                      showSizeChanger={true}
                      showQuickJumper={true}
                    />
                  </div>
                )}
              </div>
            )}

            {activeTab === 'categories' && (
              <div>
                <h3 className="text-lg font-semibold mb-4">分类分页测试</h3>
                
                {/* 搜索 */}
                <SearchAndFilter
                  searchTerm={categoryPagination.searchTerm}
                  onSearchChange={categoryPagination.setSearchTerm}
                  placeholder="搜索分类..."
                  showFilterButton={false}
                  className="mb-6"
                />

                {/* 分类列表 */}
                <div className="space-y-4">
                  {loading ? (
                    <div className="flex items-center justify-center py-12">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-pink-600"></div>
                      <span className="ml-3">加载中...</span>
                    </div>
                  ) : categories.length === 0 ? (
                    <div className="text-center py-12 text-gray-500">
                      {categoryPagination.isFiltered ? '没有找到匹配的分类' : '暂无分类数据'}
                    </div>
                  ) : (
                    categories.map((category, index) => (
                      <div key={category.id} className="border rounded-lg p-4">
                        <div className="flex justify-between items-start">
                          <div>
                            <h4 className="font-semibold">{category.name}</h4>
                            <p className="text-sm text-gray-600">URL: /{category.slug}</p>
                            <p className="text-xs text-gray-500">
                              页面位置: 第{categoryPagination.currentPage}页 第{index + 1}项
                            </p>
                          </div>
                          <div className="text-sm text-gray-500">
                            ID: {category.id.slice(0, 8)}...
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>

                {/* 分页组件 */}
                {categoryPagination.totalItems > 0 && (
                  <div className="mt-6">
                    <Pagination
                      currentPage={categoryPagination.currentPage}
                      totalPages={categoryPagination.paginationInfo.totalPages}
                      totalItems={categoryPagination.totalItems}
                      itemsPerPage={categoryPagination.pageSize}
                      onPageChange={categoryPagination.setPage}
                      onSizeChange={categoryPagination.setPageSize}
                      pageSizeOptions={[5, 10, 20, 50]}
                      showTotal={true}
                      showSizeChanger={true}
                      showQuickJumper={true}
                    />
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

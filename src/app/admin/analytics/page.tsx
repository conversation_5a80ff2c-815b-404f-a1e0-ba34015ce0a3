'use client';

import { useState, useEffect } from 'react';
import { ArrowLeft, RefreshCw, Download, Calendar } from 'lucide-react';
import Link from 'next/link';
import { AnalyticsDashboard } from '@/components/Charts';
import { statsService } from '@/lib/database';

interface ChartData {
  timeTrend: Array<{
    date: string;
    products: number;
    articles: number;
  }>;
  categoryDistribution: Array<{
    category: string;
    count: number;
    slug: string;
  }>;
  topPages: Array<{
    page: string;
    views: number;
    title?: string;
  }>;
  deviceBreakdown: Array<{
    device: string;
    count: number;
    percentage: string;
  }>;
  conversionTrend: Array<{
    date: string;
    pageViews: number;
    amazonClicks: number;
    conversionRate: number;
  }>;
}

export default function AnalyticsPage() {
  const [chartData, setChartData] = useState<ChartData | null>(null);
  const [loading, setLoading] = useState(true);
  const [dateRange, setDateRange] = useState<'7d' | '30d' | '90d' | 'all'>('30d');

  // 获取日期范围
  const getDateRangeFilter = (range: '7d' | '30d' | '90d' | 'all') => {
    if (range === 'all') return undefined;
    
    const end = new Date();
    const start = new Date();
    
    switch (range) {
      case '7d':
        start.setDate(start.getDate() - 7);
        break;
      case '30d':
        start.setDate(start.getDate() - 30);
        break;
      case '90d':
        start.setDate(start.getDate() - 90);
        break;
    }
    
    return { start, end };
  };

  // 加载图表数据
  const loadChartData = async () => {
    try {
      setLoading(true);
      const dateFilter = getDateRangeFilter(dateRange);
      const data = await statsService.getChartData(dateFilter);
      setChartData(data);
    } catch (error) {
      console.error('Error loading chart data:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadChartData();
  }, [dateRange]);

  // 导出数据功能
  const handleExportData = () => {
    if (!chartData) return;
    
    const dataToExport = {
      exportDate: new Date().toISOString(),
      dateRange,
      data: chartData
    };
    
    const blob = new Blob([JSON.stringify(dataToExport, null, 2)], {
      type: 'application/json'
    });
    
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `analytics-data-${dateRange}-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Link
                href="/admin"
                className="mr-4 p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
              >
                <ArrowLeft className="h-5 w-5" />
              </Link>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">数据分析</h1>
                <p className="text-sm text-gray-600">网站访问和用户行为分析</p>
              </div>
            </div>
            
            <div className="flex items-center gap-4">
              {/* 时间范围选择器 */}
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-gray-500" />
                <span className="text-sm font-medium text-gray-700">时间范围:</span>
                <div className="flex bg-gray-100 rounded-lg p-1">
                  {[
                    { key: '7d', label: '7天' },
                    { key: '30d', label: '30天' },
                    { key: '90d', label: '90天' },
                    { key: 'all', label: '全部' }
                  ].map((option) => (
                    <button
                      key={option.key}
                      onClick={() => setDateRange(option.key as '7d' | '30d' | '90d' | 'all')}
                      className={`px-3 py-1 text-sm font-medium rounded-md transition-colors ${
                        dateRange === option.key
                          ? 'bg-pink-600 text-white'
                          : 'text-gray-600 hover:text-pink-600'
                      }`}
                    >
                      {option.label}
                    </button>
                  ))}
                </div>
              </div>
              
              {/* 操作按钮 */}
              <button
                onClick={loadChartData}
                disabled={loading}
                className="flex items-center gap-2 bg-pink-600 hover:bg-pink-700 disabled:bg-pink-400 text-white px-4 py-2 rounded-lg font-medium transition-colors"
              >
                <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                刷新
              </button>
              
              <button
                onClick={handleExportData}
                disabled={!chartData}
                className="flex items-center gap-2 bg-gray-600 hover:bg-gray-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg font-medium transition-colors"
              >
                <Download className="h-4 w-4" />
                导出
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {loading ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <RefreshCw className="h-8 w-8 text-pink-600 animate-spin mx-auto mb-4" />
              <p className="text-gray-600">加载分析数据中...</p>
            </div>
          </div>
        ) : chartData ? (
          <AnalyticsDashboard data={chartData} />
        ) : (
          <div className="text-center py-12">
            <div className="text-gray-500 mb-4">
              <Calendar className="h-12 w-12 mx-auto mb-4" />
              <h3 className="text-lg font-medium">暂无数据</h3>
              <p className="text-sm">当前时间范围内没有可用的分析数据</p>
            </div>
            <button
              onClick={loadChartData}
              className="bg-pink-600 hover:bg-pink-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
            >
              重新加载
            </button>
          </div>
        )}
      </div>
    </div>
  );
}

import { NextResponse } from 'next/server';
import { productService, articleService } from '@/lib/database';
import { siteConfig } from '@/lib/seo';

export async function GET() {
  try {
    // 获取所有数据
    const [products, articles] = await Promise.all([
      productService.getAll(),
      articleService.getPublished()
    ]);

    const baseUrl = siteConfig.url;
    const currentDate = new Date().toISOString();

    let sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:image="http://www.google.com/schemas/sitemap-image/1.1">`;

    // 为每篇文章添加图片信息
    articles.forEach(article => {
      const product = products.find(p => p.id === article.productId);
      
      if (product && (product.images?.length > 0 || article.coverImage)) {
        sitemap += `
  <url>
    <loc>${baseUrl}/articulo/${article.slug}</loc>
    <lastmod>${article.updatedAt || article.publishedAt || article.createdAt}</lastmod>`;

        // 添加文章封面图片
        if (article.coverImage) {
          sitemap += `
    <image:image>
      <image:loc>${article.coverImage}</image:loc>
      <image:title>${article.title}</image:title>
      <image:caption>${article.excerpt || article.title}</image:caption>
    </image:image>`;
        }

        // 添加产品图片
        if (product.images && product.images.length > 0) {
          product.images.forEach((imageUrl, index) => {
            sitemap += `
    <image:image>
      <image:loc>${imageUrl}</image:loc>
      <image:title>${product.name} - Imagen ${index + 1}</image:title>
      <image:caption>Imagen del producto ${product.name}</image:caption>
    </image:image>`;
          });
        }

        sitemap += `
  </url>`;
      }
    });

    sitemap += `
</urlset>`;

    return new NextResponse(sitemap, {
      status: 200,
      headers: {
        'Content-Type': 'application/xml',
        'Cache-Control': 'public, max-age=3600, s-maxage=3600'
      }
    });

  } catch (error) {
    console.error('Error generating image sitemap:', error);
    return new NextResponse('Error generating image sitemap', { status: 500 });
  }
}

import Link from 'next/link';
import { Star, Shield, Truck, Users, Award } from 'lucide-react';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import { FadeIn, SlideUp, ScaleUp } from '@/components/ScrollReveal';

export default function Home() {
  const productSeries = [
    {
      name: 'Serie de Iniciación Suave',
      description: 'Ideal para principiantes, experiencia suave.',
      emoji: '🌸',
      href: '/categoria/principiantes',
      amazonUrl: 'https://amazon.com/placeholder-1',
    },
    {
      name: 'Serie para Compartir en Pareja',
      description: 'Fortalece la relación, placer para dos.',
      emoji: '💕',
      href: '/categoria/parejas',
      amazonUrl: 'https://amazon.com/placeholder-2',
    },
    {
      name: 'Serie de Experiencia Premium',
      description: 'Calidad superior, disfrute de lujo.',
      emoji: '✨',
      href: '/categoria/premium',
      amazonUrl: 'https://amazon.com/placeholder-3',
    },
  ];

  const guides = [
    {
      title: 'Guía para Principiantes: Cómo Empezar Tu Viaje de Placer',
      description: 'Desde cero, te explicamos los puntos clave y precauciones.',
      emoji: '📚',
      href: '/guias/principiantes',
    },
    {
      title: 'Ciencia de Materiales: Cómo Elegir Productos Seguros',
      description: 'Análisis profundo de materiales para que compres con confianza.',
      emoji: '🔬',
      href: '/guias/materiales',
    },
    {
      title: 'Interacción en Pareja: Cómo Mejorar la Intimidad',
      description: 'Consejos profesionales para una relación más íntima y armoniosa.',
      emoji: '💑',
      href: '/guias/parejas',
    },
  ];

  const trustFeatures = [
    {
      icon: Shield,
      title: 'Privacidad y Seguridad',
      description: 'Cifrado de extremo a extremo para proteger tu privacidad.',
      emoji: '🔒',
    },
    {
      icon: Award,
      title: 'Selección de Alta Calidad',
      description: 'Productos de calidad rigurosamente seleccionados.',
      emoji: '⭐',
    },
    {
      icon: Users,
      title: 'Amigable para Principiantes',
      description: 'Guía detallada para un inicio fácil.',
      emoji: '🤝',
    },
  ];

  return (
    <div className="min-h-screen bg-white">
      <Navbar />

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-pink-50 to-purple-50 py-20 relative overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
          <div className="text-center">
            <FadeIn delay={100}>
              <div className="flex justify-center items-center mb-6">
                <div className="text-6xl mr-4">✨</div>
                <div className="text-2xl font-medium text-pink-600">Bienvenido a un Nuevo Mundo de Placer</div>
                <div className="text-6xl ml-4">✨</div>
              </div>
            </FadeIn>
            <SlideUp delay={200}>
              <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
                Tu Asesor de{' '}
                <span className="bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent">
                  Placer Personal
                </span>
              </h1>
            </SlideUp>
            <SlideUp delay={300}>
              <p className="text-xl text-gray-600 mb-4 max-w-3xl mx-auto">
                ¿No sabes por dónde empezar? ¡Prueba nuestro divertido quiz!
              </p>
            </SlideUp>
            <SlideUp delay={400}>
              <p className="text-lg text-gray-500 mb-8 max-w-2xl mx-auto">
                Recomendaciones personalizadas para que elegir sea fácil y divertido.
              </p>
            </SlideUp>
            <ScaleUp delay={500}>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link
                  href="/quiz"
                  className="bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-700 hover:to-purple-700 text-white px-8 py-4 rounded-xl font-medium transition-all transform hover:scale-105 flex items-center justify-center space-x-2 shadow-lg"
                >
                  <span>🎯</span>
                  <span>Iniciar Quiz Divertido</span>
                  <span>✨</span>
                </Link>
                <Link
                  href="/categoria/vibradores"
                  className="border-2 border-pink-600 text-pink-600 hover:bg-pink-600 hover:text-white px-8 py-4 rounded-xl font-medium transition-all transform hover:scale-105"
                >
                  Ver Productos Directamente
                </Link>
              </div>
            </ScaleUp>
          </div>
        </div>
      </section>

      {/* Product Recommendations */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <SlideUp>
            <div className="text-center mb-16">
              <div className="flex justify-center items-center mb-4">
                <div className="text-4xl mr-3">✨</div>
                <div className="text-lg font-medium text-pink-600">Seleccionado con Cuidado para Ti</div>
              </div>
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Recomendaciones Destacadas
              </h2>
              <p className="text-lg text-gray-600">
                Productos de alta calidad seleccionados cuidadosamente para ti, cada uno rigurosamente evaluado.
              </p>
            </div>
          </SlideUp>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {productSeries.map((series, index) => (
              <ScaleUp key={series.href} delay={100 + index * 150}>
                <div className="group bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all transform hover:scale-105">
                  <div className="h-48 bg-gradient-to-br from-pink-100 to-purple-100 flex items-center justify-center relative">
                    <div className="text-8xl">{series.emoji}</div>
                    <div className="absolute top-4 right-4">
                      <div className="bg-white rounded-full p-2 shadow-md">
                        <Star className="h-5 w-5 text-yellow-500 fill-current" />
                      </div>
                    </div>
                  </div>
                  <div className="p-6">
                    <h3 className="text-xl font-semibold text-gray-900 mb-2 group-hover:text-pink-600 transition-colors">
                      {series.name}
                    </h3>
                    <p className="text-gray-600 mb-4">
                      {series.description}
                    </p>
                    <Link
                      href={series.amazonUrl}
                      className="inline-flex items-center space-x-2 bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white px-4 py-2 rounded-lg font-medium transition-all transform hover:scale-105"
                    >
                      <span>Ver Detalles en Amazon</span>
                      <Truck className="h-4 w-4" />
                    </Link>
                  </div>
                </div>
              </ScaleUp>
            ))}
          </div>
        </div>
      </section>

      {/* Buying Guides Section */}
      <section className="bg-gray-50 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <SlideUp>
            <div className="text-center mb-16">
              <div className="flex justify-center items-center mb-4">
                <div className="text-4xl mr-3">📚</div>
                <div className="text-lg font-medium text-pink-600">Guía Profesional</div>
              </div>
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Guías de Compra
              </h2>
              <p className="text-lg text-gray-600">
                Consejos de compra profesionales y guías de uso detalladas para que cada elección sea segura.
              </p>
            </div>
          </SlideUp>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {guides.map((guide, index) => (
              <SlideUp key={guide.href} delay={100 + index * 150}>
                <div className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all transform hover:scale-105">
                  <div className="h-48 bg-gradient-to-br from-blue-100 to-indigo-100 flex items-center justify-center">
                    <div className="text-8xl">{guide.emoji}</div>
                  </div>
                  <div className="p-6">
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">
                      {guide.title}
                    </h3>
                    <p className="text-gray-600 mb-4">
                      {guide.description}
                    </p>
                    <Link
                      href={guide.href}
                      className="inline-flex items-center space-x-2 bg-pink-600 hover:bg-pink-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
                    >
                      <span>Leer Más</span>
                      <span>→</span>
                    </Link>
                  </div>
                </div>
              </SlideUp>
            ))}
          </div>
        </div>
      </section>

      {/* Trust Building Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <SlideUp>
            <div className="text-center mb-16">
              <div className="flex justify-center items-center mb-4">
                <div className="text-4xl mr-3">🛡️</div>
                <div className="text-lg font-medium text-pink-600">Digno de Confianza</div>
              </div>
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                ¿Por Qué Elegirnos?
              </h2>
              <p className="text-lg text-gray-600">
                Servimos a cada usuario con dedicación para que la experiencia sea segura, simple y maravillosa.
              </p>
            </div>
          </SlideUp>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {trustFeatures.map((feature, index) => (
              <FadeIn key={index} delay={200 + index * 150}>
                <div className="text-center">
                  <div className="bg-pink-100 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <div className="text-3xl">{feature.emoji}</div>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600">
                    {feature.description}
                  </p>
                </div>
              </FadeIn>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-gradient-to-br from-pink-50 to-purple-50 py-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <SlideUp>
            <h3 className="text-3xl font-bold text-gray-900 mb-4">
              ¿Listo para empezar tu viaje de placer?
            </h3>
          </SlideUp>
          <SlideUp delay={150}>
            <p className="text-lg text-gray-600 mb-8">
              Deja que nuestro quiz te ayude a encontrar el producto perfecto y comienza tu experiencia exclusiva.
            </p>
          </SlideUp>
          <ScaleUp delay={300}>
            <Link
              href="/quiz"
              className="inline-flex items-center space-x-3 bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-700 hover:to-purple-700 text-white px-8 py-4 rounded-xl font-medium transition-all transform hover:scale-105 shadow-lg"
            >
              <span>🎯</span>
              <span>Comenzar el Quiz Ahora</span>
              <span>✨</span>
            </Link>
          </ScaleUp>
        </div>
      </section>

      <Footer />
    </div>
  );
}

import { MetadataRoute } from 'next';
import { productService, articleService, categoryService } from '@/lib/database';
import { siteConfig } from '@/lib/seo';
import { sitemapCache, generateCacheKey, getContentLastModified } from '@/lib/sitemap-cache';

// 支持大型站点地图分割的函数
export async function generateSitemaps(): Promise<Array<{ id: string | number }>> {
  try {
    const [products, articles, categories] = await Promise.all([
      productService.getAll(),
      articleService.getPublished(),
      categoryService.getAll()
    ]);

    const sitemaps = [];

    // 主站点地图（首页和静态页面）
    sitemaps.push({ id: 'main' });

    // 如果分类数量超过 1000，分割分类站点地图
    if (categories.length > 1000) {
      const categoryChunks = Math.ceil(categories.length / 1000);
      for (let i = 0; i < categoryChunks; i++) {
        sitemaps.push({ id: `categories-${i}` });
      }
    } else {
      sitemaps.push({ id: 'categories' });
    }

    // 如果文章数量超过 1000，分割文章站点地图
    if (articles.length > 1000) {
      const articleChunks = Math.ceil(articles.length / 1000);
      for (let i = 0; i < articleChunks; i++) {
        sitemaps.push({ id: `articles-${i}` });
      }
    } else {
      sitemaps.push({ id: 'articles' });
    }

    return sitemaps;
  } catch (error) {
    console.error('Error generating sitemap list:', error);
    return [{ id: 'main' }];
  }
}

export default async function sitemap(params?: { id?: string }): Promise<MetadataRoute.Sitemap> {
  const baseUrl = siteConfig.url;
  const currentDate = new Date();
  const sitemapId = params?.id || 'main';

  try {
    // 生成缓存键
    const cacheKey = generateCacheKey('main', { id: sitemapId });

    // 并行获取所有数据
    const [products, articles, categories] = await Promise.all([
      productService.getAll(),
      articleService.getPublished(),
      categoryService.getAll()
    ]);

    // 检查缓存是否需要更新
    const contentLastModified = getContentLastModified(products, articles, categories);

    // 尝试从缓存获取数据
    if (!sitemapCache.shouldUpdate(cacheKey, contentLastModified)) {
      const cachedData = sitemapCache.get(cacheKey);
      if (cachedData) {
        console.log(`Using cached sitemap ${sitemapId}`);
        return cachedData;
      }
    }

    console.log(`Generating fresh sitemap ${sitemapId}`);
    const sitemapEntries: MetadataRoute.Sitemap = [];

    // 根据 sitemapId 生成不同的内容
    if (sitemapId === 'main') {
      // 主站点地图：首页和静态页面
      const latestArticleDate = articles.length > 0
        ? new Date(Math.max(...articles.map(a => new Date(a.updatedAt || a.publishedAt || a.createdAt || currentDate).getTime())))
        : currentDate;

      sitemapEntries.push({
        url: baseUrl,
        lastModified: latestArticleDate,
        changeFrequency: 'daily',
        priority: 1.0,
        alternates: {
          languages: {
            'es-MX': baseUrl,
            'es': baseUrl,
          }
        }
      });

      // 静态页面
      const staticPages = [
        { path: '/blog', changeFreq: 'daily' as const, priority: 0.9, lastMod: latestArticleDate },
        { path: '/guias', changeFreq: 'weekly' as const, priority: 0.8, lastMod: currentDate },
        { path: '/quiz', changeFreq: 'monthly' as const, priority: 0.7, lastMod: currentDate },
        { path: '/sobre-nosotros', changeFreq: 'monthly' as const, priority: 0.6, lastMod: new Date('2024-01-01') },
        { path: '/privacidad', changeFreq: 'yearly' as const, priority: 0.3, lastMod: new Date('2024-01-01') },
      ];

      staticPages.forEach(page => {
        sitemapEntries.push({
          url: `${baseUrl}${page.path}`,
          lastModified: page.lastMod,
          changeFrequency: page.changeFreq,
          priority: page.priority,
          alternates: {
            languages: {
              'es-MX': `${baseUrl}${page.path}`,
              'es': `${baseUrl}${page.path}`,
            }
          }
        });
      });
    } else if (sitemapId === 'categories' || sitemapId.startsWith('categories-')) {
      // 分类站点地图
      let categoriesToProcess = categories;

      if (sitemapId.startsWith('categories-')) {
        const chunkIndex = parseInt(sitemapId.split('-')[1]);
        const chunkSize = 1000;
        const startIndex = chunkIndex * chunkSize;
        const endIndex = startIndex + chunkSize;
        categoriesToProcess = categories.slice(startIndex, endIndex);
      }

      categoriesToProcess.forEach(category => {
        // 找到该分类下最新的文章更新时间
        const categoryArticles = articles.filter(article => {
          const product = products.find(p => p.id === article.productId);
          return product && product.category === category.name;
        });

        const categoryLastModified = categoryArticles.length > 0
          ? new Date(Math.max(...categoryArticles.map(a => new Date(a.updatedAt || a.publishedAt || a.createdAt || currentDate).getTime())))
          : category.createdAt || currentDate;

        sitemapEntries.push({
          url: `${baseUrl}/categoria/${category.slug}`,
          lastModified: categoryLastModified,
          changeFrequency: 'weekly',
          priority: 0.8,
          alternates: {
            languages: {
              'es-MX': `${baseUrl}/categoria/${category.slug}`,
              'es': `${baseUrl}/categoria/${category.slug}`,
            }
          }
        });
      });
    } else if (sitemapId === 'articles' || sitemapId.startsWith('articles-')) {
      // 文章站点地图
      let articlesToProcess = articles;

      if (sitemapId.startsWith('articles-')) {
        const chunkIndex = parseInt(sitemapId.split('-')[1]);
        const chunkSize = 1000;
        const startIndex = chunkIndex * chunkSize;
        const endIndex = startIndex + chunkSize;
        articlesToProcess = articles.slice(startIndex, endIndex);
      }

      articlesToProcess.forEach(article => {
        const lastModified = new Date(article.updatedAt || article.publishedAt || article.createdAt || currentDate);

        // 根据文章新鲜度调整优先级
        const daysSinceUpdate = Math.floor((currentDate.getTime() - lastModified.getTime()) / (1000 * 60 * 60 * 24));
        let priority = 0.7;

        if (daysSinceUpdate <= 7) {
          priority = 0.9; // 最近一周的文章
        } else if (daysSinceUpdate <= 30) {
          priority = 0.8; // 最近一个月的文章
        } else if (daysSinceUpdate <= 90) {
          priority = 0.7; // 最近三个月的文章
        } else {
          priority = 0.6; // 较旧的文章
        }

        sitemapEntries.push({
          url: `${baseUrl}/articulo/${article.slug}`,
          lastModified,
          changeFrequency: 'monthly',
          priority,
          alternates: {
            languages: {
              'es-MX': `${baseUrl}/articulo/${article.slug}`,
              'es': `${baseUrl}/articulo/${article.slug}`,
            }
          }
        });
      });
    }

    // 按优先级排序，确保重要页面在前面
    sitemapEntries.sort((a, b) => (b.priority || 0) - (a.priority || 0));

    // 缓存生成的数据
    sitemapCache.set(cacheKey, sitemapEntries, contentLastModified);

    console.log(`Generated sitemap ${sitemapId} with ${sitemapEntries.length} URLs`);
    return sitemapEntries;

  } catch (error) {
    console.error('Error generating sitemap:', error);

    // 返回基础的 sitemap，至少包含首页和主要静态页面
    return [
      {
        url: baseUrl,
        lastModified: currentDate,
        changeFrequency: 'daily',
        priority: 1.0,
      },
      {
        url: `${baseUrl}/blog`,
        lastModified: currentDate,
        changeFrequency: 'daily',
        priority: 0.9,
      },
      {
        url: `${baseUrl}/guias`,
        lastModified: currentDate,
        changeFrequency: 'weekly',
        priority: 0.8,
      }
    ];
  }
}

// 导出 revalidate 配置，设置为 1 小时重新验证
export const revalidate = 3600;

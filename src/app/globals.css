@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Video Player Slider Styles */
.slider {
  -webkit-appearance: none;
  appearance: none;
  background: transparent;
  cursor: pointer;
}

.slider::-webkit-slider-track {
  background: rgba(255, 255, 255, 0.3);
  height: 4px;
  border-radius: 2px;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  background: #ec4899;
  height: 12px;
  width: 12px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
}

.slider::-webkit-slider-thumb:hover {
  background: #db2777;
  transform: scale(1.2);
}

.slider::-moz-range-track {
  background: rgba(255, 255, 255, 0.3);
  height: 4px;
  border-radius: 2px;
  border: none;
}

.slider::-moz-range-thumb {
  background: #ec4899;
  height: 12px;
  width: 12px;
  border-radius: 50%;
  cursor: pointer;
  border: none;
  transition: all 0.2s ease;
}

.slider::-moz-range-thumb:hover {
  background: #db2777;
  transform: scale(1.2);
}

/* Scroll Reveal Animations */
@media (prefers-reduced-motion: no-preference) {
  /* Custom duration classes for scroll reveal */
  .duration-150 { transition-duration: 150ms; }
  .duration-200 { transition-duration: 200ms; }
  .duration-300 { transition-duration: 300ms; }
  .duration-400 { transition-duration: 400ms; }
  .duration-500 { transition-duration: 500ms; }
  .duration-600 { transition-duration: 600ms; }
  .duration-700 { transition-duration: 700ms; }
  .duration-800 { transition-duration: 800ms; }
  .duration-900 { transition-duration: 900ms; }
  .duration-1000 { transition-duration: 1000ms; }

  /* Custom delay classes */
  .delay-100 { transition-delay: 100ms; }
  .delay-200 { transition-delay: 200ms; }
  .delay-300 { transition-delay: 300ms; }
  .delay-400 { transition-delay: 400ms; }
  .delay-500 { transition-delay: 500ms; }
  .delay-600 { transition-delay: 600ms; }
  .delay-700 { transition-delay: 700ms; }
  .delay-800 { transition-delay: 800ms; }
  .delay-900 { transition-delay: 900ms; }
  .delay-1000 { transition-delay: 1000ms; }

  /* Smooth easing for scroll animations */
  .scroll-reveal {
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Enhanced transform animations */
  .scroll-reveal-transform {
    will-change: transform, opacity;
  }
}

/* Respect reduced motion preference */
@media (prefers-reduced-motion: reduce) {
  .scroll-reveal,
  .scroll-reveal-transform {
    transition: none !important;
    animation: none !important;
    transform: none !important;
  }
}

/* 管理系统输入框样式优化 */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
input[type="url"],
input[type="search"],
textarea,
select {
  color: #1f2937 !important; /* 深灰色文本，确保可读性 */
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,
input[type="number"]:focus,
input[type="url"]:focus,
input[type="search"]:focus,
textarea:focus,
select:focus {
  color: #111827 !important; /* 聚焦时更深的颜色 */
}

/* 占位符文本颜色 */
input::placeholder,
textarea::placeholder {
  color: #6b7280 !important; /* 中等灰色，与输入文本形成对比 */
  opacity: 1;
}

/* 禁用状态的输入框 */
input:disabled,
textarea:disabled,
select:disabled {
  color: #9ca3af !important; /* 禁用时的灰色 */
  background-color: #f9fafb !important;
}

/* 深色模式下的输入框样式 */
@media (prefers-color-scheme: dark) {
  input[type="text"],
  input[type="email"],
  input[type="password"],
  input[type="number"],
  input[type="url"],
  input[type="search"],
  textarea,
  select {
    color: #f9fafb !important;
    background-color: #374151 !important;
    border-color: #4b5563 !important;
  }

  input[type="text"]:focus,
  input[type="email"]:focus,
  input[type="password"]:focus,
  input[type="number"]:focus,
  input[type="url"]:focus,
  input[type="search"]:focus,
  textarea:focus,
  select:focus {
    color: #ffffff !important;
    background-color: #374151 !important;
    border-color: #ec4899 !important;
  }

  input::placeholder,
  textarea::placeholder {
    color: #9ca3af !important;
  }

  input:disabled,
  textarea:disabled,
  select:disabled {
    color: #6b7280 !important;
    background-color: #1f2937 !important;
  }
}

/* 文章卡片增强样式 */
.article-card {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.article-card:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* 图片加载动画 */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* 渐变文本效果 */
.gradient-text {
  background: linear-gradient(135deg, #ec4899, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 自定义滚动条 */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #ec4899, #8b5cf6);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #db2777, #7c3aed);
}

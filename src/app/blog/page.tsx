import { Metadata } from 'next';
import Link from 'next/link';
import { Calendar, User, ArrowRight, Star } from 'lucide-react';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import SEOHead from '@/components/SEOHead';
import { ArticleCoverCard } from '@/components/ArticleCover';
import { articleService } from '@/lib/database';
import { generateSEO } from '@/lib/seo';
import { getArticleCoverImage } from '@/lib/utils';
import { generateAmazonMexicoUrl } from '@/lib/asinUtils';

export const metadata: Metadata = generateSEO({
  title: 'Blog - Guías y Reseñas de Productos Íntimos',
  description: 'Descubre nuestros artículos sobre productos íntimos, guías de compra, reseñas detalladas y consejos para tu bienestar personal.',
  keywords: ['blog', 'artículos', 'guías', 'reseñas', 'productos íntimos', 'bienestar', 'consejos'],
  canonical: '/blog'
});

export default async function BlogPage() {
  const articles = await articleService.getPublished();

  // Generar datos estructurados
  const jsonLd = {
    '@context': 'https://schema.org',
    '@type': 'Blog',
    name: 'Blog - Tu Tienda Íntima',
    description: 'Artículos, guías y reseñas sobre productos íntimos y bienestar personal',
    url: `${process.env.NEXT_PUBLIC_SITE_URL}/blog`,
    blogPost: articles.map(article => ({
      '@type': 'BlogPosting',
      headline: article.title,
      description: article.excerpt,
      datePublished: article.publishedAt,
      author: {
        '@type': 'Organization',
        name: 'Tu Tienda Íntima'
      },
      url: `${process.env.NEXT_PUBLIC_SITE_URL}/articulo/${article.slug}`
    }))
  };

  return (
    <div className="min-h-screen bg-white">
      <SEOHead jsonLd={jsonLd} />
      <Navbar />
      
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-pink-50 to-purple-50 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              Nuestro <span className="text-pink-600">Blog</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              Descubre guías completas, reseñas honestas y consejos expertos 
              para tomar las mejores decisiones sobre tu bienestar íntimo.
            </p>
          </div>
        </div>
      </section>

      {/* Articles Grid */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {articles.length === 0 ? (
            <div className="text-center py-16">
              <div className="text-6xl mb-4">📝</div>
              <h2 className="text-2xl font-bold text-gray-900 mb-4">
                Próximamente nuevos artículos
              </h2>
              <p className="text-gray-600">
                Estamos trabajando en contenido increíble para ti. ¡Vuelve pronto!
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {articles.map((article) => (
                <article
                  key={article.id}
                  className="group article-card bg-white/80 rounded-2xl shadow-sm border border-gray-100 overflow-hidden hover:border-pink-200 transition-all duration-300 hover:-translate-y-1"
                >
                  {/* Article Cover */}
                  <div className="relative aspect-square overflow-hidden">
                    <ArticleCoverCard
                      src={getArticleCoverImage(article)}
                      alt={article.title}
                      className="w-full h-full group-hover:scale-105 transition-transform duration-300"
                    />
                    {article.product && (
                      <div className="absolute top-3 right-3 bg-white/90 backdrop-blur-sm rounded-full px-2.5 py-1 text-xs font-medium text-gray-700 shadow-sm">
                        Producto
                      </div>
                    )}
                  </div>

                  {/* Article Content */}
                  <div className="p-5">
                    {/* Meta Info */}
                    <div className="flex items-center gap-3 text-xs text-gray-500 mb-3">
                      <div className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        <time dateTime={article.publishedAt?.toISOString()}>
                          {article.publishedAt?.toLocaleDateString('es-MX', {
                            day: 'numeric',
                            month: 'short',
                            year: 'numeric'
                          })}
                        </time>
                      </div>
                      <div className="flex items-center gap-1">
                        <User className="h-3 w-3" />
                        <span>Tu Tienda Íntima</span>
                      </div>
                    </div>

                    {/* Title */}
                    <h2 className="text-lg font-bold text-gray-900 mb-2 line-clamp-2 group-hover:text-pink-600 transition-colors leading-tight">
                      <Link href={`/articulo/${article.slug}`} className="block">
                        {article.title}
                      </Link>
                    </h2>

                    {/* Excerpt */}
                    <p className="text-gray-600 text-sm mb-3 line-clamp-2 leading-relaxed">
                      {article.excerpt}
                    </p>

                    {/* Keywords */}
                    {article.keywords.length > 0 && (
                      <div className="flex flex-wrap gap-1 mb-4">
                        {article.keywords.slice(0, 2).map((keyword, index) => (
                          <span
                            key={index}
                            className="bg-pink-50 text-pink-700 px-2 py-0.5 rounded-full text-xs font-medium"
                          >
                            {keyword}
                          </span>
                        ))}
                        {article.keywords.length > 2 && (
                          <span className="text-gray-400 text-xs px-2 py-0.5">
                            +{article.keywords.length - 2}
                          </span>
                        )}
                      </div>
                    )}

                    {/* Product Info */}
                    {article.product && (
                      <div className="bg-gradient-to-r from-pink-50 to-purple-50 rounded-xl p-3 mb-4 border border-pink-100">
                        <div className="flex items-start justify-between gap-2">
                          <div className="flex-1 min-w-0">
                            <p className="font-semibold text-gray-900 text-sm line-clamp-1">
                              {article.product.name}
                            </p>
                            <p className="text-gray-600 text-xs capitalize mt-0.5">
                              {article.product.category.replace('-', ' ')}
                            </p>
                          </div>
                          <a
                            href={generateAmazonMexicoUrl(article.product.asin)}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex-shrink-0 bg-pink-600 hover:bg-pink-700 text-white px-3 py-1.5 rounded-lg text-xs font-medium transition-colors inline-flex items-center gap-1"
                          >
                            Amazon
                            <ArrowRight className="h-3 w-3" />
                          </a>
                        </div>
                      </div>
                    )}

                    {/* Read More Button */}
                    <Link
                      href={`/articulo/${article.slug}`}
                      className="inline-flex items-center gap-2 text-pink-600 hover:text-pink-700 font-semibold text-sm group/link"
                    >
                      Leer artículo completo
                      <ArrowRight className="h-4 w-4 group-hover/link:translate-x-0.5 transition-transform" />
                    </Link>
                  </div>
                </article>
              ))}
            </div>
          )}
        </div>
      </section>

      {/* Newsletter Section */}
      <section className="bg-gray-50 py-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            ¿Quieres estar al día?
          </h2>
          <p className="text-lg text-gray-600 mb-8">
            Recibe nuestros últimos artículos y guías directamente en tu correo.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
            <input
              type="email"
              placeholder="Tu correo electrónico"
              className="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
            />
            <button className="bg-pink-600 hover:bg-pink-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
              Suscribirse
            </button>
          </div>
          <p className="text-sm text-gray-500 mt-4">
            No spam. Solo contenido de calidad. Cancela cuando quieras.
          </p>
        </div>
      </section>

      <Footer />
    </div>
  );
}

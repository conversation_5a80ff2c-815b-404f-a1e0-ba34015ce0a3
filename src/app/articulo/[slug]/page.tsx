import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { Calendar, User } from 'lucide-react';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import SEOHead from '@/components/SEOHead';
import ContentWarning, { ProductWarning, LegalDisclaimer } from '@/components/ContentWarning';
import MarkdownRenderer from '@/components/MarkdownRenderer';
import { ArticleCoverHero } from '@/components/ArticleCover';
import RelatedArticles from '@/components/RelatedArticles';
import ProductImageGallery from '@/components/ProductImageGallery';
import { PlyrVideoPlayerHero } from '@/components/PlyrVideoPlayer';
import { articleService } from '@/lib/database';
import { generateArticleSEO, generateProductJsonLd } from '@/lib/seo';
import { getArticleCoverImage } from '@/lib/utils';

interface ArticlePageProps {
  params: Promise<{
    slug: string;
  }>;
}

// 生成元数据
export async function generateMetadata({ params }: ArticlePageProps): Promise<Metadata> {
  const { slug } = await params;
  const article = await articleService.getBySlug(slug);
  
  if (!article) {
    return {
      title: 'Artículo no encontrado',
    };
  }

  return generateArticleSEO(article.title, article.excerpt, article.keywords, article.slug);
}

export default async function ArticlePage({ params }: ArticlePageProps) {
  const { slug } = await params;
  const article = await articleService.getBySlug(slug);

  if (!article) {
    notFound();
  }

  // 获取相关文章
  const relatedArticles = await articleService.getRelatedArticles(article.id, 3);

  // 生成结构化数据
  const jsonLd = article.product
    ? generateProductJsonLd(article.product, article)
    : {
        '@context': 'https://schema.org',
        '@type': 'Article',
        headline: article.title,
        description: article.excerpt,
        author: {
          '@type': 'Organization',
          name: 'Tu Tienda Íntima'
        },
        publisher: {
          '@type': 'Organization',
          name: 'Tu Tienda Íntima'
        },
        datePublished: article.publishedAt,
        dateModified: article.updatedAt,
        // 添加视频结构化数据
        ...(article.videoUrl && {
          video: {
            '@type': 'VideoObject',
            name: `${article.title} - Video de demostración`,
            description: article.excerpt,
            contentUrl: article.videoUrl,
            thumbnailUrl: getArticleCoverImage(article),
            uploadDate: article.publishedAt
          }
        })
      };

  return (
    <div className="min-h-screen bg-white">
      <SEOHead jsonLd={jsonLd} />
      <Navbar />

      {/* Content Warning */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 pt-8">
        <ContentWarning />
      </div>

      {/* Main Content Area */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="lg:grid lg:grid-cols-3 lg:gap-8">
          {/* Article Content - 2/3 width on desktop */}
          <article className="lg:col-span-2">
        {/* Article Header */}
        <header className="mb-8">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            {article.title}
          </h1>
          
          <div className="flex items-center gap-6 text-gray-600 mb-6">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              <time dateTime={article.publishedAt?.toISOString()}>
                {article.publishedAt?.toLocaleDateString('es-MX', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}
              </time>
            </div>
            <div className="flex items-center gap-2">
              <User className="h-4 w-4" />
              <span>Tu Tienda Íntima</span>
            </div>
          </div>

          <p className="text-xl text-gray-600 leading-relaxed mb-8">
            {article.excerpt}
          </p>

          {/* Article Cover Image */}
          {getArticleCoverImage(article) && (
            <div className="mb-8">
              <ArticleCoverHero
                src={getArticleCoverImage(article)}
                alt={article.title}
                className="w-full"
              />
            </div>
          )}

          {/* Product Video */}
          {article.videoUrl && (
            <div className="mb-8">
              <PlyrVideoPlayerHero
                src={article.videoUrl}
                poster={getArticleCoverImage(article)}
                title={`${article.title} - Video de demostración`}
                className="w-full"
              />
            </div>
          )}
        </header>



        {/* Article Content */}
        <MarkdownRenderer
          content={article.content}
          className="mb-8"
        />



        {/* Product Warning */}
        {article.product && (
          <ProductWarning
            productType={article.product.category}
            className="mt-8"
          />
        )}

        {/* Keywords */}
        {article.keywords.length > 0 && (
          <div className="mt-8 pt-8 border-t border-gray-200">
            <h4 className="text-lg font-semibold text-gray-900 mb-3">
              Temas relacionados:
            </h4>
            <div className="flex flex-wrap gap-2">
              {article.keywords.map((keyword, index) => (
                <span
                  key={index}
                  className="bg-pink-100 text-pink-800 px-3 py-1 rounded-full text-sm"
                >
                  {keyword}
                </span>
              ))}
            </div>
          </div>
        )}



            {/* Legal Disclaimer */}
            <LegalDisclaimer className="mt-12" />
          </article>

          {/* Sidebar - 1/3 width on desktop, hidden on mobile */}
          {article.product && (
            <aside className="hidden lg:block lg:col-span-1">
              <ProductImageGallery
                product={article.product}
                sticky={true}
                showProductInfo={true}
              />
            </aside>
          )}
        </div>

        {/* Mobile Product Card - visible only on mobile when sidebar is hidden */}
        {article.product && (
          <div className="lg:hidden mt-8">
            <ProductImageGallery
              product={article.product}
              sticky={false}
              showProductInfo={true}
              className="mx-auto max-w-md"
            />
          </div>
        )}
      </div>

      {/* Related Articles */}
      {relatedArticles.length > 0 && (
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <RelatedArticles articles={relatedArticles} />
        </div>
      )}

      <Footer />
    </div>
  );
}

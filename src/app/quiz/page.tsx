'use client';

import { useState, useEffect } from 'react';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import QuestionContainer, {
  ProgressIndicator,
  QuestionNavigation,
  QuizHeader
} from '@/components/quiz/QuestionContainer';
import QuizResults from '@/components/quiz/QuizResults';
import {
  QuizConfig,
  QuizAnswer,
  QuizResult,
  QuizSession
} from '@/types';
import { QuizEngine, QuizService } from '@/lib/quizEngine';
import { ProductGroupingService } from '@/lib/productGrouping';
import { QuizStorage } from '@/lib/quizStorage';
import QuizErrorBoundary, { useErrorHandler } from '@/components/quiz/QuizErrorBoundary';

export default function QuizPage() {
  // State management
  const [quizConfig, setQuizConfig] = useState<QuizConfig | null>(null);
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [answers, setAnswers] = useState<QuizAnswer[]>([]);
  const [showResult, setShowResult] = useState(false);
  const [result, setResult] = useState<QuizResult | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [quizEngine, setQuizEngine] = useState<QuizEngine | null>(null);
  const { handleError } = useErrorHandler();

  // Load quiz configuration on component mount
  useEffect(() => {
    loadQuizConfig();
  }, []);

  const loadQuizConfig = async () => {
    try {
      setLoading(true);
      const config = await QuizService.getActiveQuizConfig();

      if (!config) {
        setError('No hay configuración de quiz disponible');
        return;
      }

      setQuizConfig(config);
      setQuizEngine(new QuizEngine(config));

      // Load saved answers from localStorage
      const savedAnswers = QuizStorage.loadAnswers(config.id);
      if (savedAnswers.length > 0) {
        setAnswers(savedAnswers);
        // 找到最后回答的问题
        const lastAnsweredIndex = config.questions.findIndex(q =>
          !savedAnswers.find(a => a.questionId === q.id)
        );
        if (lastAnsweredIndex > 0) {
          setCurrentQuestion(lastAnsweredIndex);
        }
      }

      // Create quiz session
      const sessionId = await QuizService.createQuizSession(
        config.id,
        generateSessionId()
      );
      setSessionId(sessionId);

    } catch (err) {
      console.error('Error loading quiz config:', err);
      handleError(err as Error);
      setError('Error al cargar el quiz');
    } finally {
      setLoading(false);
    }
  };

  const generateSessionId = () => {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  };

  const handleAnswer = (answer: QuizAnswer) => {
    const newAnswers = [...answers];
    const existingIndex = newAnswers.findIndex(a => a.questionId === answer.questionId);

    if (existingIndex >= 0) {
      newAnswers[existingIndex] = answer;
    } else {
      newAnswers.push(answer);
    }

    setAnswers(newAnswers);

    // Save progress to database and localStorage
    if (sessionId) {
      QuizService.updateQuizSession(sessionId, newAnswers);
    }

    if (quizConfig) {
      QuizStorage.saveAnswers(quizConfig.id, newAnswers);
    }
  };

  const handleNext = async () => {
    if (!quizConfig || !quizEngine) return;

    if (currentQuestion < quizConfig.questions.length - 1) {
      setCurrentQuestion(currentQuestion + 1);
    } else {
      // Process quiz completion
      await processQuizCompletion();
    }
  };

  const handlePrevious = () => {
    if (currentQuestion > 0) {
      setCurrentQuestion(currentQuestion - 1);
    }
  };

  const processQuizCompletion = async () => {
    if (!quizEngine || !sessionId) return;

    try {
      setLoading(true);

      // Validate answers
      const validation = quizEngine.validateAnswers(answers);
      if (!validation.valid) {
        setError(`Faltan respuestas para las preguntas: ${validation.missingQuestions.join(', ')}`);
        return;
      }

      // Calculate result
      const quizResult = await quizEngine.processQuizCompletion(answers);
      if (!quizResult) {
        setError('Error al procesar los resultados del quiz');
        return;
      }

      // Get enhanced product recommendations
      const enhancedProducts = await ProductGroupingService.getRecommendedProductsByQuizResult(
        quizResult,
        6
      );

      const finalResult: QuizResult = {
        ...quizResult,
        recommendedProducts: enhancedProducts.length > 0 ? enhancedProducts : quizResult.recommendedProducts
      };

      // Save final result to database and localStorage
      await QuizService.updateQuizSession(sessionId, answers, finalResult);

      if (quizConfig) {
        QuizStorage.saveResult(quizConfig.id, finalResult);
        QuizStorage.clearAnswers(quizConfig.id); // 清除进度，因为已完成
      }

      setResult(finalResult);
      setShowResult(true);

    } catch (err) {
      console.error('Error processing quiz completion:', err);
      handleError(err as Error);
      setError('Error al procesar los resultados');
    } finally {
      setLoading(false);
    }
  };

  const resetQuiz = () => {
    setCurrentQuestion(0);
    setAnswers([]);
    setShowResult(false);
    setResult(null);
    setError(null);

    // Clear localStorage
    if (quizConfig) {
      QuizStorage.clearAllQuizData(quizConfig.id);
    }

    loadQuizConfig(); // Reload config and create new session
  };

  const goToQuestion = (questionIndex: number) => {
    if (questionIndex >= 0 && questionIndex < (quizConfig?.questions.length || 0)) {
      setCurrentQuestion(questionIndex);
    }
  };

  const getCurrentAnswer = () => {
    if (!quizConfig) return undefined;
    const questionId = quizConfig.questions[currentQuestion]?.id;
    return answers.find(a => a.questionId === questionId);
  };

  const getAnsweredQuestions = () => {
    if (!quizConfig) return new Set<number>();
    const answeredQuestionIds = new Set(answers.map(a => a.questionId));
    const answeredIndices = new Set<number>();

    quizConfig.questions.forEach((question, index) => {
      if (answeredQuestionIds.has(question.id)) {
        answeredIndices.add(index);
      }
    });

    return answeredIndices;
  };

  const canGoNext = () => {
    if (!quizConfig) return false;
    const currentQuestionObj = quizConfig.questions[currentQuestion];
    const currentAnswer = getCurrentAnswer();

    // If question is not required, can always go next
    if (currentQuestionObj?.required === false) return true;

    // Otherwise, need an answer
    return !!currentAnswer;
  };

  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-white">
        <Navbar />
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-pink-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Cargando quiz...</p>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen bg-white">
        <Navbar />
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-center max-w-md">
            <div className="text-6xl mb-4">😞</div>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              Oops, algo salió mal
            </h2>
            <p className="text-gray-600 mb-6">{error}</p>
            <button
              onClick={() => {
                setError(null);
                loadQuizConfig();
              }}
              className="bg-pink-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-pink-700 transition-all"
            >
              Intentar de nuevo
            </button>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  // Results state
  if (showResult && result) {
    return (
      <div className="min-h-screen bg-white">
        <Navbar />
        <QuizResults
          result={result}
          onRetakeQuiz={resetQuiz}
          onShareResult={() => {
            // TODO: Implement share functionality
            console.log('Share result');
          }}
        />
        <Footer />
      </div>
    );
  }

  // Quiz not loaded
  if (!quizConfig) {
    return (
      <div className="min-h-screen bg-white">
        <Navbar />
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-center">
            <div className="text-6xl mb-4">🤔</div>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              Quiz no disponible
            </h2>
            <p className="text-gray-600">
              No se pudo cargar la configuración del quiz
            </p>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  // Main quiz interface
  const answeredQuestions = getAnsweredQuestions();
  const currentQuestionObj = quizConfig.questions[currentQuestion];

  return (
    <QuizErrorBoundary>
      <div className="min-h-screen bg-white">
        <Navbar />

      <section className="py-20 bg-gradient-to-br from-pink-50 to-purple-50">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Quiz Header */}
          <QuizHeader
            title={quizConfig.title}
            description={quizConfig.description}
            currentQuestion={currentQuestion}
            totalQuestions={quizConfig.questions.length}
          />

          {/* Progress Indicator */}
          <ProgressIndicator
            currentQuestion={currentQuestion}
            totalQuestions={quizConfig.questions.length}
            completedQuestions={answeredQuestions.size}
          />

          {/* Question Navigation */}
          <QuestionNavigation
            currentQuestion={currentQuestion}
            totalQuestions={quizConfig.questions.length}
            onGoToQuestion={goToQuestion}
            answeredQuestions={answeredQuestions}
          />

          {/* Question Container */}
          <QuestionContainer
            question={currentQuestionObj}
            currentAnswer={getCurrentAnswer()}
            onAnswer={handleAnswer}
            onNext={handleNext}
            onPrevious={handlePrevious}
            canGoNext={canGoNext()}
            canGoPrevious={currentQuestion > 0}
            isLastQuestion={currentQuestion === quizConfig.questions.length - 1}
          />
        </div>
      </section>

        <Footer />
      </div>
    </QuizErrorBoundary>
  );
}

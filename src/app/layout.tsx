import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import AgeVerificationModal from "@/components/AgeVerificationModal";
import SEOHead from "@/components/SEOHead";
import Analytics from "@/components/Analytics";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "Tu Tienda Íntima - Productos para Adultos en México",
  description: "Descubre los mejores productos íntimos y para adultos en México. Guías completas, reseñas honestas y recomendaciones expertas.",
  keywords: ["productos íntimos", "adultos", "México", "juguetes", "bienestar"],
  openGraph: {
    title: "Tu Tienda Íntima - Productos para Adultos en México",
    description: "Descubre los mejores productos íntimos y para adultos en México",
    locale: "es_MX",
    type: "website",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="es-MX">
      <body className={`${inter.variable} font-sans antialiased`}>
        <SEOHead />
        <Analytics>
          <AgeVerificationModal />
          {children}
        </Analytics>
      </body>
    </html>
  );
}

'use client';

import { useState, useEffect } from 'react';
import { X, ExternalLink, Package, FileText } from 'lucide-react';
import { Category } from '@/types';
import { categoryService } from '@/lib/database';

interface CategoryViewModalProps {
  category: Category;
  onClose: () => void;
}

export default function CategoryViewModal({ category, onClose }: CategoryViewModalProps) {
  const [productCount, setProductCount] = useState<number>(0);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadCategoryStats();
  }, [category.slug]);

  const loadCategoryStats = async () => {
    try {
      setLoading(true);
      const isUsed = await categoryService.isUsedByProducts(category.slug);
      // 这里可以扩展获取更详细的统计信息
      setProductCount(isUsed ? 1 : 0); // 简化处理，实际应该获取确切数量
    } catch (error) {
      console.error('Error loading category stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (date: Date | string) => {
    const d = new Date(date);
    return d.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg w-full max-w-2xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="bg-pink-600 text-white p-4 flex justify-between items-center">
          <h3 className="text-lg font-semibold">分类详情</h3>
          <button
            onClick={onClose}
            className="text-white hover:text-pink-200 text-2xl"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          <div className="space-y-6">
            {/* 基本信息 */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="text-lg font-semibold text-gray-900 mb-4">基本信息</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-600 mb-1">
                    分类名称
                  </label>
                  <p className="text-gray-900 font-medium">{category.name}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-600 mb-1">
                    URL别名
                  </label>
                  <p className="text-gray-900 font-mono text-sm bg-gray-100 px-2 py-1 rounded">
                    {category.slug}
                  </p>
                </div>
              </div>
            </div>

            {/* 描述 */}
            <div>
              <h4 className="text-lg font-semibold text-gray-900 mb-3">描述</h4>
              <div className="bg-gray-50 rounded-lg p-4">
                {category.description ? (
                  <p className="text-gray-700 leading-relaxed">{category.description}</p>
                ) : (
                  <p className="text-gray-500 italic">暂无描述</p>
                )}
              </div>
            </div>

            {/* 统计信息 */}
            <div>
              <h4 className="text-lg font-semibold text-gray-900 mb-3">统计信息</h4>
              <div className="bg-gray-50 rounded-lg p-4">
                {loading ? (
                  <div className="flex items-center justify-center py-4">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-pink-600"></div>
                    <span className="ml-2 text-gray-600">加载中...</span>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex items-center gap-3">
                      <div className="bg-blue-100 p-2 rounded-lg">
                        <Package className="h-5 w-5 text-blue-600" />
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">关联产品</p>
                        <p className="text-lg font-semibold text-gray-900">
                          {productCount} 个
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="bg-green-100 p-2 rounded-lg">
                        <FileText className="h-5 w-5 text-green-600" />
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">相关文章</p>
                        <p className="text-lg font-semibold text-gray-900">
                          - 个
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* 链接预览 */}
            <div>
              <h4 className="text-lg font-semibold text-gray-900 mb-3">页面链接</h4>
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <ExternalLink className="h-4 w-4 text-gray-500" />
                  <span className="text-sm font-medium text-gray-600">分类页面</span>
                </div>
                <a
                  href={`/categoria/${category.slug}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-pink-600 hover:text-pink-700 underline break-all"
                >
                  {process.env.NEXT_PUBLIC_SITE_URL || 'https://your-site.com'}/categoria/{category.slug}
                </a>
              </div>
            </div>

            {/* 创建时间 */}
            <div>
              <h4 className="text-lg font-semibold text-gray-900 mb-3">创建信息</h4>
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-600">创建时间：</span>
                  <span className="text-gray-900 font-medium">
                    {formatDate(category.createdAt)}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="bg-gray-50 px-6 py-4 flex justify-end">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
          >
            关闭
          </button>
        </div>
      </div>
    </div>
  );
}

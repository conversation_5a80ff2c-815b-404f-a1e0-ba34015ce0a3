'use client';

import React from 'react';
import { useScrollReveal } from '@/hooks/useScrollReveal';

export type AnimationType = 
  | 'fade-in'
  | 'slide-up'
  | 'slide-down'
  | 'slide-left'
  | 'slide-right'
  | 'scale-up'
  | 'scale-down';

interface ScrollRevealProps {
  children: React.ReactNode;
  animation?: AnimationType;
  delay?: number;
  duration?: number;
  threshold?: number;
  rootMargin?: string;
  triggerOnce?: boolean;
  className?: string;
  as?: React.ElementType;
}

const animationClasses = {
  'fade-in': {
    initial: 'opacity-0',
    animate: 'opacity-100',
    transform: ''
  },
  'slide-up': {
    initial: 'opacity-0 translate-y-8',
    animate: 'opacity-100 translate-y-0',
    transform: 'transform'
  },
  'slide-down': {
    initial: 'opacity-0 -translate-y-8',
    animate: 'opacity-100 translate-y-0',
    transform: 'transform'
  },
  'slide-left': {
    initial: 'opacity-0 translate-x-8',
    animate: 'opacity-100 translate-x-0',
    transform: 'transform'
  },
  'slide-right': {
    initial: 'opacity-0 -translate-x-8',
    animate: 'opacity-100 translate-x-0',
    transform: 'transform'
  },
  'scale-up': {
    initial: 'opacity-0 scale-95',
    animate: 'opacity-100 scale-100',
    transform: 'transform'
  },
  'scale-down': {
    initial: 'opacity-0 scale-105',
    animate: 'opacity-100 scale-100',
    transform: 'transform'
  }
};

export default function ScrollReveal({
  children,
  animation = 'fade-in',
  delay = 0,
  duration = 600,
  threshold = 0.2,
  rootMargin = '0px 0px -10% 0px',
  triggerOnce = true,
  className = '',
  as: Component = 'div' as React.ElementType
}: ScrollRevealProps) {
  const { ref, isVisible, prefersReducedMotion } = useScrollReveal({
    threshold,
    rootMargin,
    triggerOnce,
    delay,
    duration
  });

  const animationConfig = animationClasses[animation];
  
  // Build the CSS classes
  const baseClasses = 'transition-all ease-out';
  const durationClass = `duration-${Math.min(Math.max(duration, 150), 1000)}`;
  const delayClass = delay > 0 ? `delay-${Math.min(delay, 1000)}` : '';
  
  const visibilityClasses = prefersReducedMotion 
    ? animationConfig.animate // Show immediately if reduced motion
    : isVisible 
      ? animationConfig.animate 
      : animationConfig.initial;

  const finalClassName = [
    baseClasses,
    durationClass,
    delayClass,
    visibilityClasses,
    className
  ].filter(Boolean).join(' ');

  return React.createElement(
    Component,
    {
      ref,
      className: finalClassName,
      style: {
        transitionDuration: `${duration}ms`,
        transitionDelay: `${delay}ms`
      }
    },
    children
  );
}

// Convenience components for common animations
export const FadeIn = (props: Omit<ScrollRevealProps, 'animation'>) => (
  <ScrollReveal {...props} animation="fade-in" />
);

export const SlideUp = (props: Omit<ScrollRevealProps, 'animation'>) => (
  <ScrollReveal {...props} animation="slide-up" />
);

export const SlideDown = (props: Omit<ScrollRevealProps, 'animation'>) => (
  <ScrollReveal {...props} animation="slide-down" />
);

export const SlideLeft = (props: Omit<ScrollRevealProps, 'animation'>) => (
  <ScrollReveal {...props} animation="slide-left" />
);

export const SlideRight = (props: Omit<ScrollRevealProps, 'animation'>) => (
  <ScrollReveal {...props} animation="slide-right" />
);

export const ScaleUp = (props: Omit<ScrollRevealProps, 'animation'>) => (
  <ScrollReveal {...props} animation="scale-up" />
);

export const ScaleDown = (props: Omit<ScrollRevealProps, 'animation'>) => (
  <ScrollReveal {...props} animation="scale-down" />
);

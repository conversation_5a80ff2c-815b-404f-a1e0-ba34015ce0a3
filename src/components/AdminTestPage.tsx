'use client';

import { useState } from 'react';
import { Product, Article } from '@/types';
import ProductEditForm from './ProductEditForm';
import ArticleEditForm from './ArticleEditForm';
import { useToast, ToastContainer } from './Toast';

// 测试页面，用于验证编辑功能
export default function AdminTestPage() {
  const [showProductForm, setShowProductForm] = useState(false);
  const [showArticleForm, setShowArticleForm] = useState(false);
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const [editingArticle, setEditingArticle] = useState<Article | null>(null);
  
  const toast = useToast();

  // 测试产品数据
  const testProduct: Product = {
    id: 'test-product-1',
    name: '测试产品',
    category: 'vibradores',
    amazonUrl: 'https://amazon.com.mx/dp/test123',
    createdAt: new Date(),
    updatedAt: new Date()
  };

  // 测试文章数据
  const testArticle: Article = {
    id: 'test-article-1',
    title: '测试文章标题',
    content: '# 测试文章\n\n这是一个测试文章的内容...',
    excerpt: '这是测试文章的摘要',
    slug: 'test-article',
    productId: 'test-product-1',
    metaTitle: '测试文章 - SEO标题',
    metaDescription: '这是测试文章的SEO描述',
    keywords: ['测试', '文章', '关键词'],
    published: true,
    publishedAt: new Date(),
    createdAt: new Date(),
    updatedAt: new Date()
  };

  const handleProductSaved = (product: Product) => {
    console.log('Product saved:', product);
    toast.success('产品保存成功', `产品"${product.name}"已保存`);
    setShowProductForm(false);
    setEditingProduct(null);
  };

  const handleArticleSaved = (article: Article) => {
    console.log('Article saved:', article);
    toast.success('文章保存成功', `文章"${article.title}"已保存`);
    setShowArticleForm(false);
    setEditingArticle(null);
  };

  const handleNewProduct = () => {
    setEditingProduct(null);
    setShowProductForm(true);
  };

  const handleEditProduct = () => {
    setEditingProduct(testProduct);
    setShowProductForm(true);
  };

  const handleNewArticle = () => {
    setEditingArticle(null);
    setShowArticleForm(true);
  };

  const handleEditArticle = () => {
    setEditingArticle(testArticle);
    setShowArticleForm(true);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">
          管理后台编辑功能测试
        </h1>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* 产品编辑测试 */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              产品编辑功能
            </h2>
            <div className="space-y-4">
              <button
                onClick={handleNewProduct}
                className="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
              >
                新建产品
              </button>
              <button
                onClick={handleEditProduct}
                className="w-full bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700"
              >
                编辑测试产品
              </button>
            </div>
          </div>

          {/* 文章编辑测试 */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              文章编辑功能
            </h2>
            <div className="space-y-4">
              <button
                onClick={handleNewArticle}
                className="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
              >
                新建文章
              </button>
              <button
                onClick={handleEditArticle}
                className="w-full bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700"
              >
                编辑测试文章
              </button>
            </div>
          </div>

          {/* Toast测试 */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              通知测试
            </h2>
            <div className="space-y-2">
              <button
                onClick={() => toast.success('成功', '这是成功消息')}
                className="w-full bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700"
              >
                成功通知
              </button>
              <button
                onClick={() => toast.error('错误', '这是错误消息')}
                className="w-full bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700"
              >
                错误通知
              </button>
              <button
                onClick={() => toast.warning('警告', '这是警告消息')}
                className="w-full bg-yellow-600 text-white px-4 py-2 rounded-lg hover:bg-yellow-700"
              >
                警告通知
              </button>
              <button
                onClick={() => toast.info('信息', '这是信息消息')}
                className="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
              >
                信息通知
              </button>
            </div>
          </div>

          {/* 功能说明 */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              功能说明
            </h2>
            <ul className="text-sm text-gray-600 space-y-2">
              <li>✅ 产品和文章的完整CRUD操作</li>
              <li>✅ 表单验证和错误处理</li>
              <li>✅ 用户友好的通知系统</li>
              <li>✅ 确认对话框</li>
              <li>✅ 未保存更改提醒</li>
              <li>✅ 加载状态和禁用状态</li>
              <li>✅ 响应式设计</li>
              <li>✅ 中文界面</li>
            </ul>
          </div>
        </div>

        {/* 编辑表单 */}
        {showProductForm && (
          <ProductEditForm
            product={editingProduct}
            onClose={() => setShowProductForm(false)}
            onSave={handleProductSaved}
          />
        )}

        {showArticleForm && (
          <ArticleEditForm
            article={editingArticle}
            onClose={() => setShowArticleForm(false)}
            onSave={handleArticleSaved}
          />
        )}

        {/* Toast容器 */}
        <ToastContainer toasts={toast.toasts} onRemove={toast.removeToast} />
      </div>
    </div>
  );
}

'use client';

import { useState, useRef, useCallback } from 'react';
import { Upload, X, Image as ImageIcon, Loader2, AlertCircle } from 'lucide-react';
import Image from 'next/image';
import { supabase } from '@/lib/supabase';

interface ImageUploadProps {
  images: string[];
  onChange: (images: string[]) => void;
  maxImages?: number;
  maxSizeInMB?: number;
  className?: string;
  disabled?: boolean;
}

interface UploadingImage {
  id: string;
  file: File;
  progress: number;
  preview: string;
  error?: string;
}

export default function ImageUpload({
  images,
  onChange,
  maxImages = 10,
  maxSizeInMB = 10,
  className = '',
  disabled = false
}: ImageUploadProps) {
  const [uploadingImages, setUploadingImages] = useState<UploadingImage[]>([]);
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 验证文件
  const validateFile = (file: File): string | null => {
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      return '只支持 JPG、PNG、WebP 格式的图片';
    }
    
    const maxSizeInBytes = maxSizeInMB * 1024 * 1024;
    if (file.size > maxSizeInBytes) {
      return `图片大小不能超过 ${maxSizeInMB}MB`;
    }
    
    return null;
  };

  // 生成唯一文件名
  const generateFileName = (file: File): string => {
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 15);
    const extension = file.name.split('.').pop();
    return `product_${timestamp}_${randomString}.${extension}`;
  };

  // 上传单个文件到Supabase Storage
  const uploadFile = async (file: File, uploadId: string): Promise<string> => {
    const fileName = generateFileName(file);
    const filePath = `products/${fileName}`;

    // 创建预览URL
    const preview = URL.createObjectURL(file);

    // 更新上传状态
    setUploadingImages(prev => prev.map(img =>
      img.id === uploadId
        ? { ...img, preview, progress: 0 }
        : img
    ));

    try {
      // 模拟上传进度
      const progressInterval = setInterval(() => {
        setUploadingImages(prev => prev.map(img =>
          img.id === uploadId && img.progress < 90
            ? { ...img, progress: img.progress + 10 }
            : img
        ));
      }, 200);

      // 上传到Supabase Storage
      const { data, error } = await supabase.storage
        .from('product-images')
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: false
        });

      clearInterval(progressInterval);

      if (error) {
        throw error;
      }

      // 获取公开URL
      const { data: { publicUrl } } = supabase.storage
        .from('product-images')
        .getPublicUrl(filePath);

      // 更新进度为100%
      setUploadingImages(prev => prev.map(img =>
        img.id === uploadId
          ? { ...img, progress: 100 }
          : img
      ));

      return publicUrl;
    } catch (error) {
      console.error('Upload error:', error);
      throw error;
    } finally {
      // 清理预览URL
      URL.revokeObjectURL(preview);
    }
  };

  // 压缩图片
  const compressImage = async (file: File): Promise<File> => {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        // 计算压缩后的尺寸
        let { width, height } = img;
        const maxWidth = 1920;
        const maxHeight = 1080;

        if (width > maxWidth || height > maxHeight) {
          const ratio = Math.min(maxWidth / width, maxHeight / height);
          width *= ratio;
          height *= ratio;
        }

        canvas.width = width;
        canvas.height = height;

        // 绘制压缩后的图片
        ctx?.drawImage(img, 0, 0, width, height);

        canvas.toBlob(
          (blob) => {
            if (blob) {
              const compressedFile = new File([blob], file.name, {
                type: file.type,
                lastModified: Date.now()
              });
              resolve(compressedFile);
            } else {
              resolve(file); // 如果压缩失败，返回原文件
            }
          },
          file.type,
          0.8 // 压缩质量
        );
      };

      img.onerror = () => resolve(file); // 如果加载失败，返回原文件
      img.src = URL.createObjectURL(file);
    });
  };

  // 处理文件选择
  const handleFiles = useCallback(async (files: FileList) => {
    if (disabled) return;

    const fileArray = Array.from(files);
    const remainingSlots = maxImages - images.length;
    const filesToProcess = fileArray.slice(0, remainingSlots);

    if (fileArray.length > remainingSlots) {
      alert(`最多只能上传 ${maxImages} 张图片，已自动选择前 ${remainingSlots} 张`);
    }

    // 验证文件并创建上传任务
    const uploadTasks: UploadingImage[] = [];
    for (const file of filesToProcess) {
      const error = validateFile(file);
      const uploadId = Math.random().toString(36).substring(2, 15);

      // 如果文件有效，尝试压缩
      let processedFile = file;
      if (!error && file.size > 1024 * 1024) { // 只压缩大于1MB的文件
        try {
          processedFile = await compressImage(file);
        } catch (compressionError) {
          console.warn('Image compression failed, using original file:', compressionError);
        }
      }

      uploadTasks.push({
        id: uploadId,
        file: processedFile,
        progress: 0,
        preview: '',
        error
      });
    }

    setUploadingImages(prev => [...prev, ...uploadTasks]);

    // 开始上传有效文件
    const validTasks = uploadTasks.filter(task => !task.error);
    const uploadPromises = validTasks.map(async (task) => {
      try {
        const url = await uploadFile(task.file, task.id);
        return url;
      } catch (error) {
        // 更新错误状态
        const errorMessage = error instanceof Error
          ? `上传失败: ${error.message}`
          : '上传失败，请重试';

        setUploadingImages(prev => prev.map(img =>
          img.id === task.id
            ? { ...img, error: errorMessage }
            : img
        ));
        return null;
      }
    });

    // 等待所有上传完成
    const results = await Promise.all(uploadPromises);
    const successfulUploads = results.filter(url => url !== null) as string[];

    // 更新图片列表
    if (successfulUploads.length > 0) {
      onChange([...images, ...successfulUploads]);

      // 显示成功提示
      if (successfulUploads.length === 1) {
        console.log('图片上传成功！');
      } else {
        console.log(`${successfulUploads.length} 张图片上传成功！`);
      }
    }

    // 清理上传状态（延迟清理以显示完成状态）
    setTimeout(() => {
      setUploadingImages(prev => prev.filter(img =>
        !validTasks.some(task => task.id === img.id)
      ));
    }, 1500);
  }, [images, onChange, maxImages, disabled]);

  // 拖拽处理
  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      handleFiles(e.dataTransfer.files);
    }
  }, [handleFiles]);

  // 删除图片
  const removeImage = (index: number) => {
    const newImages = images.filter((_, i) => i !== index);
    onChange(newImages);
  };

  // 删除上传中的图片
  const removeUploadingImage = (uploadId: string) => {
    setUploadingImages(prev => prev.filter(img => img.id !== uploadId));
  };

  // 重新排序图片
  const moveImage = (fromIndex: number, toIndex: number) => {
    const newImages = [...images];
    const [movedImage] = newImages.splice(fromIndex, 1);
    newImages.splice(toIndex, 0, movedImage);
    onChange(newImages);
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* 上传区域 */}
      <div
        className={`relative border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
          dragActive
            ? 'border-pink-500 bg-pink-50'
            : disabled
            ? 'border-gray-200 bg-gray-50'
            : 'border-gray-300 hover:border-pink-400 hover:bg-pink-50'
        }`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="image/jpeg,image/jpg,image/png,image/webp"
          onChange={(e) => e.target.files && handleFiles(e.target.files)}
          className="hidden"
          disabled={disabled}
        />
        
        <div className="space-y-2">
          <Upload className={`mx-auto h-12 w-12 ${disabled ? 'text-gray-400' : 'text-gray-500'}`} />
          <div>
            <button
              type="button"
              onClick={() => fileInputRef.current?.click()}
              disabled={disabled || images.length >= maxImages}
              className={`font-medium ${
                disabled || images.length >= maxImages
                  ? 'text-gray-400 cursor-not-allowed'
                  : 'text-pink-600 hover:text-pink-500'
              }`}
            >
              点击上传图片
            </button>
            <p className="text-gray-500">或拖拽图片到此区域</p>
          </div>
          <p className="text-sm text-gray-500">
            支持 JPG、PNG、WebP 格式，单个文件最大 {maxSizeInMB}MB
          </p>
          <p className="text-sm text-gray-500">
            已上传 {images.length}/{maxImages} 张图片
          </p>
        </div>
      </div>

      {/* 上传中的图片 */}
      {uploadingImages.length > 0 && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-700">上传中...</h4>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {uploadingImages.map((uploadImg) => (
              <div key={uploadImg.id} className="relative">
                <div className="aspect-square bg-gray-100 rounded-lg border border-gray-200 overflow-hidden">
                  {uploadImg.preview ? (
                    <Image
                      src={uploadImg.preview}
                      alt="上传预览"
                      fill
                      className="object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center">
                      <ImageIcon className="h-8 w-8 text-gray-400" />
                    </div>
                  )}
                  
                  {/* 上传进度或错误状态 */}
                  <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                    {uploadImg.error ? (
                      <div className="text-center">
                        <AlertCircle className="h-6 w-6 text-red-400 mx-auto mb-1" />
                        <p className="text-xs text-white">{uploadImg.error}</p>
                      </div>
                    ) : uploadImg.progress === 100 ? (
                      <div className="text-center">
                        <div className="h-6 w-6 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-1">
                          <svg className="h-4 w-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                        </div>
                        <p className="text-xs text-white">上传完成</p>
                      </div>
                    ) : (
                      <div className="text-center">
                        <Loader2 className="h-6 w-6 text-white animate-spin mx-auto mb-1" />
                        <p className="text-xs text-white">上传中...</p>
                      </div>
                    )}
                  </div>
                </div>
                
                {/* 删除按钮 */}
                <button
                  type="button"
                  onClick={() => removeUploadingImage(uploadImg.id)}
                  className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors"
                >
                  <X className="h-3 w-3" />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 已上传的图片 */}
      {images.length > 0 && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-700">已上传的图片</h4>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {images.map((imageUrl, index) => (
              <div key={index} className="relative group">
                <div className="aspect-square bg-gray-100 rounded-lg border border-gray-200 overflow-hidden">
                  <Image
                    src={imageUrl}
                    alt={`产品图片 ${index + 1}`}
                    fill
                    className="object-cover"
                  />
                </div>
                
                {/* 主图标识 */}
                {index === 0 && (
                  <div className="absolute top-2 left-2 bg-pink-500 text-white text-xs px-2 py-1 rounded">
                    主图
                  </div>
                )}
                
                {/* 删除按钮 */}
                <button
                  type="button"
                  onClick={() => removeImage(index)}
                  className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors opacity-0 group-hover:opacity-100"
                  disabled={disabled}
                >
                  <X className="h-3 w-3" />
                </button>
                
                {/* 排序按钮 */}
                <div className="absolute bottom-2 right-2 flex gap-1 opacity-0 group-hover:opacity-100">
                  {index > 0 && (
                    <button
                      type="button"
                      onClick={() => moveImage(index, index - 1)}
                      className="bg-black bg-opacity-50 text-white p-1 rounded hover:bg-opacity-70"
                      disabled={disabled}
                      title="向前移动"
                    >
                      <svg className="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                      </svg>
                    </button>
                  )}
                  {index < images.length - 1 && (
                    <button
                      type="button"
                      onClick={() => moveImage(index, index + 1)}
                      className="bg-black bg-opacity-50 text-white p-1 rounded hover:bg-opacity-70"
                      disabled={disabled}
                      title="向后移动"
                    >
                      <svg className="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

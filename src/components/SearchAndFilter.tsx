'use client';

import { useState, useEffect } from 'react';
import { Search, Filter, X, RefreshCw } from 'lucide-react';

interface SearchAndFilterProps {
  searchTerm: string;
  onSearchChange: (term: string) => void;
  filters?: Record<string, any>;
  onFilterChange?: (key: string, value: any) => void;
  onClearFilters?: () => void;
  filterOptions?: FilterOption[];
  placeholder?: string;
  showFilterButton?: boolean;
  className?: string;
}

interface FilterOption {
  key: string;
  label: string;
  type: 'select' | 'checkbox' | 'radio';
  options?: { value: string; label: string }[];
  placeholder?: string;
}

export default function SearchAndFilter({
  searchTerm,
  onSearchChange,
  filters = {},
  onFilterChange,
  onClearFilters,
  filterOptions = [],
  placeholder = '搜索...',
  showFilterButton = true,
  className = ''
}: SearchAndFilterProps) {
  const [showFilters, setShowFilters] = useState(false);
  const [localSearchTerm, setLocalSearchTerm] = useState(searchTerm);

  // 防抖搜索
  useEffect(() => {
    const timer = setTimeout(() => {
      onSearchChange(localSearchTerm);
    }, 300);

    return () => clearTimeout(timer);
  }, [localSearchTerm, onSearchChange]);

  // 同步外部搜索词
  useEffect(() => {
    setLocalSearchTerm(searchTerm);
  }, [searchTerm]);

  const handleFilterChange = (key: string, value: any) => {
    if (onFilterChange) {
      onFilterChange(key, value);
    }
  };

  const handleClearAll = () => {
    setLocalSearchTerm('');
    if (onClearFilters) {
      onClearFilters();
    }
  };

  const hasActiveFilters = Object.keys(filters).some(key => {
    const value = filters[key];
    return value !== undefined && value !== null && value !== '' && value !== 'all';
  });

  const activeFilterCount = Object.keys(filters).filter(key => {
    const value = filters[key];
    return value !== undefined && value !== null && value !== '' && value !== 'all';
  }).length;

  return (
    <div className={`space-y-4 ${className}`}>
      {/* 搜索栏 */}
      <div className="flex gap-2">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <input
            type="text"
            value={localSearchTerm}
            onChange={(e) => setLocalSearchTerm(e.target.value)}
            placeholder={placeholder}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent"
          />
          {localSearchTerm && (
            <button
              onClick={() => setLocalSearchTerm('')}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
            >
              <X className="h-4 w-4" />
            </button>
          )}
        </div>

        {/* 过滤器按钮 */}
        {showFilterButton && filterOptions.length > 0 && (
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`px-4 py-2 border rounded-lg flex items-center gap-2 transition-colors ${
              showFilters || hasActiveFilters
                ? 'bg-pink-50 border-pink-300 text-pink-700'
                : 'border-gray-300 text-gray-700 hover:bg-gray-50'
            }`}
          >
            <Filter className="h-4 w-4" />
            过滤器
            {activeFilterCount > 0 && (
              <span className="bg-pink-600 text-white text-xs rounded-full px-2 py-0.5 min-w-[20px] text-center">
                {activeFilterCount}
              </span>
            )}
          </button>
        )}

        {/* 清除所有按钮 */}
        {(localSearchTerm || hasActiveFilters) && (
          <button
            onClick={handleClearAll}
            className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 flex items-center gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            清除
          </button>
        )}
      </div>

      {/* 过滤器面板 */}
      {showFilters && filterOptions.length > 0 && (
        <div className="bg-gray-50 rounded-lg p-4 border">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filterOptions.map((option) => (
              <div key={option.key} className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">
                  {option.label}
                </label>
                
                {option.type === 'select' && (
                  <select
                    value={filters[option.key] || ''}
                    onChange={(e) => handleFilterChange(option.key, e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                  >
                    <option value="">{option.placeholder || '全部'}</option>
                    {option.options?.map((opt) => (
                      <option key={opt.value} value={opt.value}>
                        {opt.label}
                      </option>
                    ))}
                  </select>
                )}

                {option.type === 'checkbox' && option.options && (
                  <div className="space-y-2">
                    {option.options.map((opt) => (
                      <label key={opt.value} className="flex items-center">
                        <input
                          type="checkbox"
                          checked={filters[option.key]?.includes(opt.value) || false}
                          onChange={(e) => {
                            const currentValues = filters[option.key] || [];
                            const newValues = e.target.checked
                              ? [...currentValues, opt.value]
                              : currentValues.filter((v: string) => v !== opt.value);
                            handleFilterChange(option.key, newValues);
                          }}
                          className="mr-2 rounded border-gray-300 text-pink-600 focus:ring-pink-500"
                        />
                        <span className="text-sm text-gray-700">{opt.label}</span>
                      </label>
                    ))}
                  </div>
                )}

                {option.type === 'radio' && option.options && (
                  <div className="space-y-2">
                    {option.options.map((opt) => (
                      <label key={opt.value} className="flex items-center">
                        <input
                          type="radio"
                          name={option.key}
                          value={opt.value}
                          checked={filters[option.key] === opt.value}
                          onChange={(e) => handleFilterChange(option.key, e.target.value)}
                          className="mr-2 border-gray-300 text-pink-600 focus:ring-pink-500"
                        />
                        <span className="text-sm text-gray-700">{opt.label}</span>
                      </label>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* 过滤器操作按钮 */}
          <div className="flex justify-end gap-2 mt-4 pt-4 border-t">
            <button
              onClick={() => onClearFilters && onClearFilters()}
              className="px-4 py-2 text-gray-600 hover:text-gray-800"
            >
              清除过滤器
            </button>
            <button
              onClick={() => setShowFilters(false)}
              className="px-4 py-2 bg-pink-600 text-white rounded-lg hover:bg-pink-700"
            >
              应用过滤器
            </button>
          </div>
        </div>
      )}

      {/* 活跃过滤器标签 */}
      {hasActiveFilters && (
        <div className="flex flex-wrap gap-2">
          {Object.entries(filters).map(([key, value]) => {
            if (!value || value === '' || value === 'all') return null;
            
            const option = filterOptions.find(opt => opt.key === key);
            if (!option) return null;

            const getDisplayValue = () => {
              if (Array.isArray(value)) {
                return value.map(v => {
                  const opt = option.options?.find(o => o.value === v);
                  return opt?.label || v;
                }).join(', ');
              }
              const opt = option.options?.find(o => o.value === value);
              return opt?.label || value;
            };

            return (
              <span
                key={key}
                className="inline-flex items-center gap-1 px-3 py-1 bg-pink-100 text-pink-800 rounded-full text-sm"
              >
                {option.label}: {getDisplayValue()}
                <button
                  onClick={() => handleFilterChange(key, Array.isArray(value) ? [] : '')}
                  className="ml-1 hover:text-pink-600"
                >
                  <X className="h-3 w-3" />
                </button>
              </span>
            );
          })}
        </div>
      )}
    </div>
  );
}

'use client';

import { useState, useRef, useEffect } from 'react';
import { Play, Pause, Volume2, VolumeX, Maximize, Minimize } from 'lucide-react';

interface VideoPlayerProps {
  src: string;
  poster?: string;
  title?: string;
  className?: string;
  autoPlay?: boolean;
  muted?: boolean;
  controls?: boolean;
  preload?: 'none' | 'metadata' | 'auto';
  aspectRatio?: 'video' | 'square' | 'wide' | 'auto';
  showCustomControls?: boolean;
  maxHeight?: string;
}

const ASPECT_RATIOS = {
  video: 'aspect-video', // 16:9
  square: 'aspect-square', // 1:1
  wide: 'aspect-[21/9]', // 21:9 ultra wide
  auto: '' // 自适应，不设置固定比例
};

export default function VideoPlayer({
  src,
  poster,
  title,
  className = '',
  autoPlay = false,
  muted = false,
  controls = true,
  preload = 'metadata',
  aspectRatio = 'auto',
  showCustomControls = false,
  maxHeight = '500px'
}: VideoPlayerProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(muted);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [videoAspectRatio, setVideoAspectRatio] = useState<number | null>(null);
  const [showControls, setShowControls] = useState(false);
  const [controlsTimeout, setControlsTimeout] = useState<NodeJS.Timeout | null>(null);

  const aspectRatioClass = ASPECT_RATIOS[aspectRatio];
  const isAutoAspect = aspectRatio === 'auto';

  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const handleLoadedMetadata = () => {
      setDuration(video.duration);
      setIsLoading(false);
      // 获取视频的实际宽高比
      if (video.videoWidth && video.videoHeight) {
        setVideoAspectRatio(video.videoWidth / video.videoHeight);
      }
    };

    const handleTimeUpdate = () => {
      setCurrentTime(video.currentTime);
    };

    const handlePlay = () => setIsPlaying(true);
    const handlePause = () => setIsPlaying(false);
    const handleVolumeChange = () => {
      setVolume(video.volume);
      setIsMuted(video.muted);
    };

    const handleError = () => {
      setHasError(true);
      setIsLoading(false);
    };

    const handleFullscreenChange = () => {
      const isNowFullscreen = !!document.fullscreenElement;
      setIsFullscreen(isNowFullscreen);

      // 在全屏模式下显示控制栏
      if (isNowFullscreen && showCustomControls) {
        showControlsTemporarily();
      }
    };

    video.addEventListener('loadedmetadata', handleLoadedMetadata);
    video.addEventListener('timeupdate', handleTimeUpdate);
    video.addEventListener('play', handlePlay);
    video.addEventListener('pause', handlePause);
    video.addEventListener('volumechange', handleVolumeChange);
    video.addEventListener('error', handleError);
    document.addEventListener('fullscreenchange', handleFullscreenChange);

    return () => {
      video.removeEventListener('loadedmetadata', handleLoadedMetadata);
      video.removeEventListener('timeupdate', handleTimeUpdate);
      video.removeEventListener('play', handlePlay);
      video.removeEventListener('pause', handlePause);
      video.removeEventListener('volumechange', handleVolumeChange);
      video.removeEventListener('error', handleError);
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, []);

  // 控制栏显示逻辑
  const showControlsTemporarily = () => {
    setShowControls(true);

    // 清除之前的定时器
    if (controlsTimeout) {
      clearTimeout(controlsTimeout);
    }

    // 设置新的定时器，3秒后隐藏控制栏
    const timeout = setTimeout(() => {
      setShowControls(false);
    }, 3000);

    setControlsTimeout(timeout);
  };

  const handleMouseMove = () => {
    if (showCustomControls) {
      showControlsTemporarily();
    }
  };

  const handleClick = () => {
    if (showCustomControls) {
      showControlsTemporarily();
      togglePlay();
    }
  };

  // 清理定时器
  useEffect(() => {
    return () => {
      if (controlsTimeout) {
        clearTimeout(controlsTimeout);
      }
    };
  }, [controlsTimeout]);

  // 初始化时显示控制栏
  useEffect(() => {
    if (showCustomControls) {
      showControlsTemporarily();
    }
  }, [showCustomControls]);

  const togglePlay = () => {
    const video = videoRef.current;
    if (!video) return;

    if (isPlaying) {
      video.pause();
    } else {
      video.play();
    }
  };

  const toggleMute = () => {
    const video = videoRef.current;
    if (!video) return;

    video.muted = !video.muted;
  };

  const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const video = videoRef.current;
    if (!video) return;

    const newVolume = parseFloat(e.target.value);
    video.volume = newVolume;
    setVolume(newVolume);
  };

  const handleSeek = (e: React.ChangeEvent<HTMLInputElement>) => {
    const video = videoRef.current;
    if (!video) return;

    const newTime = parseFloat(e.target.value);
    video.currentTime = newTime;
    setCurrentTime(newTime);
  };

  const toggleFullscreen = async () => {
    const video = videoRef.current;
    if (!video) return;

    try {
      if (isFullscreen) {
        await document.exitFullscreen();
      } else {
        await video.requestFullscreen();
      }
    } catch (error) {
      console.error('Fullscreen error:', error);
    }
  };

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  if (hasError) {
    return (
      <div className={`${isAutoAspect ? 'min-h-[200px]' : aspectRatioClass} bg-gray-100 rounded-lg flex items-center justify-center ${className}`}>
        <div className="text-center text-gray-500">
          <div className="text-4xl mb-2">⚠️</div>
          <p className="text-sm">Error al cargar el video</p>
          <p className="text-xs text-gray-400 mt-1">Por favor verifica que el enlace del video sea válido</p>
        </div>
      </div>
    );
  }

  // 计算容器样式
  const containerStyle = isAutoAspect && videoAspectRatio
    ? {
        aspectRatio: videoAspectRatio.toString(),
        maxHeight: maxHeight
      }
    : {};

  const containerClasses = isAutoAspect
    ? `relative bg-black rounded-lg overflow-hidden group ${className}`
    : `relative ${aspectRatioClass} bg-black rounded-lg overflow-hidden group ${className}`;

  return (
    <div
      className={containerClasses}
      style={containerStyle}
      onMouseMove={handleMouseMove}
      onMouseEnter={() => showCustomControls && showControlsTemporarily()}
    >
      {/* 视频元素 */}
      <video
        ref={videoRef}
        src={src}
        poster={poster}
        autoPlay={autoPlay}
        muted={muted}
        controls={controls && !showCustomControls}
        preload={preload}
        className={isAutoAspect ? "w-full h-full object-contain" : "w-full h-full object-cover"}
        aria-label={title || 'Video de demostración del producto'}
        onClick={showCustomControls ? handleClick : undefined}
      />

      {/* 加载状态 */}
      {isLoading && (
        <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
        </div>
      )}

      {/* 自定义控制栏 */}
      {showCustomControls && (
        <div className={`absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4 transition-opacity duration-300 ${
          showControls || isFullscreen ? 'opacity-100' : 'opacity-0 group-hover:opacity-100'
        }`}>
          {/* 进度条 */}
          <div className="mb-3">
            <input
              type="range"
              min="0"
              max={duration || 0}
              value={currentTime}
              onChange={handleSeek}
              className="w-full h-1 bg-white/30 rounded-lg appearance-none cursor-pointer slider"
            />
          </div>

          {/* 控制按钮 */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              {/* 播放/暂停 */}
              <button
                onClick={togglePlay}
                className="text-white hover:text-pink-300 transition-colors"
                aria-label={isPlaying ? 'Pausar' : 'Reproducir'}
              >
                {isPlaying ? <Pause className="h-5 w-5" /> : <Play className="h-5 w-5" />}
              </button>

              {/* 音量控制 */}
              <div className="flex items-center gap-2">
                <button
                  onClick={toggleMute}
                  className="text-white hover:text-pink-300 transition-colors"
                  aria-label={isMuted ? 'Activar sonido' : 'Silenciar'}
                >
                  {isMuted ? <VolumeX className="h-4 w-4" /> : <Volume2 className="h-4 w-4" />}
                </button>
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  value={isMuted ? 0 : volume}
                  onChange={handleVolumeChange}
                  className="w-16 h-1 bg-white/30 rounded-lg appearance-none cursor-pointer slider"
                />
              </div>

              {/* 时间显示 */}
              <span className="text-white text-sm">
                {formatTime(currentTime)} / {formatTime(duration)}
              </span>
            </div>

            {/* 全屏按钮 */}
            <button
              onClick={toggleFullscreen}
              className="text-white hover:text-pink-300 transition-colors"
              aria-label={isFullscreen ? 'Salir de pantalla completa' : 'Pantalla completa'}
            >
              {isFullscreen ? <Minimize className="h-4 w-4" /> : <Maximize className="h-4 w-4" />}
            </button>
          </div>
        </div>
      )}

      {/* 视频标题覆盖层（可选） */}
      {title && (
        <div className="absolute top-4 left-4 right-4">
          <h3 className="text-white text-sm font-medium drop-shadow-lg line-clamp-2">
            {title}
          </h3>
        </div>
      )}
    </div>
  );
}

// 预设样式的便捷组件
export function VideoPlayerCard({
  src,
  poster,
  title,
  className = ''
}: Pick<VideoPlayerProps, 'src' | 'poster' | 'title' | 'className'>) {
  return (
    <VideoPlayer
      src={src}
      poster={poster}
      title={title}
      className={`rounded-xl ${className}`}
      aspectRatio="auto"
      showCustomControls={true}
      preload="metadata"
      maxHeight="400px"
    />
  );
}

export function VideoPlayerHero({
  src,
  poster,
  title,
  className = ''
}: Pick<VideoPlayerProps, 'src' | 'poster' | 'title' | 'className'>) {
  return (
    <VideoPlayer
      src={src}
      poster={poster}
      title={title}
      className={`rounded-xl ${className}`}
      aspectRatio="auto"
      showCustomControls={true}
      preload="metadata"
      maxHeight="500px"
    />
  );
}

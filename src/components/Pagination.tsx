'use client';

import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-react';

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  onPageChange: (page: number) => void;
  showSizeChanger?: boolean;
  onSizeChange?: (size: number) => void;
  pageSizeOptions?: number[];
  showQuickJumper?: boolean;
  showTotal?: boolean;
  className?: string;
}

export default function Pagination({
  currentPage,
  totalPages,
  totalItems,
  itemsPerPage,
  onPageChange,
  showSizeChanger = true,
  onSizeChange,
  pageSizeOptions = [10, 20, 50, 100],
  showQuickJumper = true,
  showTotal = true,
  className = ''
}: PaginationProps) {
  // 计算显示的页码范围
  const getVisiblePages = () => {
    const delta = 2; // 当前页前后显示的页数
    const range = [];
    const rangeWithDots = [];

    // 计算显示范围
    const start = Math.max(1, currentPage - delta);
    const end = Math.min(totalPages, currentPage + delta);

    // 添加第一页
    if (start > 1) {
      rangeWithDots.push(1);
      if (start > 2) {
        rangeWithDots.push('...');
      }
    }

    // 添加中间页码
    for (let i = start; i <= end; i++) {
      rangeWithDots.push(i);
    }

    // 添加最后一页
    if (end < totalPages) {
      if (end < totalPages - 1) {
        rangeWithDots.push('...');
      }
      rangeWithDots.push(totalPages);
    }

    return rangeWithDots;
  };

  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages && page !== currentPage) {
      onPageChange(page);
    }
  };

  const handleSizeChange = (size: number) => {
    if (onSizeChange) {
      onSizeChange(size);
    }
  };

  const handleQuickJump = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      const target = e.target as HTMLInputElement;
      const page = parseInt(target.value);
      if (page >= 1 && page <= totalPages) {
        handlePageChange(page);
        target.value = '';
      }
    }
  };

  if (totalPages <= 1) {
    return null;
  }

  const startItem = (currentPage - 1) * itemsPerPage + 1;
  const endItem = Math.min(currentPage * itemsPerPage, totalItems);

  return (
    <div className={`flex items-center justify-between gap-4 ${className}`}>
      {/* 总数显示 */}
      {showTotal && (
        <div className="text-sm text-gray-600">
          显示 {startItem}-{endItem} 项，共 {totalItems} 项
        </div>
      )}

      <div className="flex items-center gap-4">
        {/* 每页数量选择器 */}
        {showSizeChanger && onSizeChange && (
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">每页</span>
            <select
              value={itemsPerPage}
              onChange={(e) => handleSizeChange(parseInt(e.target.value))}
              className="border border-gray-300 rounded px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent"
            >
              {pageSizeOptions.map(size => (
                <option key={size} value={size}>{size}</option>
              ))}
            </select>
            <span className="text-sm text-gray-600">项</span>
          </div>
        )}

        {/* 分页控件 */}
        <div className="flex items-center gap-1">
          {/* 第一页 */}
          <button
            onClick={() => handlePageChange(1)}
            disabled={currentPage === 1}
            className="p-2 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
            title="第一页"
          >
            <ChevronsLeft className="h-4 w-4" />
          </button>

          {/* 上一页 */}
          <button
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
            className="p-2 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
            title="上一页"
          >
            <ChevronLeft className="h-4 w-4" />
          </button>

          {/* 页码 */}
          <div className="flex items-center gap-1">
            {getVisiblePages().map((page, index) => (
              <div key={index}>
                {page === '...' ? (
                  <span className="px-3 py-2 text-gray-400">...</span>
                ) : (
                  <button
                    onClick={() => handlePageChange(page as number)}
                    className={`px-3 py-2 rounded text-sm font-medium ${
                      currentPage === page
                        ? 'bg-pink-600 text-white'
                        : 'hover:bg-gray-100 text-gray-700'
                    }`}
                  >
                    {page}
                  </button>
                )}
              </div>
            ))}
          </div>

          {/* 下一页 */}
          <button
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
            className="p-2 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
            title="下一页"
          >
            <ChevronRight className="h-4 w-4" />
          </button>

          {/* 最后一页 */}
          <button
            onClick={() => handlePageChange(totalPages)}
            disabled={currentPage === totalPages}
            className="p-2 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
            title="最后一页"
          >
            <ChevronsRight className="h-4 w-4" />
          </button>
        </div>

        {/* 快速跳转 */}
        {showQuickJumper && (
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">跳转到</span>
            <input
              type="number"
              min={1}
              max={totalPages}
              placeholder="页码"
              onKeyDown={handleQuickJump}
              className="w-16 px-2 py-1 border border-gray-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent"
            />
            <span className="text-sm text-gray-600">页</span>
          </div>
        )}
      </div>
    </div>
  );
}

// 分页信息接口
export interface PaginationInfo {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

// 分页参数接口
export interface PaginationParams {
  page: number;
  limit: number;
  offset: number;
}

// 分页结果接口
export interface PaginatedResult<T> {
  data: T[];
  pagination: PaginationInfo;
}

// 计算分页参数的工具函数
export function calculatePagination(page: number, itemsPerPage: number): PaginationParams {
  const currentPage = Math.max(1, page);
  const limit = Math.max(1, itemsPerPage);
  const offset = (currentPage - 1) * limit;

  return {
    page: currentPage,
    limit,
    offset
  };
}

// 创建分页信息的工具函数
export function createPaginationInfo(
  currentPage: number,
  itemsPerPage: number,
  totalItems: number
): PaginationInfo {
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  
  return {
    currentPage: Math.max(1, Math.min(currentPage, totalPages)),
    totalPages: Math.max(1, totalPages),
    totalItems,
    itemsPerPage,
    hasNextPage: currentPage < totalPages,
    hasPrevPage: currentPage > 1
  };
}

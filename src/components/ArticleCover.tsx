'use client';

import Image from 'next/image';
import { useState } from 'react';
import { ImageIcon } from 'lucide-react';

interface ArticleCoverProps {
  src?: string;
  alt: string;
  title?: string;
  className?: string;
  priority?: boolean;
  sizes?: string;
  fill?: boolean;
  width?: number;
  height?: number;
  aspectRatio?: 'square' | 'video' | 'wide' | 'tall';
  showPlaceholder?: boolean;
}

const ASPECT_RATIOS = {
  square: 'aspect-square',
  video: 'aspect-video', // 16:9
  wide: 'aspect-[21/9]', // 21:9 ultra wide
  tall: 'aspect-[4/5]' // 4:5 portrait
};

const DEFAULT_PLACEHOLDER = '/images/article-placeholder.svg';

export default function ArticleCover({
  src,
  alt,
  title,
  className = '',
  priority = false,
  sizes = '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',
  fill = false,
  width,
  height,
  aspectRatio = 'video',
  showPlaceholder = true
}: ArticleCoverProps) {
  const [imageError, setImageError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // 如果没有图片且不显示占位符，返回null
  if (!src && !showPlaceholder) {
    return null;
  }

  // 确定要显示的图片源
  const imageSrc = (src && !imageError) ? src : DEFAULT_PLACEHOLDER;
  const aspectRatioClass = ASPECT_RATIOS[aspectRatio];

  const handleImageLoad = () => {
    setIsLoading(false);
  };

  const handleImageError = () => {
    setImageError(true);
    setIsLoading(false);
  };

  return (
    <div className={`relative overflow-hidden bg-gradient-to-br from-gray-50 to-gray-100 ${aspectRatioClass} ${className}`}>
      {/* 加载状态 */}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200">
          <div className="flex flex-col items-center gap-3 text-gray-400">
            <div className="relative">
              <div className="w-8 h-8 border-2 border-pink-200 border-t-pink-500 rounded-full animate-spin"></div>
              <div className="absolute inset-0 w-8 h-8 border-2 border-transparent border-r-purple-300 rounded-full animate-spin" style={{ animationDelay: '0.5s', animationDirection: 'reverse' }}></div>
            </div>
            <span className="text-sm font-medium">Cargando...</span>
          </div>
        </div>
      )}

      {/* 图片 */}
      {fill ? (
        <Image
          src={imageSrc}
          alt={alt}
          fill
          className={`object-cover transition-all duration-500 ${
            isLoading ? 'opacity-0 scale-105' : 'opacity-100 scale-100'
          }`}
          sizes={sizes}
          priority={priority}
          onLoad={handleImageLoad}
          onError={handleImageError}
        />
      ) : (
        <Image
          src={imageSrc}
          alt={alt}
          width={width || 800}
          height={height || 450}
          className={`w-full h-full object-cover transition-all duration-500 ${
            isLoading ? 'opacity-0 scale-105' : 'opacity-100 scale-100'
          }`}
          sizes={sizes}
          priority={priority}
          onLoad={handleImageLoad}
          onError={handleImageError}
        />
      )}

      {/* 标题覆盖层（可选） */}
      {title && (
        <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent">
          <div className="absolute bottom-4 left-4 right-4">
            <h3 className="text-white text-lg font-bold line-clamp-2 drop-shadow-lg">
              {title}
            </h3>
          </div>
        </div>
      )}

      {/* 错误状态指示器 */}
      {imageError && src && (
        <div className="absolute top-3 right-3">
          <div className="bg-red-50 text-red-600 px-2.5 py-1 rounded-full text-xs font-medium shadow-sm">
            Error de imagen
          </div>
        </div>
      )}
    </div>
  );
}

// 预设样式的便捷组件
export function ArticleCoverCard({
  src,
  alt,
  title,
  className = ''
}: Pick<ArticleCoverProps, 'src' | 'alt' | 'title' | 'className'>) {
  return (
    <ArticleCover
      src={src}
      alt={alt}
      title={title}
      className={`rounded-xl overflow-hidden ${className}`}
      aspectRatio="square"
      fill
    />
  );
}

export function ArticleCoverHero({ 
  src, 
  alt, 
  title, 
  className = '' 
}: Pick<ArticleCoverProps, 'src' | 'alt' | 'title' | 'className'>) {
  return (
    <ArticleCover
      src={src}
      alt={alt}
      title={title}
      className={`rounded-xl ${className}`}
      aspectRatio="wide"
      priority
      fill
    />
  );
}

export function ArticleCoverThumbnail({ 
  src, 
  alt, 
  className = '' 
}: Pick<ArticleCoverProps, 'src' | 'alt' | 'className'>) {
  return (
    <ArticleCover
      src={src}
      alt={alt}
      className={`rounded-md ${className}`}
      aspectRatio="square"
      width={150}
      height={150}
      sizes="150px"
    />
  );
}

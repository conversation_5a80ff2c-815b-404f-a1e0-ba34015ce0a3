import Link from 'next/link';
import { Star, ExternalLink, Heart } from 'lucide-react';
import { Product } from '@/types';
import { generateAmazonMexicoUrl } from '@/lib/asinUtils';

interface ProductCardProps {
  product: Product;
  className?: string;
}

export default function ProductCard({
  product,
  className = ''
}: ProductCardProps) {
  // Generate Amazon Mexico URL from ASIN
  const amazonUrl = generateAmazonMexicoUrl(product.asin);
  return (
    <div className={`bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 hover:-translate-y-1 ${className}`}>
      {/* Product Image */}
      <div className="h-48 bg-gradient-to-br from-pink-100 to-purple-100 flex items-center justify-center relative overflow-hidden">
        <div className="text-6xl">🎁</div>

        {/* Category Badge */}
        <div className="absolute top-4 left-4 bg-pink-600 text-white px-3 py-1 rounded-full text-xs font-medium">
          {product.category.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
        </div>

        {/* Wishlist Button */}
        <button className="absolute bottom-4 right-4 bg-white rounded-full p-2 shadow-md hover:bg-pink-50 transition-colors">
          <Heart className="h-4 w-4 text-gray-600 hover:text-pink-600" />
        </button>
      </div>

      {/* Product Content */}
      <div className="p-6">
        <h3 className="text-xl font-bold text-gray-900 mb-4 line-clamp-2">
          {product.name}
        </h3>

        {/* Category Info */}
        <div className="mb-4">
          <span className="inline-block bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm">
            {product.category.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
          </span>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-3">
          <a
            href={amazonUrl}
            target="_blank"
            rel="noopener noreferrer"
            className="flex-1 bg-pink-600 hover:bg-pink-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center justify-center gap-2"
          >
            Ver en Amazon
            <ExternalLink className="h-4 w-4" />
          </a>
        </div>

        {/* Trust Indicators */}
        <div className="mt-4 pt-4 border-t border-gray-100">
          <div className="flex items-center justify-between text-xs text-gray-500">
            <span className="flex items-center gap-1">
              <span className="w-2 h-2 bg-green-400 rounded-full"></span>
              Disponible en Amazon
            </span>
            <span>Envío rápido</span>
            <span>Pago seguro</span>
          </div>
        </div>
      </div>
    </div>
  );
}

// Componente para mostrar una lista de productos
interface ProductGridProps {
  products: Product[];
  title?: string;
  showAll?: boolean;
  maxItems?: number;
  className?: string;
}

export function ProductGrid({ 
  products, 
  title, 
  showAll = false, 
  maxItems = 6,
  className = '' 
}: ProductGridProps) {
  const displayProducts = showAll ? products : products.slice(0, maxItems);

  return (
    <div className={className}>
      {title && (
        <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">
          {title}
        </h2>
      )}
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {displayProducts.map((product) => (
          <ProductCard key={product.id} product={product} />
        ))}
      </div>

      {!showAll && products.length > maxItems && (
        <div className="text-center mt-8">
          <Link
            href="/productos"
            className="inline-flex items-center gap-2 bg-pink-600 hover:bg-pink-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
          >
            Ver todos los productos
            <ExternalLink className="h-4 w-4" />
          </Link>
        </div>
      )}
    </div>
  );
}

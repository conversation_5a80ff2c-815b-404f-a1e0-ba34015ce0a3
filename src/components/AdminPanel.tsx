'use client';

import { useState, useEffect, useCallback } from 'react';
import { Plus, Edit, Trash2, Eye, FileText, PenTool, Settings } from 'lucide-react';
import { Product, Article, Category } from '@/types';
import { productService, articleService, categoryService } from '@/lib/database';
import ProductEditForm from './ProductEditForm';
import ArticleEditForm from './ArticleEditForm';
import CategoryEditForm from './CategoryEditForm';
import ProductViewModal from './ProductViewModal';
import ArticleViewModal from './ArticleViewModal';
import CategoryViewModal from './CategoryViewModal';
import QuizConfigManager from './admin/QuizConfigManager';
import { useToast, ToastContainer } from './Toast';
import ConfirmDialog, { useConfirmDialog } from './ConfirmDialog';
import Pagination from './Pagination';
import SearchAndFilter from './SearchAndFilter';
import { usePaginatedData } from '@/hooks/usePagination';

interface AdminPanelProps {
  onClose: () => void;
}

export default function AdminPanel({ onClose }: AdminPanelProps) {
  const [activeTab, setActiveTab] = useState<'products' | 'categories' | 'quiz'>('products');
  const [products, setProducts] = useState<Product[]>([]);
  const [articles, setArticles] = useState<Article[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [productArticles, setProductArticles] = useState<Record<string, Article | null>>({});
  const [loading, setLoading] = useState(true);

  // 分页状态
  const productPagination = usePaginatedData({
    initialPageSize: 10,
    initialPage: 1
  });

  const categoryPagination = usePaginatedData({
    initialPageSize: 10,
    initialPage: 1
  });

  // 编辑状态管理
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const [editingArticle, setEditingArticle] = useState<Article | null>(null);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const [editingArticleForProduct, setEditingArticleForProduct] = useState<Product | null>(null);
  const [showProductForm, setShowProductForm] = useState(false);
  const [showArticleForm, setShowArticleForm] = useState(false);
  const [showCategoryForm, setShowCategoryForm] = useState(false);

  // 查看状态管理
  const [viewingProduct, setViewingProduct] = useState<Product | null>(null);
  const [viewingArticle, setViewingArticle] = useState<Article | null>(null);
  const [viewingCategory, setViewingCategory] = useState<Category | null>(null);
  const [showProductView, setShowProductView] = useState(false);
  const [showArticleView, setShowArticleView] = useState(false);
  const [showCategoryView, setShowCategoryView] = useState(false);

  // Toast和确认对话框
  const toast = useToast();
  const confirmDialog = useConfirmDialog();

  // 加载产品数据
  const loadProducts = useCallback(async () => {
    try {
      setLoading(true);
      const result = await productService.getPaginated(
        productPagination.paginationParams,
        productPagination.searchTerm,
        productPagination.filters.category
      );

      setProducts(result.data);
      productPagination.setTotalItems(result.pagination.totalItems);

      // 加载每个产品对应的文章
      await loadProductArticles(result.data);
    } catch (error) {
      console.error('Error loading products:', error);
      toast.error('加载失败', '无法加载产品数据');
    } finally {
      setLoading(false);
    }
  }, [productPagination.paginationParams, productPagination.searchTerm, productPagination.filters.category]);

  // 加载分类数据
  const loadCategories = useCallback(async () => {
    try {
      setLoading(true);
      const result = await categoryService.getPaginated(
        categoryPagination.paginationParams,
        categoryPagination.searchTerm
      );

      setCategories(result.data);
      categoryPagination.setTotalItems(result.pagination.totalItems);
    } catch (error) {
      console.error('Error loading categories:', error);
      toast.error('加载失败', '无法加载分类数据');
    } finally {
      setLoading(false);
    }
  }, [categoryPagination.paginationParams, categoryPagination.searchTerm]);

  // 初始化数据加载
  useEffect(() => {
    if (activeTab === 'products') {
      loadProducts();
    } else if (activeTab === 'categories') {
      loadCategories();
    }
    // quiz tab doesn't need initial data loading
  }, [activeTab, loadProducts, loadCategories]);

  // 监听产品分页变化
  useEffect(() => {
    if (activeTab === 'products') {
      loadProducts();
    }
  }, [productPagination.currentPage, productPagination.pageSize, productPagination.searchTerm, productPagination.filters]);

  // 监听分类分页变化
  useEffect(() => {
    if (activeTab === 'categories') {
      loadCategories();
    }
  }, [categoryPagination.currentPage, categoryPagination.pageSize, categoryPagination.searchTerm]);

  const loadProductArticles = async (products: Product[]) => {
    try {
      const articleMap: Record<string, Article | null> = {};

      for (const product of products) {
        try {
          const article = await articleService.getByProductId(product.id);
          articleMap[product.id] = article;
        } catch (error) {
          console.error(`Error loading article for product ${product.id}:`, error);
          articleMap[product.id] = null;
        }
      }

      setProductArticles(articleMap);
    } catch (error) {
      console.error('Error loading product articles:', error);
    }
  };

  const handleDeleteProduct = async (id: string) => {
    const product = products.find(p => p.id === id);
    if (!product) return;

    confirmDialog.showConfirm({
      title: '删除产品',
      message: `确定要删除产品"${product.name}"吗？此操作无法撤销。`,
      confirmText: '删除',
      cancelText: '取消',
      type: 'danger',
      onConfirm: async () => {
        try {
          await productService.delete(id);
          toast.success('删除成功', '产品已成功删除');
          // 重新加载产品数据
          loadProducts();
        } catch (error) {
          console.error('Error deleting product:', error);
          toast.error('删除失败', '删除产品时出错，请重试');
        }
      }
    });
  };



  const handleDeleteCategory = async (id: string) => {
    const category = categories.find(c => c.id === id);
    if (!category) return;

    // 检查是否有产品使用该分类
    try {
      const isUsed = await categoryService.isUsedByProducts(category.slug);
      if (isUsed) {
        toast.error('无法删除', '该分类下还有产品，请先删除或移动相关产品');
        return;
      }
    } catch (error) {
      console.error('Error checking category usage:', error);
      toast.error('检查失败', '无法检查分类使用情况，请重试');
      return;
    }

    confirmDialog.showConfirm({
      title: '删除分类',
      message: `确定要删除分类"${category.name}"吗？此操作无法撤销。`,
      confirmText: '删除',
      cancelText: '取消',
      type: 'danger',
      onConfirm: async () => {
        try {
          await categoryService.delete(id);
          toast.success('删除成功', '分类已成功删除');
          // 重新加载分类数据
          loadCategories();
        } catch (error) {
          console.error('Error deleting category:', error);
          toast.error('删除失败', '删除分类时出错，请重试');
        }
      }
    });
  };

  // 编辑处理函数
  const handleEditProduct = (product: Product) => {
    setEditingProduct(product);
    setShowProductForm(true);
  };



  const handleEditCategory = (category: Category) => {
    setEditingCategory(category);
    setShowCategoryForm(true);
  };

  // 查看处理函数
  const handleViewProduct = (product: Product) => {
    setViewingProduct(product);
    setShowProductView(true);
  };

  const handleViewArticle = (article: Article) => {
    setViewingArticle(article);
    setShowArticleView(true);
  };

  const handleViewCategory = (category: Category) => {
    setViewingCategory(category);
    setShowCategoryView(true);
  };

  const handleNewProduct = () => {
    setEditingProduct(null);
    setShowProductForm(true);
  };



  const handleNewCategory = () => {
    setEditingCategory(null);
    setShowCategoryForm(true);
  };

  // 产品文章管理函数
  const handleManageProductArticle = (product: Product) => {
    const existingArticle = productArticles[product.id];
    if (existingArticle) {
      // 编辑现有文章
      setEditingArticle(existingArticle);
    } else {
      // 创建新文章
      setEditingArticle(null);
    }
    setEditingArticleForProduct(product);
    setShowArticleForm(true);
  };

  const handleViewProductArticle = (product: Product) => {
    const article = productArticles[product.id];
    if (article) {
      setViewingArticle(article);
      setShowArticleView(true);
    }
  };

  const handleProductSaved = (product: Product) => {
    if (editingProduct) {
      toast.success('更新成功', '产品信息已成功更新');
    } else {
      toast.success('创建成功', '新产品已成功创建');
      // 新建产品后回到第一页
      productPagination.setPage(1);
    }
    setShowProductForm(false);
    setEditingProduct(null);
    // 重新加载产品数据
    loadProducts();
  };

  const handleArticleSaved = (article: Article) => {
    if (editingArticle) {
      // 更新现有文章
      setArticles(articles.map(a => a.id === article.id ? article : a));
      toast.success('更新成功', '文章已成功更新');
    } else {
      // 添加新文章
      setArticles([article, ...articles]);
      toast.success('创建成功', '新文章已成功创建');
    }

    // 如果是通过产品管理创建/编辑的文章，更新产品文章映射
    if (editingArticleForProduct) {
      setProductArticles(prev => ({
        ...prev,
        [editingArticleForProduct.id]: article
      }));
      setEditingArticleForProduct(null);
    }

    setShowArticleForm(false);
    setEditingArticle(null);
  };

  const handleCategorySaved = (category: Category) => {
    if (editingCategory) {
      toast.success('更新成功', '分类已成功更新');
    } else {
      toast.success('创建成功', '新分类已成功创建');
      // 新建分类后回到第一页
      categoryPagination.setPage(1);
    }
    setShowCategoryForm(false);
    setEditingCategory(null);
    // 重新加载分类数据
    loadCategories();
  };

  const closeProductForm = () => {
    setShowProductForm(false);
    setEditingProduct(null);
  };

  const closeArticleForm = () => {
    setShowArticleForm(false);
    setEditingArticle(null);
    setEditingArticleForProduct(null);
  };

  const closeCategoryForm = () => {
    setShowCategoryForm(false);
    setEditingCategory(null);
  };

  // 关闭查看模态框
  const closeProductView = () => {
    setShowProductView(false);
    setViewingProduct(null);
  };

  const closeArticleView = () => {
    setShowArticleView(false);
    setViewingArticle(null);
  };

  const closeCategoryView = () => {
    setShowCategoryView(false);
    setViewingCategory(null);
  };

  if (loading) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-pink-600 mx-auto"></div>
          <p className="mt-4 text-center">加载中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg w-full max-w-6xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="bg-pink-600 text-white p-4 flex justify-between items-center">
          <h2 className="text-xl font-bold">管理面板</h2>
          <button
            onClick={onClose}
            className="text-white hover:text-pink-200 text-2xl"
          >
            ×
          </button>
        </div>

        {/* Tabs */}
        <div className="border-b">
          <div className="flex">
            <button
              onClick={() => setActiveTab('products')}
              className={`px-6 py-3 font-medium ${
                activeTab === 'products'
                  ? 'border-b-2 border-pink-600 text-pink-600'
                  : 'text-gray-600 hover:text-pink-600'
              }`}
            >
              产品 ({productPagination.totalItems})
            </button>
            <button
              onClick={() => setActiveTab('categories')}
              className={`px-6 py-3 font-medium ${
                activeTab === 'categories'
                  ? 'border-b-2 border-pink-600 text-pink-600'
                  : 'text-gray-600 hover:text-pink-600'
              }`}
            >
              分类 ({categoryPagination.totalItems})
            </button>
            <button
              onClick={() => setActiveTab('quiz')}
              className={`px-6 py-3 font-medium flex items-center gap-2 ${
                activeTab === 'quiz'
                  ? 'border-b-2 border-pink-600 text-pink-600'
                  : 'text-gray-600 hover:text-pink-600'
              }`}
            >
              <Settings className="h-4 w-4" />
              测验管理
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          {activeTab === 'products' && (
            <div>
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-lg font-semibold text-pink-600">产品管理</h3>
                <button
                  onClick={handleNewProduct}
                  className="bg-pink-600 text-white px-4 py-2 rounded-lg hover:bg-pink-700 flex items-center gap-2"
                >
                  <Plus className="h-4 w-4" />
                  新建产品
                </button>
              </div>

              {/* 搜索和过滤 */}
              <SearchAndFilter
                searchTerm={productPagination.searchTerm}
                onSearchChange={productPagination.setSearchTerm}
                filters={productPagination.filters}
                onFilterChange={productPagination.setFilter}
                onClearFilters={productPagination.clearFilters}
                filterOptions={[
                  {
                    key: 'category',
                    label: '分类',
                    type: 'select',
                    options: [
                      { value: 'all', label: '全部分类' },
                      { value: 'vibrators', label: '震动器' },
                      { value: 'dildos', label: '假阳具' },
                      { value: 'anal-toys', label: '肛门玩具' },
                      { value: 'couples-toys', label: '情侣玩具' },
                      { value: 'male-toys', label: '男性玩具' },
                      { value: 'accessories', label: '配件' }
                    ],
                    placeholder: '选择分类'
                  }
                ]}
                placeholder="搜索产品名称或描述..."
                className="mb-6"
              />

              <div className="grid gap-4">
                {loading ? (
                  <div className="flex items-center justify-center py-12">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-pink-600"></div>
                    <span className="ml-3 text-gray-600">加载中...</span>
                  </div>
                ) : products.length === 0 ? (
                  <div className="text-center py-12">
                    <div className="text-gray-400 mb-4">
                      <FileText className="h-12 w-12 mx-auto mb-2" />
                      {productPagination.isFiltered ? '没有找到匹配的产品' : '暂无产品'}
                    </div>
                    <p className="text-gray-500 mb-4">
                      {productPagination.isFiltered
                        ? '尝试调整搜索条件或清除过滤器'
                        : '点击上方"新建产品"按钮开始添加产品'
                      }
                    </p>
                    {productPagination.isFiltered && (
                      <button
                        onClick={productPagination.clearAll}
                        className="text-pink-600 hover:text-pink-700"
                      >
                        清除所有过滤条件
                      </button>
                    )}
                  </div>
                ) : (
                  products.map((product) => (
                  <div key={product.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h4 className="font-semibold text-lg text-black">{product.name}</h4>
                        </div>
                        <p className="text-gray-600 text-sm mb-2 capitalize">{product.category.replace('-', ' ')}</p>
                        <div className="mt-2 flex items-center gap-4 text-sm text-gray-500">
                          <span>分类: {product.category}</span>
                          <span>创建时间: {new Date(product.createdAt).toLocaleDateString('zh-CN')}</span>
                          {productArticles[product.id] && (
                            <span>文章: {productArticles[product.id]?.title}</span>
                          )}
                        </div>
                      </div>
                      <div className="flex gap-2 ml-4">
                        {/* 文章管理按钮 */}
                        {productArticles[product.id] ? (
                          <button
                            onClick={() => handleViewProductArticle(product)}
                            className="p-2 text-purple-600 hover:bg-purple-50 rounded"
                            title="查看文章"
                          >
                            <FileText className="h-4 w-4" />
                          </button>
                        ) : null}
                        <button
                          onClick={() => handleManageProductArticle(product)}
                          className={`p-2 rounded ${
                            productArticles[product.id]
                              ? 'text-orange-600 hover:bg-orange-50'
                              : 'text-blue-600 hover:bg-blue-50'
                          }`}
                          title={productArticles[product.id] ? '编辑文章' : '创建文章'}
                        >
                          <PenTool className="h-4 w-4" />
                        </button>

                        {/* 产品管理按钮 */}
                        <button
                          onClick={() => handleViewProduct(product)}
                          className="p-2 text-blue-600 hover:bg-blue-50 rounded"
                          title="查看产品详情"
                        >
                          <Eye className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleEditProduct(product)}
                          className="p-2 text-green-600 hover:bg-green-50 rounded"
                          title="编辑产品"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDeleteProduct(product.id)}
                          className="p-2 text-red-600 hover:bg-red-50 rounded"
                          title="删除产品"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                  ))
                )}
              </div>

              {/* 产品分页 */}
              {productPagination.totalItems > 0 && (
                <div className="mt-6">
                  <Pagination
                    currentPage={productPagination.currentPage}
                    totalPages={productPagination.paginationInfo.totalPages}
                    totalItems={productPagination.totalItems}
                    itemsPerPage={productPagination.pageSize}
                    onPageChange={productPagination.setPage}
                    onSizeChange={productPagination.setPageSize}
                    showTotal={true}
                    showSizeChanger={true}
                    showQuickJumper={true}
                  />
                </div>
              )}
            </div>
          )}



          {activeTab === 'categories' && (
            <div>
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-lg font-semibold text-pink-600">分类管理</h3>
                <button
                  onClick={handleNewCategory}
                  className="bg-pink-600 text-white px-4 py-2 rounded-lg hover:bg-pink-700 flex items-center gap-2"
                >
                  <Plus className="h-4 w-4" />
                  新建分类
                </button>
              </div>

              {/* 搜索和过滤 */}
              <SearchAndFilter
                searchTerm={categoryPagination.searchTerm}
                onSearchChange={categoryPagination.setSearchTerm}
                placeholder="搜索分类名称、描述或URL..."
                showFilterButton={false}
                className="mb-6"
              />

              <div className="grid gap-4">
                {loading ? (
                  <div className="flex items-center justify-center py-12">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-pink-600"></div>
                    <span className="ml-3 text-gray-600">加载中...</span>
                  </div>
                ) : categories.length === 0 ? (
                  <div className="text-center py-12">
                    <div className="text-gray-400 mb-4">
                      <FileText className="h-12 w-12 mx-auto mb-2" />
                      {categoryPagination.isFiltered ? '没有找到匹配的分类' : '暂无分类'}
                    </div>
                    <p className="text-gray-500 mb-4">
                      {categoryPagination.isFiltered
                        ? '尝试调整搜索条件'
                        : '点击上方"新建分类"按钮开始添加分类'
                      }
                    </p>
                    {categoryPagination.isFiltered && (
                      <button
                        onClick={categoryPagination.clearAll}
                        className="text-pink-600 hover:text-pink-700"
                      >
                        清除搜索条件
                      </button>
                    )}
                  </div>
                ) : (
                  categories.map((category) => (
                  <div key={category.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <h4 className="font-semibold text-lg text-black">{category.name}</h4>
                        <p className="text-gray-600 text-sm mb-2">URL: /{category.slug}</p>
                        <p className="text-gray-700 line-clamp-2">
                          {category.description || '暂无描述'}
                        </p>
                        <div className="mt-2 flex items-center gap-4 text-sm text-gray-500">
                          <span>创建时间: {new Date(category.createdAt).toLocaleDateString('zh-CN')}</span>
                        </div>
                      </div>
                      <div className="flex gap-2 ml-4">
                        <button
                          onClick={() => handleViewCategory(category)}
                          className="p-2 text-blue-600 hover:bg-blue-50 rounded"
                          title="查看分类详情"
                        >
                          <Eye className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleEditCategory(category)}
                          className="p-2 text-green-600 hover:bg-green-50 rounded"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDeleteCategory(category.id)}
                          className="p-2 text-red-600 hover:bg-red-50 rounded"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                  ))
                )}
              </div>

              {/* 分类分页 */}
              {categoryPagination.totalItems > 0 && (
                <div className="mt-6">
                  <Pagination
                    currentPage={categoryPagination.currentPage}
                    totalPages={categoryPagination.paginationInfo.totalPages}
                    totalItems={categoryPagination.totalItems}
                    itemsPerPage={categoryPagination.pageSize}
                    onPageChange={categoryPagination.setPage}
                    onSizeChange={categoryPagination.setPageSize}
                    showTotal={true}
                    showSizeChanger={true}
                    showQuickJumper={true}
                  />
                </div>
              )}
            </div>
          )}

          {activeTab === 'quiz' && (
            <QuizConfigManager />
          )}
        </div>

        {/* 编辑表单模态框 */}
        {showProductForm && (
          <ProductEditForm
            product={editingProduct}
            onClose={closeProductForm}
            onSave={handleProductSaved}
          />
        )}

        {showArticleForm && (
          <ArticleEditForm
            article={editingArticle}
            presetProductId={editingArticleForProduct?.id}
            onClose={closeArticleForm}
            onSave={handleArticleSaved}
          />
        )}

        {showCategoryForm && (
          <CategoryEditForm
            category={editingCategory}
            onClose={closeCategoryForm}
            onSave={handleCategorySaved}
          />
        )}

        {/* 查看详情模态框 */}
        {showProductView && viewingProduct && (
          <ProductViewModal
            product={viewingProduct}
            onClose={closeProductView}
          />
        )}

        {showArticleView && viewingArticle && (
          <ArticleViewModal
            article={viewingArticle}
            onClose={closeArticleView}
          />
        )}

        {showCategoryView && viewingCategory && (
          <CategoryViewModal
            category={viewingCategory}
            onClose={closeCategoryView}
          />
        )}

        {/* Toast通知 */}
        <ToastContainer toasts={toast.toasts} onRemove={toast.removeToast} />

        {/* 确认对话框 */}
        <ConfirmDialog {...confirmDialog.dialog} />
      </div>
    </div>
  );
}

import Script from 'next/script';

interface SEOHeadProps {
  jsonLd?: object;
  children?: React.ReactNode;
}

export default function SEOHead({ jsonLd, children }: SEOHeadProps) {
  return (
    <>
      {children}
      
      {/* Google Analytics */}
      {process.env.NEXT_PUBLIC_GA_ID && (
        <>
          <Script
            src={`https://www.googletagmanager.com/gtag/js?id=${process.env.NEXT_PUBLIC_GA_ID}`}
            strategy="afterInteractive"
          />
          <Script id="google-analytics" strategy="afterInteractive">
            {`
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', '${process.env.NEXT_PUBLIC_GA_ID}');
            `}
          </Script>
        </>
      )}

      {/* JSON-LD Structured Data */}
      {jsonLd && (
        <Script
          id="json-ld"
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(jsonLd)
          }}
        />
      )}

      {/* Additional SEO Scripts */}
      <Script id="age-verification-schema" type="application/ld+json">
        {`
          {
            "@context": "https://schema.org",
            "@type": "WebSite",
            "name": "Tu Tienda Íntima",
            "url": "${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}",
            "description": "Guía confiable para productos íntimos y bienestar personal en México",
            "inLanguage": "es-MX",
            "audience": {
              "@type": "Audience",
              "audienceType": "Adults",
              "suggestedMinAge": 18
            },
            "potentialAction": {
              "@type": "SearchAction",
              "target": "${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/buscar?q={search_term_string}",
              "query-input": "required name=search_term_string"
            }
          }
        `}
      </Script>
    </>
  );
}

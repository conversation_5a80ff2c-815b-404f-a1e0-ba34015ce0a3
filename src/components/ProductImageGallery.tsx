'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { ChevronLeft, ChevronRight, X, ExternalLink, Star, ShoppingCart, Maximize2, ChevronDown, ChevronUp } from 'lucide-react';
import { Product } from '@/types';
import AmazonButton from './AmazonButton';

interface ProductImageGalleryProps {
  product: Product;
  className?: string;
  showProductInfo?: boolean;
  sticky?: boolean;
}

const DEFAULT_PRODUCT_IMAGE = '/images/product-placeholder.svg';

export default function ProductImageGallery({
  product,
  className = '',
  showProductInfo = true,
  sticky = true
}: ProductImageGalleryProps) {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isLightboxOpen, setIsLightboxOpen] = useState(false);
  const [lightboxImageIndex, setLightboxImageIndex] = useState(0);
  const [imageErrors, setImageErrors] = useState<Set<number>>(new Set());
  const [isProductInfoExpanded, setIsProductInfoExpanded] = useState(false);

  // 处理图片数组，如果没有图片则使用默认占位图
  const images = product.images && product.images.length > 0
    ? product.images
    : [DEFAULT_PRODUCT_IMAGE];

  // 处理图片加载错误
  const handleImageError = (index: number) => {
    setImageErrors(prev => new Set([...prev, index]));
  };

  // 获取有效的图片URL，如果出错则使用占位图
  const getImageSrc = (index: number) => {
    if (imageErrors.has(index) || !images[index]) {
      return DEFAULT_PRODUCT_IMAGE;
    }
    return images[index];
  };

  const nextImage = () => {
    setCurrentImageIndex((prev) => (prev + 1) % images.length);
  };

  const prevImage = () => {
    setCurrentImageIndex((prev) => (prev - 1 + images.length) % images.length);
  };

  const openLightbox = (index: number) => {
    setLightboxImageIndex(index);
    setIsLightboxOpen(true);
  };

  const closeLightbox = () => {
    setIsLightboxOpen(false);
  };

  const nextLightboxImage = () => {
    setLightboxImageIndex((prev) => (prev + 1) % images.length);
  };

  const prevLightboxImage = () => {
    setLightboxImageIndex((prev) => (prev - 1 + images.length) % images.length);
  };

  // 键盘导航
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isLightboxOpen) return;
      
      switch (e.key) {
        case 'Escape':
          closeLightbox();
          break;
        case 'ArrowLeft':
          prevLightboxImage();
          break;
        case 'ArrowRight':
          nextLightboxImage();
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isLightboxOpen]);

  return (
    <>
      <div className={`bg-white rounded-lg shadow-lg overflow-hidden ${sticky ? 'sticky top-4' : ''} ${className}`}>
        {/* 主图片区域 */}
        <div className="relative">
          <div className="aspect-square bg-gray-100 relative overflow-hidden">
            <Image
              src={getImageSrc(currentImageIndex)}
              alt={`${product.name} - 图片 ${currentImageIndex + 1}`}
              fill
              className="object-cover cursor-pointer hover:scale-105 transition-transform duration-300"
              onClick={() => openLightbox(currentImageIndex)}
              sizes="(max-width: 768px) 100vw, 33vw"
              onError={() => handleImageError(currentImageIndex)}
              priority={currentImageIndex === 0}
            />
            
            {/* 放大图标 */}
            <button
              onClick={() => openLightbox(currentImageIndex)}
              className="absolute top-3 right-3 bg-black/50 text-white p-2 rounded-full hover:bg-black/70 transition-colors"
              aria-label="放大查看"
            >
              <Maximize2 className="h-4 w-4" />
            </button>

            {/* 导航箭头 */}
            {images.length > 1 && (
              <>
                <button
                  onClick={prevImage}
                  className="absolute left-2 top-1/2 -translate-y-1/2 bg-black/50 text-white p-2 rounded-full hover:bg-black/70 transition-colors"
                  aria-label="上一张图片"
                >
                  <ChevronLeft className="h-4 w-4" />
                </button>
                <button
                  onClick={nextImage}
                  className="absolute right-2 top-1/2 -translate-y-1/2 bg-black/50 text-white p-2 rounded-full hover:bg-black/70 transition-colors"
                  aria-label="下一张图片"
                >
                  <ChevronRight className="h-4 w-4" />
                </button>
              </>
            )}

            {/* 图片指示器 */}
            {images.length > 1 && (
              <div className="absolute bottom-3 left-1/2 -translate-x-1/2 flex gap-1">
                {images.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentImageIndex(index)}
                    className={`w-2 h-2 rounded-full transition-colors ${
                      index === currentImageIndex ? 'bg-white' : 'bg-white/50'
                    }`}
                    aria-label={`查看图片 ${index + 1}`}
                  />
                ))}
              </div>
            )}
          </div>

          {/* 缩略图 */}
          {images.length > 1 && (
            <div className="p-3 bg-gray-50">
              <div className="flex gap-2 overflow-x-auto">
                {images.map((image, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentImageIndex(index)}
                    className={`flex-shrink-0 w-16 h-16 rounded-md overflow-hidden border-2 transition-colors ${
                      index === currentImageIndex
                        ? 'border-pink-500'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <Image
                      src={getImageSrc(index)}
                      alt={`${product.name} - 缩略图 ${index + 1}`}
                      width={64}
                      height={64}
                      className="w-full h-full object-cover"
                      onError={() => handleImageError(index)}
                    />
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* 产品信息 */}
        {showProductInfo && (
          <div>
            {/* 产品标题和基本信息 - 始终显示 */}
            <div className="p-4 pb-2">
              <div className="relative group">
                <h3 className="font-bold text-lg text-gray-900 mb-2 line-clamp-2 cursor-help">
                  {product.name}
                </h3>
                {/* Tooltip for full product name */}
                <div className="absolute bottom-full left-0 mb-2 px-3 py-2 bg-gray-900 text-white text-sm rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-10 max-w-xs whitespace-normal">
                  {product.name}
                  <div className="absolute top-full left-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
                </div>
              </div>

              {/* 评分显示 - 只有当评分大于0时才显示 */}
              {product.rating > 0 && (
                <div className="flex items-center gap-2 mb-3">
                  <div className="flex items-center">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        className={`h-4 w-4 ${
                          i < Math.floor(product.rating)
                            ? 'text-yellow-400 fill-current'
                            : 'text-gray-300'
                        }`}
                      />
                    ))}
                  </div>
                  <span className="text-sm text-gray-600">
                    ({product.rating.toFixed(1)})
                  </span>
                </div>
              )}
            </div>

            {/* 可折叠的详细信息区域 */}
            {product.features && (
              <div className="px-4">
                <button
                  onClick={() => setIsProductInfoExpanded(!isProductInfoExpanded)}
                  className="flex items-center justify-between w-full py-2 text-left text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors"
                >
                  <span>Características del producto</span>
                  {isProductInfoExpanded ? (
                    <ChevronUp className="h-4 w-4" />
                  ) : (
                    <ChevronDown className="h-4 w-4" />
                  )}
                </button>

                <div className={`overflow-hidden transition-all duration-300 ease-in-out ${
                  isProductInfoExpanded ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
                }`}>
                  <div className="pb-4">
                    <p className="text-sm text-gray-600">
                      {product.features}
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* CTA按钮区域 - 始终显示且固定在底部 */}
            <div className="p-4 pt-2 border-t border-gray-100 bg-white">
              <div className="space-y-2">
                <AmazonButton
                  product={product}
                  variant="primary"
                  className="w-full"
                />
                <a
                  href="https://www.amazon.com.mx/s?me=AABH0E2XGWJR6&marketplaceID=A1AM78C64UM0Y8"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center justify-center gap-2 w-full px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm"
                >
                  <ExternalLink className="h-4 w-4" />
                  Explorar Nuestra Tienda
                </a>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Lightbox */}
      {isLightboxOpen && (
        <div className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4">
          <div className="relative max-w-4xl max-h-full">
            <Image
              src={getImageSrc(lightboxImageIndex)}
              alt={`${product.name} - 图片 ${lightboxImageIndex + 1}`}
              width={800}
              height={800}
              className="max-w-full max-h-full object-contain"
              onError={() => handleImageError(lightboxImageIndex)}
            />
            
            {/* 关闭按钮 */}
            <button
              onClick={closeLightbox}
              className="absolute top-4 right-4 bg-black/50 text-white p-2 rounded-full hover:bg-black/70 transition-colors"
              aria-label="关闭"
            >
              <X className="h-6 w-6" />
            </button>

            {/* 导航按钮 */}
            {images.length > 1 && (
              <>
                <button
                  onClick={prevLightboxImage}
                  className="absolute left-4 top-1/2 -translate-y-1/2 bg-black/50 text-white p-3 rounded-full hover:bg-black/70 transition-colors"
                  aria-label="上一张图片"
                >
                  <ChevronLeft className="h-6 w-6" />
                </button>
                <button
                  onClick={nextLightboxImage}
                  className="absolute right-4 top-1/2 -translate-y-1/2 bg-black/50 text-white p-3 rounded-full hover:bg-black/70 transition-colors"
                  aria-label="下一张图片"
                >
                  <ChevronRight className="h-6 w-6" />
                </button>
              </>
            )}

            {/* 图片计数器 */}
            <div className="absolute bottom-4 left-1/2 -translate-x-1/2 bg-black/50 text-white px-3 py-1 rounded-full text-sm">
              {lightboxImageIndex + 1} / {images.length}
            </div>
          </div>
        </div>
      )}
    </>
  );
}

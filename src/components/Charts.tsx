'use client';

import { 
  LineChart, 
  Line, 
  AreaChart, 
  Area, 
  BarChart, 
  Bar, 
  PieChart, 
  Pie, 
  Cell, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  <PERSON><PERSON><PERSON>, 
  Legend, 
  ResponsiveContainer 
} from 'recharts';

// 颜色主题
const COLORS = {
  primary: '#ec4899', // pink-600
  secondary: '#f97316', // orange-500
  success: '#10b981', // emerald-500
  warning: '#f59e0b', // amber-500
  info: '#3b82f6', // blue-500
  purple: '#8b5cf6', // violet-500
  gray: '#6b7280' // gray-500
};

const PIE_COLORS = [COLORS.primary, COLORS.secondary, COLORS.success, COLORS.warning, COLORS.info, COLORS.purple];

// 时间趋势图表
interface TimeTrendData {
  date: string;
  products: number;
  articles: number;
  pageViews?: number;
  amazonClicks?: number;
}

interface TimeTrendChartProps {
  data: TimeTrendData[];
  title?: string;
  height?: number;
}

export function TimeTrendChart({ data, title = "时间趋势", height = 300 }: TimeTrendChartProps) {
  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">{title}</h3>
      <ResponsiveContainer width="100%" height={height}>
        <LineChart data={data}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis 
            dataKey="date" 
            tick={{ fontSize: 12 }}
            tickFormatter={(value) => new Date(value).toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })}
          />
          <YAxis tick={{ fontSize: 12 }} />
          <Tooltip 
            labelFormatter={(value) => new Date(value).toLocaleDateString('zh-CN')}
            formatter={(value, name) => [value, name === 'products' ? '产品' : name === 'articles' ? '文章' : name === 'pageViews' ? '页面访问' : 'Amazon点击']}
          />
          <Legend 
            formatter={(value) => value === 'products' ? '产品' : value === 'articles' ? '文章' : value === 'pageViews' ? '页面访问' : 'Amazon点击'}
          />
          <Line 
            type="monotone" 
            dataKey="products" 
            stroke={COLORS.primary} 
            strokeWidth={2}
            dot={{ fill: COLORS.primary, strokeWidth: 2, r: 4 }}
          />
          <Line 
            type="monotone" 
            dataKey="articles" 
            stroke={COLORS.secondary} 
            strokeWidth={2}
            dot={{ fill: COLORS.secondary, strokeWidth: 2, r: 4 }}
          />
          {data.some(d => d.pageViews !== undefined) && (
            <Line 
              type="monotone" 
              dataKey="pageViews" 
              stroke={COLORS.info} 
              strokeWidth={2}
              dot={{ fill: COLORS.info, strokeWidth: 2, r: 4 }}
            />
          )}
          {data.some(d => d.amazonClicks !== undefined) && (
            <Line 
              type="monotone" 
              dataKey="amazonClicks" 
              stroke={COLORS.success} 
              strokeWidth={2}
              dot={{ fill: COLORS.success, strokeWidth: 2, r: 4 }}
            />
          )}
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
}

// 分类分布饼图
interface CategoryData {
  category: string;
  count: number;
  slug: string;
}

interface CategoryPieChartProps {
  data: CategoryData[];
  title?: string;
  height?: number;
}

export function CategoryPieChart({ data, title = "产品分类分布", height = 300 }: CategoryPieChartProps) {
  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">{title}</h3>
      <ResponsiveContainer width="100%" height={height}>
        <PieChart>
          <Pie
            data={data}
            cx="50%"
            cy="50%"
            labelLine={false}
            label={({ category, percent }) => `${category} ${(percent * 100).toFixed(0)}%`}
            outerRadius={80}
            fill="#8884d8"
            dataKey="count"
          >
            {data.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={PIE_COLORS[index % PIE_COLORS.length]} />
            ))}
          </Pie>
          <Tooltip formatter={(value, name) => [value, '产品数量']} />
        </PieChart>
      </ResponsiveContainer>
    </div>
  );
}

// 访问量统计柱状图
interface PageViewData {
  page: string;
  views: number;
  title?: string;
}

interface PageViewBarChartProps {
  data: PageViewData[];
  title?: string;
  height?: number;
}

export function PageViewBarChart({ data, title = "热门页面", height = 300 }: PageViewBarChartProps) {
  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">{title}</h3>
      <ResponsiveContainer width="100%" height={height}>
        <BarChart data={data} layout="horizontal">
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis type="number" tick={{ fontSize: 12 }} />
          <YAxis 
            type="category" 
            dataKey="page" 
            tick={{ fontSize: 12 }}
            width={100}
            tickFormatter={(value) => value.length > 15 ? `${value.substring(0, 15)}...` : value}
          />
          <Tooltip 
            formatter={(value) => [value, '访问量']}
            labelFormatter={(label) => `页面: ${label}`}
          />
          <Bar dataKey="views" fill={COLORS.primary} />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
}

// 设备类型分布图
interface DeviceData {
  device: string;
  count: number;
  percentage: string;
}

interface DeviceChartProps {
  data: DeviceData[];
  title?: string;
  height?: number;
}

export function DeviceChart({ data, title = "设备类型分布", height = 300 }: DeviceChartProps) {
  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">{title}</h3>
      <ResponsiveContainer width="100%" height={height}>
        <PieChart>
          <Pie
            data={data}
            cx="50%"
            cy="50%"
            labelLine={false}
            label={({ device, percentage }) => `${device} ${percentage}%`}
            outerRadius={80}
            fill="#8884d8"
            dataKey="count"
          >
            {data.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={PIE_COLORS[index % PIE_COLORS.length]} />
            ))}
          </Pie>
          <Tooltip formatter={(value, name) => [value, '访问量']} />
        </PieChart>
      </ResponsiveContainer>
    </div>
  );
}

// 转化率趋势图
interface ConversionData {
  date: string;
  pageViews: number;
  amazonClicks: number;
  conversionRate: number;
}

interface ConversionChartProps {
  data: ConversionData[];
  title?: string;
  height?: number;
}

export function ConversionChart({ data, title = "转化率趋势", height = 300 }: ConversionChartProps) {
  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">{title}</h3>
      <ResponsiveContainer width="100%" height={height}>
        <LineChart data={data}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis
            dataKey="date"
            tick={{ fontSize: 12 }}
            tickFormatter={(value) => new Date(value).toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })}
          />
          <YAxis tick={{ fontSize: 12 }} />
          <Tooltip
            labelFormatter={(value) => new Date(value).toLocaleDateString('zh-CN')}
            formatter={(value, name) => [
              name === 'conversionRate' ? `${value}%` : value,
              name === 'pageViews' ? '页面访问' : name === 'amazonClicks' ? 'Amazon点击' : '转化率'
            ]}
          />
          <Legend
            formatter={(value) => value === 'pageViews' ? '页面访问' : value === 'amazonClicks' ? 'Amazon点击' : '转化率'}
          />
          <Line
            type="monotone"
            dataKey="pageViews"
            stroke={COLORS.info}
            strokeWidth={2}
            dot={{ fill: COLORS.info, strokeWidth: 2, r: 4 }}
          />
          <Line
            type="monotone"
            dataKey="amazonClicks"
            stroke={COLORS.success}
            strokeWidth={2}
            dot={{ fill: COLORS.success, strokeWidth: 2, r: 4 }}
          />
          <Line
            type="monotone"
            dataKey="conversionRate"
            stroke={COLORS.warning}
            strokeWidth={3}
            dot={{ fill: COLORS.warning, strokeWidth: 2, r: 4 }}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
}

// 综合统计仪表板
interface DashboardData {
  timeTrend: TimeTrendData[];
  categoryDistribution: CategoryData[];
  topPages: PageViewData[];
  deviceBreakdown: DeviceData[];
  conversionTrend: ConversionData[];
}

interface AnalyticsDashboardProps {
  data: DashboardData;
}

export function AnalyticsDashboard({ data }: AnalyticsDashboardProps) {
  return (
    <div className="space-y-6">
      {/* 第一行：时间趋势和分类分布 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <TimeTrendChart data={data.timeTrend} />
        <CategoryPieChart data={data.categoryDistribution} />
      </div>
      
      {/* 第二行：热门页面和设备分布 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <PageViewBarChart data={data.topPages} />
        <DeviceChart data={data.deviceBreakdown} />
      </div>
      
      {/* 第三行：转化率趋势 */}
      <div className="grid grid-cols-1 gap-6">
        <ConversionChart data={data.conversionTrend} />
      </div>
    </div>
  );
}

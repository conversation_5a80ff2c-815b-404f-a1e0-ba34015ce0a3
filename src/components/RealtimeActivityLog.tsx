'use client';

import React from 'react';
import { <PERSON>, MousePointer, Users, Clock, ExternalLink } from 'lucide-react';
import { useRealtimeEvents, RealtimeEvent } from '@/lib/realtime';

// 事件图标映射
const getEventIcon = (type: RealtimeEvent['type']) => {
  switch (type) {
    case 'page_view':
      return Eye;
    case 'amazon_click':
      return ExternalLink;
    case 'new_visitor':
      return Users;
    case 'stats_update':
      return MousePointer;
    default:
      return Clock;
  }
};

// 事件颜色映射
const getEventColor = (type: RealtimeEvent['type']) => {
  switch (type) {
    case 'page_view':
      return 'text-blue-600 bg-blue-100';
    case 'amazon_click':
      return 'text-orange-600 bg-orange-100';
    case 'new_visitor':
      return 'text-green-600 bg-green-100';
    case 'stats_update':
      return 'text-purple-600 bg-purple-100';
    default:
      return 'text-gray-600 bg-gray-100';
  }
};

// 事件描述
const getEventDescription = (event: RealtimeEvent) => {
  switch (event.type) {
    case 'page_view':
      return {
        title: '页面访问',
        description: `访问了 ${event.data.pagePath}`,
        details: event.data.pageTitle || '无标题'
      };
    case 'amazon_click':
      return {
        title: 'Amazon点击',
        description: `点击了 ${event.data.productName || '产品'}`,
        details: event.data.pagePath || '未知页面'
      };
    case 'new_visitor':
      return {
        title: '新访客',
        description: '新用户访问网站',
        details: event.data.sessionId || '未知会话'
      };
    case 'stats_update':
      return {
        title: '数据更新',
        description: '统计数据已更新',
        details: `${event.data.totalPageViews} 访问, ${event.data.totalAmazonClicks} 点击`
      };
    default:
      return {
        title: '未知事件',
        description: '未知活动',
        details: ''
      };
  }
};

// 格式化时间
const formatTime = (date: Date) => {
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  
  if (diff < 60000) { // 小于1分钟
    return '刚刚';
  } else if (diff < 3600000) { // 小于1小时
    return `${Math.floor(diff / 60000)}分钟前`;
  } else if (diff < 86400000) { // 小于1天
    return `${Math.floor(diff / 3600000)}小时前`;
  } else {
    return date.toLocaleDateString();
  }
};

interface RealtimeActivityLogProps {
  maxEvents?: number;
  showHeader?: boolean;
  className?: string;
}

export default function RealtimeActivityLog({ 
  maxEvents = 10, 
  showHeader = true,
  className = ''
}: RealtimeActivityLogProps) {
  const { events, isConnected, clearEvents } = useRealtimeEvents();

  const displayEvents = events.slice(0, maxEvents);

  return (
    <div className={`bg-white rounded-lg shadow ${className}`}>
      {showHeader && (
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Clock className="h-5 w-5 text-gray-600" />
              <h3 className="text-lg font-semibold text-gray-900">实时活动</h3>
              <div className={`flex items-center gap-1 text-sm ${isConnected ? 'text-green-600' : 'text-gray-400'}`}>
                <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500 animate-pulse' : 'bg-gray-400'}`} />
                <span>{isConnected ? '在线' : '离线'}</span>
              </div>
            </div>
            {events.length > 0 && (
              <button
                onClick={clearEvents}
                className="text-sm text-gray-500 hover:text-gray-700 transition-colors"
              >
                清空
              </button>
            )}
          </div>
        </div>
      )}

      <div className="p-4">
        {displayEvents.length === 0 ? (
          <div className="text-center py-8">
            <Clock className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <p className="text-gray-500">暂无活动记录</p>
            <p className="text-sm text-gray-400 mt-1">
              {isConnected ? '等待用户活动...' : '连接已断开'}
            </p>
          </div>
        ) : (
          <div className="space-y-3">
            {displayEvents.map((event, index) => {
              const Icon = getEventIcon(event.type);
              const colorClass = getEventColor(event.type);
              const eventInfo = getEventDescription(event);
              
              return (
                <div
                  key={`${event.timestamp.getTime()}-${index}`}
                  className="flex items-start gap-3 p-3 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <div className={`flex-shrink-0 p-2 rounded-full ${colorClass}`}>
                    <Icon className="h-4 w-4" />
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <p className="text-sm font-medium text-gray-900">
                        {eventInfo.title}
                      </p>
                      <span className="text-xs text-gray-500">
                        {formatTime(event.timestamp)}
                      </span>
                    </div>
                    
                    <p className="text-sm text-gray-600 mt-1">
                      {eventInfo.description}
                    </p>
                    
                    {eventInfo.details && (
                      <p className="text-xs text-gray-500 mt-1 truncate">
                        {eventInfo.details}
                      </p>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        )}

        {events.length > maxEvents && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <p className="text-sm text-gray-500 text-center">
              显示最近 {maxEvents} 条活动，共 {events.length} 条
            </p>
          </div>
        )}
      </div>
    </div>
  );
}

// 紧凑版活动日志
export function CompactActivityLog({ className = '' }: { className?: string }) {
  const { events } = useRealtimeEvents();
  const recentEvents = events.slice(0, 5);

  if (recentEvents.length === 0) {
    return null;
  }

  return (
    <div className={`bg-white rounded-lg shadow p-4 ${className}`}>
      <h4 className="text-sm font-medium text-gray-900 mb-3">最近活动</h4>
      <div className="space-y-2">
        {recentEvents.map((event, index) => {
          const Icon = getEventIcon(event.type);
          const colorClass = getEventColor(event.type);
          const eventInfo = getEventDescription(event);
          
          return (
            <div key={`${event.timestamp.getTime()}-${index}`} className="flex items-center gap-2">
              <div className={`flex-shrink-0 p-1 rounded ${colorClass}`}>
                <Icon className="h-3 w-3" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-xs text-gray-600 truncate">
                  {eventInfo.description}
                </p>
              </div>
              <span className="text-xs text-gray-400">
                {formatTime(event.timestamp)}
              </span>
            </div>
          );
        })}
      </div>
    </div>
  );
}

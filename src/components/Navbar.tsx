'use client';

import Link from 'next/link';
import { useState } from 'react';
import { Menu, X } from 'lucide-react';

export default function Navbar() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const categories = [
    { name: 'Vibradores', href: '/categoria/vibradores' },
    { name: 'Balas Vibradoras', href: '/categoria/balas-vibradoras' },
    { name: 'Pinzas para Pezones', href: '/categoria/pinzas-pezones' },
    { name: 'Succionadores', href: '/categoria/succionadores' },
    { name: 'Anillo<PERSON>', href: '/categoria/anillos' },
    { name: 'Ma<PERSON><PERSON>bad<PERSON>', href: '/categoria/masturbadores' },
  ];

  return (
    <nav className="bg-white shadow-lg sticky top-0 z-40">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-20">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-3">
            <div className="flex items-center space-x-2">
              <div className="w-10 h-10 bg-gradient-to-br from-pink-500 to-purple-600 rounded-full flex items-center justify-center">
                <span className="text-white font-bold text-lg">P</span>
              </div>
              <div className="hidden sm:block">
                <div className="text-xl font-bold text-gray-900">PlacerViva</div>
                <div className="text-xs text-pink-600 font-medium">Tu Asesor de Placer Personal</div>
              </div>
            </div>
          </Link>

          {/* Desktop Menu */}
          <div className="hidden md:flex items-center space-x-8">
            <Link href="/" className="flex items-center space-x-1 text-gray-700 hover:text-pink-600 transition-colors">
              <span>🏠</span>
              <span>Inicio</span>
            </Link>
            <div className="relative group">
              <button className="flex items-center space-x-1 text-gray-700 hover:text-pink-600 transition-colors">
                <span>🛍️</span>
                <span>Categorías</span>
              </button>
              <div className="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                <div className="py-1">
                  {categories.map((category) => (
                    <Link
                      key={category.href}
                      href={category.href}
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-pink-50 hover:text-pink-600"
                    >
                      {category.name}
                    </Link>
                  ))}
                </div>
              </div>
            </div>
            <Link href="/guias" className="flex items-center space-x-1 text-gray-700 hover:text-pink-600 transition-colors">
              <span>📖</span>
              <span>Guías</span>
            </Link>
            <Link href="/quiz" className="flex items-center space-x-1 text-gray-700 hover:text-pink-600 transition-colors">
              <span>🎯</span>
              <span>Quiz Divertido</span>
            </Link>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="text-gray-700 hover:text-pink-600"
            >
              {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t">
              <Link
                href="/"
                className="flex items-center space-x-2 px-3 py-2 text-gray-700 hover:text-pink-600"
                onClick={() => setIsMenuOpen(false)}
              >
                <span>🏠</span>
                <span>Inicio</span>
              </Link>
              {categories.map((category) => (
                <Link
                  key={category.href}
                  href={category.href}
                  className="block px-3 py-2 text-gray-700 hover:text-pink-600"
                  onClick={() => setIsMenuOpen(false)}
                >
                  {category.name}
                </Link>
              ))}
              <Link
                href="/guias"
                className="flex items-center space-x-2 px-3 py-2 text-gray-700 hover:text-pink-600"
                onClick={() => setIsMenuOpen(false)}
              >
                <span>📖</span>
                <span>Guías</span>
              </Link>
              <Link
                href="/quiz"
                className="flex items-center space-x-2 px-3 py-2 text-gray-700 hover:text-pink-600"
                onClick={() => setIsMenuOpen(false)}
              >
                <span>🎯</span>
                <span>Quiz Divertido</span>
              </Link>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
}

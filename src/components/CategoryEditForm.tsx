'use client';

import { useState, useEffect } from 'react';
import { X, Save, Loader2 } from 'lucide-react';
import { Category } from '@/types';
import { categoryService } from '@/lib/database';

interface CategoryEditFormProps {
  category?: Category | null;
  onClose: () => void;
  onSave: (category: Category) => void;
}

interface FormData {
  name: string;
  slug: string;
  description: string;
}

interface FormErrors {
  name?: string;
  slug?: string;
  description?: string;
}

export default function CategoryEditForm({ category, onClose, onSave }: CategoryEditFormProps) {
  const [formData, setFormData] = useState<FormData>({
    name: '',
    slug: '',
    description: ''
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [loading, setLoading] = useState(false);
  const [isEditing] = useState(!!category);

  useEffect(() => {
    if (category) {
      setFormData({
        name: category.name,
        slug: category.slug,
        description: category.description || ''
      });
    }
  }, [category]);

  // 自动生成slug
  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[áàäâ]/g, 'a')
      .replace(/[éèëê]/g, 'e')
      .replace(/[íìïî]/g, 'i')
      .replace(/[óòöô]/g, 'o')
      .replace(/[úùüû]/g, 'u')
      .replace(/ñ/g, 'n')
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  };

  const handleNameChange = (value: string) => {
    setFormData(prev => ({
      ...prev,
      name: value,
      slug: isEditing ? prev.slug : generateSlug(value)
    }));
  };

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = '分类名称不能为空';
    } else if (formData.name.length < 2) {
      newErrors.name = '分类名称至少需要2个字符';
    } else if (formData.name.length > 100) {
      newErrors.name = '分类名称不能超过100个字符';
    }

    if (!formData.slug.trim()) {
      newErrors.slug = 'URL别名不能为空';
    } else if (!/^[a-z0-9-]+$/.test(formData.slug)) {
      newErrors.slug = 'URL别名只能包含小写字母、数字和连字符';
    } else if (formData.slug.length < 2) {
      newErrors.slug = 'URL别名至少需要2个字符';
    } else if (formData.slug.length > 100) {
      newErrors.slug = 'URL别名不能超过100个字符';
    }

    if (formData.description && formData.description.length > 500) {
      newErrors.description = '描述不能超过500个字符';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      let savedCategory: Category;
      
      if (isEditing && category) {
        savedCategory = await categoryService.update(category.id, {
          name: formData.name.trim(),
          slug: formData.slug.trim(),
          description: formData.description.trim() || undefined
        });
      } else {
        savedCategory = await categoryService.create({
          name: formData.name.trim(),
          slug: formData.slug.trim(),
          description: formData.description.trim() || undefined
        });
      }
      
      onSave(savedCategory);
    } catch (error: any) {
      console.error('Error saving category:', error);
      
      // 处理常见错误
      if (error.message?.includes('duplicate key') || error.code === '23505') {
        if (error.message?.includes('slug')) {
          setErrors({ slug: '该URL别名已存在，请使用其他别名' });
        } else if (error.message?.includes('name')) {
          setErrors({ name: '该分类名称已存在，请使用其他名称' });
        } else {
          setErrors({ name: '分类信息重复，请检查名称和URL别名' });
        }
      } else {
        setErrors({ name: '保存失败，请重试' });
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg w-full max-w-2xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="bg-pink-600 text-white p-4 flex justify-between items-center">
          <h3 className="text-lg font-semibold">
            {isEditing ? '编辑分类' : '新建分类'}
          </h3>
          <button
            onClick={onClose}
            className="text-white hover:text-pink-200 text-2xl"
            disabled={loading}
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          {/* 分类名称 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              分类名称 <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => handleNameChange(e.target.value)}
              className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-500 ${
                errors.name ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="例如：Vibradores"
              disabled={loading}
            />
            {errors.name && (
              <p className="mt-1 text-sm text-red-600">{errors.name}</p>
            )}
          </div>

          {/* URL别名 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              URL别名 <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              value={formData.slug}
              onChange={(e) => setFormData(prev => ({ ...prev, slug: e.target.value }))}
              className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-500 ${
                errors.slug ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="例如：vibradores"
              disabled={loading}
            />
            {errors.slug && (
              <p className="mt-1 text-sm text-red-600">{errors.slug}</p>
            )}
            <p className="mt-1 text-sm text-gray-500">
              用于URL路径，只能包含小写字母、数字和连字符
            </p>
          </div>

          {/* 描述 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              描述
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              rows={4}
              className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-500 resize-none ${
                errors.description ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="分类的详细描述，用于SEO和用户了解..."
              disabled={loading}
            />
            {errors.description && (
              <p className="mt-1 text-sm text-red-600">{errors.description}</p>
            )}
            <p className="mt-1 text-sm text-gray-500">
              {formData.description.length}/500 字符
            </p>
          </div>

          {/* 按钮 */}
          <div className="flex justify-end gap-3 pt-4 border-t">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50"
              disabled={loading}
            >
              取消
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-pink-600 text-white rounded-lg hover:bg-pink-700 flex items-center gap-2 disabled:opacity-50"
              disabled={loading}
            >
              {loading ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  保存中...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4" />
                  {isEditing ? '更新分类' : '创建分类'}
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

'use client';

import { useState, useEffect } from 'react';
import { X, Save, Loader2, <PERSON>, EyeOff, Plus, Trash2, AlertCircle } from 'lucide-react';
import { Article, Product } from '@/types';
import { articleService, productService } from '@/lib/database';
import { validateArticle, sanitizeArticleData, generateSlug, ArticleFormData } from '@/lib/validation';
import ConfirmDialog, { useConfirmDialog } from './ConfirmDialog';
import MarkdownRenderer from './MarkdownRenderer';
import { getArticleCoverImage } from '@/lib/utils';
import { ArticleCoverThumbnail } from './ArticleCover';
import SingleImageUpload from './SingleImageUpload';

interface ArticleEditFormProps {
  article?: Article | null;
  presetProductId?: string; // 预设的产品ID，用于从产品管理创建文章
  onClose: () => void;
  onSave: (article: Article) => void;
}

interface FormData {
  title: string;
  content: string;
  excerpt: string;
  slug: string;
  productId: string;
  coverImage: string;
  videoUrl: string;
  metaTitle: string;
  metaDescription: string;
  keywords: string[];
  published: boolean;
}

interface FormErrors {
  title?: string;
  content?: string;
  excerpt?: string;
  slug?: string;
  productId?: string;
  coverImage?: string;
  videoUrl?: string;
  metaTitle?: string;
  metaDescription?: string;
  keywords?: string;
}

export default function ArticleEditForm({ article, presetProductId, onClose, onSave }: ArticleEditFormProps) {
  const [formData, setFormData] = useState<FormData>({
    title: '',
    content: '',
    excerpt: '',
    slug: '',
    productId: '',
    coverImage: '',
    videoUrl: '',
    metaTitle: '',
    metaDescription: '',
    keywords: [],
    published: false
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [loading, setLoading] = useState(false);
  const [products, setProducts] = useState<Product[]>([]);
  const [newKeyword, setNewKeyword] = useState('');
  const [previewMode, setPreviewMode] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  const confirmDialog = useConfirmDialog();

  const isEditing = !!article;

  useEffect(() => {
    loadProducts();
    if (article) {
      setFormData({
        title: article.title || '',
        content: article.content || '',
        excerpt: article.excerpt || '',
        slug: article.slug || '',
        productId: article.productId || '',
        coverImage: article.coverImage || '',
        videoUrl: article.videoUrl || '',
        metaTitle: article.metaTitle || '',
        metaDescription: article.metaDescription || '',
        keywords: article.keywords || [],
        published: article.published || false
      });
    } else if (presetProductId) {
      // 如果有预设的产品ID，设置到表单中
      setFormData(prev => ({
        ...prev,
        productId: presetProductId
      }));
    }
  }, [article, presetProductId]);

  const loadProducts = async () => {
    try {
      const data = await productService.getAll();
      setProducts(data);
    } catch (error) {
      console.error('Error loading products:', error);
    }
  };

  // 使用导入的generateSlug函数

  const validateForm = (): boolean => {
    const validation = validateArticle(formData);
    setErrors(validation.errors);
    return validation.isValid;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      const articleData = {
        ...sanitizeArticleData(formData),
        published: formData.published
      };

      let savedArticle: Article;
      if (isEditing && article) {
        savedArticle = await articleService.update(article.id, articleData);
      } else {
        savedArticle = await articleService.create(articleData);
      }

      onSave(savedArticle);
      onClose();
    } catch (error) {
      console.error('Error saving article:', error);

      // 更友好的错误处理
      let errorMessage = '保存文章时出错，请重试';
      if (error instanceof Error) {
        if (error.message.includes('duplicate')) {
          errorMessage = '文章链接已存在，请使用不同的链接';
          setErrors({ slug: errorMessage });
        } else if (error.message.includes('network')) {
          errorMessage = '网络连接错误，请检查网络后重试';
          setErrors({ title: errorMessage });
        } else if (error.message.includes('permission')) {
          errorMessage = '权限不足，请联系管理员';
          setErrors({ title: errorMessage });
        } else {
          setErrors({ title: errorMessage });
        }
      } else {
        setErrors({ title: errorMessage });
      }
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof FormData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setHasChanges(true);

    // 自动生成slug
    if (field === 'title' && typeof value === 'string') {
      const slug = generateSlug(value);
      setFormData(prev => ({ ...prev, slug }));
    }

    if (errors[field as keyof FormErrors]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const handleClose = () => {
    if (hasChanges) {
      confirmDialog.showConfirm({
        title: '确认关闭',
        message: '您有未保存的更改，确定要关闭吗？',
        confirmText: '关闭',
        cancelText: '继续编辑',
        type: 'warning',
        onConfirm: onClose
      });
    } else {
      onClose();
    }
  };

  const addKeyword = () => {
    if (newKeyword.trim() && !formData.keywords.includes(newKeyword.trim())) {
      setFormData(prev => ({
        ...prev,
        keywords: [...prev.keywords, newKeyword.trim()]
      }));
      setNewKeyword('');
      setHasChanges(true);
      if (errors.keywords) {
        setErrors(prev => ({ ...prev, keywords: undefined }));
      }
    }
  };

  const removeKeyword = (index: number) => {
    setFormData(prev => ({
      ...prev,
      keywords: prev.keywords.filter((_, i) => i !== index)
    }));
    setHasChanges(true);
  };

  const selectedProduct = products.find(p => p.id === formData.productId);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg w-full max-w-6xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="bg-pink-600 text-white p-4 flex justify-between items-center">
          <h2 className="text-xl font-bold">
            {isEditing ? '编辑文章' : '新建文章'}
          </h2>
          <div className="flex items-center gap-4">
            <button
              type="button"
              onClick={() => setPreviewMode(!previewMode)}
              className="flex items-center gap-2 px-3 py-1 bg-pink-700 rounded-lg hover:bg-pink-800"
            >
              {previewMode ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              {previewMode ? '编辑' : '预览'}
            </button>
            <button
              onClick={handleClose}
              className="text-white hover:text-pink-200 text-2xl"
              disabled={loading}
            >
              <X className="h-6 w-6" />
            </button>
          </div>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 overflow-y-auto max-h-[calc(90vh-80px)]">
          {!previewMode ? (
            <div className="space-y-6">
              {/* 基本信息 */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* 文章标题 */}
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    文章标题 *
                  </label>
                  <input
                    type="text"
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent text-gray-900 placeholder-gray-500 ${
                      errors.title ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="请输入文章标题"
                    disabled={loading}
                  />
                  {errors.title && <p className="mt-1 text-sm text-red-600">{errors.title}</p>}
                </div>

                {/* 文章链接 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    文章链接 (slug) *
                  </label>
                  <input
                    type="text"
                    value={formData.slug}
                    onChange={(e) => handleInputChange('slug', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent text-gray-900 placeholder-gray-500 ${
                      errors.slug ? 'border-red-500' : 'border-gray-300'
                    }`}
                    placeholder="article-slug"
                    disabled={loading}
                  />
                  {errors.slug && <p className="mt-1 text-sm text-red-600">{errors.slug}</p>}
                </div>

                {/* 关联产品 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    关联产品 *
                  </label>
                  <select
                    value={formData.productId}
                    onChange={(e) => handleInputChange('productId', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent text-gray-900 ${
                      errors.productId ? 'border-red-500' : 'border-gray-300'
                    } ${presetProductId ? 'bg-gray-100' : ''}`}
                    disabled={loading || !!presetProductId}
                  >
                    <option value="">请选择产品</option>
                    {products.map((product) => (
                      <option key={product.id} value={product.id}>
                        {product.name}
                      </option>
                    ))}
                  </select>
                  {presetProductId && (
                    <p className="mt-1 text-sm text-blue-600">
                      已为指定产品创建文章，无法更改关联产品
                    </p>
                  )}
                  {errors.productId && <p className="mt-1 text-sm text-red-600">{errors.productId}</p>}
                </div>
              </div>

              {/* 文章摘要 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  文章摘要 *
                </label>
                <textarea
                  value={formData.excerpt}
                  onChange={(e) => handleInputChange('excerpt', e.target.value)}
                  rows={3}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent text-gray-900 placeholder-gray-500 ${
                    errors.excerpt ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="请输入文章摘要..."
                  disabled={loading}
                />
                {errors.excerpt && <p className="mt-1 text-sm text-red-600">{errors.excerpt}</p>}
              </div>

              {/* 封面图片 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  封面图片
                </label>
                <SingleImageUpload
                  imageUrl={formData.coverImage}
                  onChange={(imageUrl) => handleInputChange('coverImage', imageUrl)}
                  maxSizeInMB={10}
                  disabled={loading}
                  placeholder="上传文章封面图片"
                />
                <p className="mt-1 text-sm text-gray-500">
                  可选：上传图片作为文章封面。如果不提供，将使用关联产品的第一张图片作为默认封面。
                </p>
                {errors.coverImage && <p className="mt-1 text-sm text-red-600">{errors.coverImage}</p>}

                {/* 封面图片预览 */}
                <div className="mt-3">
                  <p className="text-sm font-medium text-gray-700 mb-2">封面预览：</p>
                  <div className="relative w-full max-w-md">
                    {formData.coverImage ? (
                      <img
                        src={formData.coverImage}
                        alt="自定义封面预览"
                        className="w-full h-32 object-cover rounded-lg border border-gray-200"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.style.display = 'none';
                        }}
                      />
                    ) : (
                      <div className="w-full h-32 bg-gray-100 rounded-lg border border-gray-200 flex items-center justify-center">
                        {formData.productId && products.find(p => p.id === formData.productId) ? (
                          <div className="text-center">
                            <p className="text-sm text-gray-600 mb-2">将使用产品图片作为默认封面</p>
                            <ArticleCoverThumbnail
                              src={getArticleCoverImage({
                                coverImage: '',
                                product: products.find(p => p.id === formData.productId)
                              } as Article)}
                              alt="默认封面预览"
                              className="w-20 h-20 mx-auto"
                            />
                          </div>
                        ) : (
                          <p className="text-sm text-gray-500">选择产品后将显示默认封面预览</p>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* 产品视频URL */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  产品展示视频URL
                </label>
                <input
                  type="url"
                  value={formData.videoUrl}
                  onChange={(e) => handleInputChange('videoUrl', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent text-gray-900 placeholder-gray-500 ${
                    errors.videoUrl ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="https://example.com/video.mp4"
                  disabled={loading}
                />
                <p className="mt-1 text-sm text-gray-500">
                  可选：输入产品展示视频的URL。支持MP4、WebM等格式。建议使用专业视频托管服务如Cloudinary。
                </p>
                {errors.videoUrl && <p className="mt-1 text-sm text-red-600">{errors.videoUrl}</p>}

                {/* 视频预览 */}
                {formData.videoUrl && (
                  <div className="mt-3">
                    <p className="text-sm font-medium text-gray-700 mb-2">视频预览：</p>
                    <div className="relative w-full max-w-md">
                      <video
                        src={formData.videoUrl}
                        poster={formData.coverImage || (formData.productId && products.find(p => p.id === formData.productId)?.images?.[0])}
                        controls
                        preload="metadata"
                        className="w-full h-32 object-cover rounded-lg border border-gray-200"
                        onError={(e) => {
                          const target = e.target as HTMLVideoElement;
                          target.style.display = 'none';
                        }}
                      />
                    </div>
                  </div>
                )}
              </div>

              {/* 文章内容 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  文章内容 * (支持Markdown)
                </label>
                <textarea
                  value={formData.content}
                  onChange={(e) => handleInputChange('content', e.target.value)}
                  rows={12}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent font-mono text-sm text-gray-900 placeholder-gray-500 ${
                    errors.content ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="请输入文章内容（支持Markdown格式）..."
                  disabled={loading}
                />
                {errors.content && <p className="mt-1 text-sm text-red-600">{errors.content}</p>}
              </div>

              {/* SEO设置 */}
              <div className="border-t pt-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">SEO设置</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* SEO标题 */}
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      SEO标题 *
                    </label>
                    <input
                      type="text"
                      value={formData.metaTitle}
                      onChange={(e) => handleInputChange('metaTitle', e.target.value)}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent text-gray-900 placeholder-gray-500 ${
                        errors.metaTitle ? 'border-red-500' : 'border-gray-300'
                      }`}
                      placeholder="SEO标题（建议50-60字符）"
                      disabled={loading}
                    />
                    <p className="mt-1 text-sm text-gray-500">
                      当前长度: {formData.metaTitle.length} 字符
                    </p>
                    {errors.metaTitle && <p className="mt-1 text-sm text-red-600">{errors.metaTitle}</p>}
                  </div>

                  {/* SEO描述 */}
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      SEO描述 *
                    </label>
                    <textarea
                      value={formData.metaDescription}
                      onChange={(e) => handleInputChange('metaDescription', e.target.value)}
                      rows={3}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent text-gray-900 placeholder-gray-500 ${
                        errors.metaDescription ? 'border-red-500' : 'border-gray-300'
                      }`}
                      placeholder="SEO描述（建议150-160字符）"
                      disabled={loading}
                    />
                    <p className="mt-1 text-sm text-gray-500">
                      当前长度: {formData.metaDescription.length} 字符
                    </p>
                    {errors.metaDescription && <p className="mt-1 text-sm text-red-600">{errors.metaDescription}</p>}
                  </div>
                </div>

                {/* 关键词 */}
                <div className="mt-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    SEO关键词 *
                  </label>
                  
                  {/* 已添加的关键词 */}
                  <div className="flex flex-wrap gap-2 mb-3">
                    {formData.keywords.map((keyword, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center gap-1 px-3 py-1 bg-pink-100 text-pink-800 rounded-full text-sm"
                      >
                        {keyword}
                        <button
                          type="button"
                          onClick={() => removeKeyword(index)}
                          className="text-pink-600 hover:text-pink-800"
                          disabled={loading}
                        >
                          <X className="h-3 w-3" />
                        </button>
                      </span>
                    ))}
                  </div>

                  {/* 添加新关键词 */}
                  <div className="flex gap-2">
                    <input
                      type="text"
                      value={newKeyword}
                      onChange={(e) => setNewKeyword(e.target.value)}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent text-gray-900 placeholder-gray-500"
                      placeholder="添加关键词..."
                      disabled={loading}
                      onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addKeyword())}
                    />
                    <button
                      type="button"
                      onClick={addKeyword}
                      className="p-2 bg-pink-600 text-white rounded-lg hover:bg-pink-700 disabled:opacity-50"
                      disabled={loading || !newKeyword.trim()}
                    >
                      <Plus className="h-4 w-4" />
                    </button>
                  </div>
                  {errors.keywords && <p className="mt-1 text-sm text-red-600">{errors.keywords}</p>}
                </div>
              </div>

              {/* 发布设置 */}
              <div className="border-t pt-6">
                <div className="flex items-center gap-3">
                  <input
                    type="checkbox"
                    id="published"
                    checked={formData.published}
                    onChange={(e) => handleInputChange('published', e.target.checked)}
                    className="h-4 w-4 text-pink-600 focus:ring-pink-500 border-gray-300 rounded"
                    disabled={loading}
                  />
                  <label htmlFor="published" className="text-sm font-medium text-gray-700">
                    立即发布文章
                  </label>
                </div>
                <p className="mt-1 text-sm text-gray-500">
                  未发布的文章将保存为草稿，不会在网站上显示
                </p>
              </div>
            </div>
          ) : (
            /* 预览模式 */
            <div className="max-w-none">
              <h1 className="text-3xl font-bold text-gray-900 mb-6">{formData.title}</h1>
              {selectedProduct && (
                <div className="bg-gray-50 p-4 rounded-lg mb-6">
                  <h3 className="text-lg font-semibold text-gray-900">关联产品: {selectedProduct.name}</h3>
                </div>
              )}
              <MarkdownRenderer content={formData.content} />
            </div>
          )}

          {/* 提交按钮 */}
          <div className="mt-8 flex justify-end gap-4 border-t pt-6">
            <button
              type="button"
              onClick={handleClose}
              className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 disabled:opacity-50"
              disabled={loading}
            >
              取消
            </button>
            <button
              type="submit"
              className="px-6 py-2 bg-pink-600 text-white rounded-lg hover:bg-pink-700 disabled:opacity-50 flex items-center gap-2"
              disabled={loading}
            >
              {loading ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  保存中...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4" />
                  {isEditing ? '更新文章' : '创建文章'}
                </>
              )}
            </button>
          </div>
        </form>

        {/* 确认对话框 */}
        <ConfirmDialog {...confirmDialog.dialog} />
      </div>
    </div>
  );
}

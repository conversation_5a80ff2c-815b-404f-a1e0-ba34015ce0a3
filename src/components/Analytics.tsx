'use client';

import { usePageTracking, usePerformanceTracking, useScrollTracking } from '@/hooks/useAnalytics';
import { usePageEngagement } from '@/hooks/usePageEngagement';

interface AnalyticsProps {
  children?: React.ReactNode;
}

/**
 * Analytics组件 - 负责在应用中启用各种分析跟踪功能
 * 
 * 功能包括：
 * - 页面访问跟踪
 * - 性能监控
 * - 滚动深度跟踪
 * - 页面参与度跟踪
 */
export default function Analytics({ children }: AnalyticsProps) {
  // 启用页面访问跟踪
  usePageTracking();

  // 启用性能监控
  usePerformanceTracking();

  // 启用滚动深度跟踪
  useScrollTracking();

  // 启用页面参与度跟踪
  usePageEngagement();

  // 这个组件不渲染任何UI，只是启用跟踪功能
  return children ? <>{children}</> : null;
}

/**
 * 分析数据展示组件 - 用于在管理后台显示分析数据
 */
export function AnalyticsDisplay() {
  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">
        分析数据
      </h3>
      <p className="text-gray-600 text-sm">
        分析数据正在收集中，请稍后查看详细统计信息。
      </p>
    </div>
  );
}

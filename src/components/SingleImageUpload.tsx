'use client';

import { useState, useRef, useCallback } from 'react';
import { Upload, X, Image as ImageIcon, Loader2, AlertCircle } from 'lucide-react';
import Image from 'next/image';
import { supabase } from '@/lib/supabase';

interface SingleImageUploadProps {
  imageUrl: string;
  onChange: (imageUrl: string) => void;
  maxSizeInMB?: number;
  className?: string;
  disabled?: boolean;
  placeholder?: string;
}

interface UploadState {
  uploading: boolean;
  progress: number;
  preview?: string;
  error?: string;
}

export default function SingleImageUpload({
  imageUrl,
  onChange,
  maxSizeInMB = 10,
  className = '',
  disabled = false,
  placeholder = '点击上传图片'
}: SingleImageUploadProps) {
  const [uploadState, setUploadState] = useState<UploadState>({
    uploading: false,
    progress: 0
  });
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 验证文件
  const validateFile = (file: File): string | null => {
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      return '只支持 JPG、PNG、WebP 格式的图片';
    }
    
    const maxSizeInBytes = maxSizeInMB * 1024 * 1024;
    if (file.size > maxSizeInBytes) {
      return `图片大小不能超过 ${maxSizeInMB}MB`;
    }
    
    return null;
  };

  // 生成唯一文件名
  const generateFileName = (file: File): string => {
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 15);
    const extension = file.name.split('.').pop();
    return `article_cover_${timestamp}_${randomString}.${extension}`;
  };

  // 上传文件到Supabase Storage
  const uploadFile = async (file: File): Promise<string> => {
    const fileName = generateFileName(file);
    const filePath = `articles/${fileName}`;

    // 创建预览URL
    const preview = URL.createObjectURL(file);
    
    setUploadState({
      uploading: true,
      progress: 0,
      preview
    });

    try {
      // 模拟上传进度
      const progressInterval = setInterval(() => {
        setUploadState(prev => ({
          ...prev,
          progress: prev.progress < 90 ? prev.progress + 10 : prev.progress
        }));
      }, 200);

      // 上传到Supabase Storage
      const { data, error } = await supabase.storage
        .from('product-images')
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: false
        });

      clearInterval(progressInterval);

      if (error) {
        throw error;
      }

      // 获取公开URL
      const { data: { publicUrl } } = supabase.storage
        .from('product-images')
        .getPublicUrl(filePath);

      // 更新进度为100%
      setUploadState(prev => ({
        ...prev,
        progress: 100
      }));

      return publicUrl;
    } catch (error) {
      console.error('Upload error:', error);
      throw error;
    } finally {
      // 清理预览URL
      URL.revokeObjectURL(preview);
    }
  };

  // 处理文件选择
  const handleFile = useCallback(async (file: File) => {
    if (disabled) return;

    const error = validateFile(file);
    if (error) {
      setUploadState({
        uploading: false,
        progress: 0,
        error
      });
      return;
    }

    try {
      const url = await uploadFile(file);
      onChange(url);
      
      // 显示成功状态
      setUploadState({
        uploading: false,
        progress: 100
      });

      // 清理状态
      setTimeout(() => {
        setUploadState({
          uploading: false,
          progress: 0
        });
      }, 1000);
    } catch (error) {
      const errorMessage = error instanceof Error
        ? `上传失败: ${error.message}`
        : '上传失败，请重试';

      setUploadState({
        uploading: false,
        progress: 0,
        error: errorMessage
      });
    }
  }, [onChange, disabled]);

  // 拖拽处理
  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      handleFile(e.dataTransfer.files[0]);
    }
  }, [handleFile]);

  // 删除图片
  const removeImage = () => {
    onChange('');
    setUploadState({
      uploading: false,
      progress: 0
    });
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* 当前图片显示 */}
      {imageUrl && !uploadState.uploading && (
        <div className="relative group">
          <div className="aspect-video bg-gray-100 rounded-lg border border-gray-200 overflow-hidden">
            <Image
              src={imageUrl}
              alt="封面图片"
              fill
              className="object-cover"
            />
          </div>
          
          {/* 删除按钮 */}
          <button
            type="button"
            onClick={removeImage}
            className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors opacity-0 group-hover:opacity-100"
            disabled={disabled}
          >
            <X className="h-4 w-4" />
          </button>
        </div>
      )}

      {/* 上传区域 */}
      {!imageUrl && (
        <div
          className={`relative border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
            dragActive
              ? 'border-pink-500 bg-pink-50'
              : disabled
              ? 'border-gray-200 bg-gray-50'
              : 'border-gray-300 hover:border-pink-400 hover:bg-pink-50'
          }`}
          onDragEnter={handleDrag}
          onDragLeave={handleDrag}
          onDragOver={handleDrag}
          onDrop={handleDrop}
        >
          <input
            ref={fileInputRef}
            type="file"
            accept="image/jpeg,image/jpg,image/png,image/webp"
            onChange={(e) => e.target.files?.[0] && handleFile(e.target.files[0])}
            className="hidden"
            disabled={disabled}
          />
          
          {uploadState.uploading ? (
            <div className="space-y-2">
              {uploadState.preview && (
                <div className="aspect-video bg-gray-100 rounded-lg border border-gray-200 overflow-hidden mb-4 relative">
                  <Image
                    src={uploadState.preview}
                    alt="上传预览"
                    fill
                    className="object-cover"
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                    <div className="text-center">
                      <Loader2 className="h-8 w-8 text-white animate-spin mx-auto mb-2" />
                      <p className="text-sm text-white">上传中...</p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          ) : uploadState.error ? (
            <div className="space-y-2">
              <AlertCircle className="mx-auto h-12 w-12 text-red-500" />
              <div>
                <p className="text-red-600 font-medium">{uploadState.error}</p>
                <button
                  type="button"
                  onClick={() => fileInputRef.current?.click()}
                  disabled={disabled}
                  className="mt-2 font-medium text-pink-600 hover:text-pink-500"
                >
                  重新上传
                </button>
              </div>
            </div>
          ) : uploadState.progress === 100 ? (
            <div className="space-y-2">
              <div className="h-12 w-12 bg-green-500 rounded-full flex items-center justify-center mx-auto">
                <svg className="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <p className="text-green-600 font-medium">上传完成</p>
            </div>
          ) : (
            <div className="space-y-2">
              <Upload className={`mx-auto h-12 w-12 ${disabled ? 'text-gray-400' : 'text-gray-500'}`} />
              <div>
                <button
                  type="button"
                  onClick={() => fileInputRef.current?.click()}
                  disabled={disabled}
                  className={`font-medium ${
                    disabled
                      ? 'text-gray-400 cursor-not-allowed'
                      : 'text-pink-600 hover:text-pink-500'
                  }`}
                >
                  {placeholder}
                </button>
                <p className="text-gray-500">或拖拽图片到此区域</p>
              </div>
              <p className="text-sm text-gray-500">
                支持 JPG、PNG、WebP 格式，最大 {maxSizeInMB}MB
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  );
}

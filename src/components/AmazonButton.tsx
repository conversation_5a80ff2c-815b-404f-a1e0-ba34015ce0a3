'use client';

import { ExternalLink, ShoppingCart, Star, Truck } from 'lucide-react';
import { Product } from '@/types';
import { useAmazonClickTracking } from '@/hooks/useAnalytics';

interface AmazonButtonProps {
  product: Product;
  variant?: 'primary' | 'secondary' | 'cta' | 'floating';
  size?: 'sm' | 'md' | 'lg';
  showPrice?: boolean;
  showRating?: boolean;
  showFeatures?: boolean;
  className?: string;
  text?: string;
}

export default function AmazonButton({
  product,
  variant = 'primary',
  size = 'md',
  showPrice = true,
  showRating = true,
  showFeatures = false,
  className = '',
  text
}: AmazonButtonProps) {
  const { trackAmazonClick } = useAmazonClickTracking();

  const handleClick = async () => {
    // Track click for analytics (only if product has a valid ID)
    if (product.id && product.id !== 'placeholder') {
      await trackAmazonClick(product.id, product.name, product.amazonUrl);
    } else {
      // For placeholder products, track without product ID
      await trackAmazonClick(undefined, product.name, product.amazonUrl);
    }

    // Legacy Google Analytics tracking (still keep for compatibility)
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'click', {
        event_category: 'Amazon Link',
        event_label: product.name
      });
    }
  };

  const baseClasses = "inline-flex items-center justify-center font-semibold transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2";
  
  const variantClasses = {
    primary: "bg-orange-500 hover:bg-orange-600 text-white focus:ring-orange-500 shadow-lg hover:shadow-xl",
    secondary: "bg-pink-600 hover:bg-pink-700 text-white focus:ring-pink-500 shadow-lg hover:shadow-xl",
    cta: "bg-gradient-to-r from-orange-500 to-pink-600 hover:from-orange-600 hover:to-pink-700 text-white focus:ring-orange-500 shadow-xl hover:shadow-2xl transform hover:-translate-y-1",
    floating: "fixed bottom-6 right-6 bg-orange-500 hover:bg-orange-600 text-white focus:ring-orange-500 shadow-2xl hover:shadow-3xl z-50 rounded-full"
  };

  const sizeClasses = {
    sm: "px-4 py-2 text-sm rounded-lg",
    md: "px-6 py-3 text-base rounded-lg",
    lg: "px-8 py-4 text-lg rounded-xl"
  };

  const buttonText = text || (variant === 'floating' ? '' : 'Ver en Amazon');

  if (variant === 'cta') {
    return (
      <div className={`bg-gradient-to-r from-orange-50 to-pink-50 rounded-2xl p-6 border border-orange-200 ${className}`}>
        <div className="text-center mb-4">
          <h3 className="text-2xl font-bold text-gray-900 mb-2">
            ¿Listo para mejorar tu bienestar íntimo?
          </h3>
          <p className="text-gray-600">
            Miles de personas ya confían en este producto
          </p>
        </div>
        
        <div className="bg-white rounded-xl p-4 mb-4 shadow-sm">
          <div className="flex items-center justify-between mb-3">
            <h4 className="font-semibold text-gray-900">{product.name}</h4>
          </div>

          <div className="mb-3">
            <p className="text-sm text-gray-600">
              Categoría: <span className="font-medium capitalize">{product.category.replace('-', ' ')}</span>
            </p>
          </div>
        </div>

        <a
          href={product.amazonUrl}
          target="_blank"
          rel="noopener noreferrer"
          onClick={handleClick}
          className={`${baseClasses} ${variantClasses.cta} ${sizeClasses.lg} w-full gap-3`}
        >
          <ShoppingCart className="h-6 w-6" />
          Comprar en Amazon México
          <ExternalLink className="h-5 w-5" />
        </a>
        
        <div className="flex items-center justify-center gap-6 mt-4 text-sm text-gray-500">
          <div className="flex items-center gap-1">
            <Truck className="h-4 w-4" />
            <span>Envío rápido</span>
          </div>
          <div className="flex items-center gap-1">
            <span className="w-2 h-2 bg-green-400 rounded-full"></span>
            <span>Pago seguro</span>
          </div>
          <div className="flex items-center gap-1">
            <span className="w-2 h-2 bg-blue-400 rounded-full"></span>
            <span>Garantía Amazon</span>
          </div>
        </div>
      </div>
    );
  }

  if (variant === 'floating') {
    return (
      <a
        href={product.amazonUrl}
        target="_blank"
        rel="noopener noreferrer"
        onClick={handleClick}
        className={`${baseClasses} ${variantClasses.floating} w-16 h-16 ${className}`}
        title={`Ver ${product.name} en Amazon`}
      >
        <ShoppingCart className="h-6 w-6" />
      </a>
    );
  }

  return (
    <a
      href={product.amazonUrl}
      target="_blank"
      rel="noopener noreferrer"
      onClick={handleClick}
      className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} gap-2 ${className}`}
    >
      {variant === 'primary' && <ShoppingCart className="h-5 w-5" />}
      {buttonText}
      <ExternalLink className="h-4 w-4" />
    </a>
  );
}

// Componente para mostrar múltiples opciones de compra
interface AmazonComparisonProps {
  products: Product[];
  title?: string;
  className?: string;
}

export function AmazonComparison({ products, title = "Opciones Recomendadas", className = '' }: AmazonComparisonProps) {
  return (
    <div className={`bg-gray-50 rounded-2xl p-6 ${className}`}>
      <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">
        {title}
      </h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {products.map((product, index) => (
          <div key={product.id} className="bg-white rounded-xl p-4 shadow-sm border">
            <div className="text-center mb-3">
              <h4 className="font-semibold text-gray-900 mb-1 line-clamp-2">
                {product.name}
              </h4>
              <p className="text-sm text-gray-600 mb-3 capitalize">
                {product.category.replace('-', ' ')}
              </p>
            </div>

            <AmazonButton
              product={product}
              variant="secondary"
              size="sm"
              showPrice={false}
              showRating={false}
              text="Ver en Amazon"
              className="w-full"
            />
          </div>
        ))}
      </div>
    </div>
  );
}

// Componente para banner promocional
interface AmazonBannerProps {
  product: Product;
  discount?: string;
  urgency?: string;
  className?: string;
}

export function AmazonBanner({ product, discount, urgency, className = '' }: AmazonBannerProps) {
  return (
    <div className={`bg-gradient-to-r from-red-500 to-pink-600 text-white rounded-xl p-6 ${className}`}>
      <div className="flex flex-col md:flex-row items-center justify-between gap-4">
        <div className="text-center md:text-left">
          <h3 className="text-xl font-bold mb-1">¡Oferta Especial!</h3>
          <p className="text-red-100 mb-2">{product.name}</p>
          {discount && (
            <p className="text-2xl font-bold">{discount} de descuento</p>
          )}
          {urgency && (
            <p className="text-sm text-red-200">{urgency}</p>
          )}
        </div>
        
        <AmazonButton
          product={product}
          variant="primary"
          size="lg"
          showPrice={false}
          showRating={false}
          text="¡Aprovechar Oferta!"
          className="bg-white text-red-600 hover:bg-gray-100"
        />
      </div>
    </div>
  );
}

'use client';

import { X, Calendar, Tag, ExternalLink, Eye, FileText } from 'lucide-react';
import { Article } from '@/types';
import MarkdownRenderer from './MarkdownRenderer';

interface ArticleViewModalProps {
  article: Article;
  onClose: () => void;
}

export default function ArticleViewModal({ article, onClose }: ArticleViewModalProps) {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b">
          <h2 className="text-2xl font-bold text-gray-900">文章详情</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Article Header */}
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6">
            <h3 className="text-2xl font-bold text-gray-900 mb-3">
              {article.title}
            </h3>
            <p className="text-gray-700 text-lg leading-relaxed">
              {article.excerpt}
            </p>
            <div className="flex items-center gap-4 mt-4">
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                article.published 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-yellow-100 text-yellow-800'
              }`}>
                {article.published ? '已发布' : '草稿'}
              </span>
              <span className="text-gray-600 text-sm">
                /{article.slug}
              </span>
            </div>
          </div>

          {/* Details Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <Tag className="h-4 w-4 text-gray-600" />
                <span className="font-medium text-gray-900">文章ID</span>
              </div>
              <p className="text-gray-700 font-mono text-sm">{article.id}</p>
            </div>

            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <Calendar className="h-4 w-4 text-gray-600" />
                <span className="font-medium text-gray-900">创建时间</span>
              </div>
              <p className="text-gray-700">
                {new Date(article.createdAt).toLocaleString('zh-CN')}
              </p>
            </div>

            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <Calendar className="h-4 w-4 text-gray-600" />
                <span className="font-medium text-gray-900">更新时间</span>
              </div>
              <p className="text-gray-700">
                {new Date(article.updatedAt).toLocaleString('zh-CN')}
              </p>
            </div>

            {article.publishedAt && (
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <Calendar className="h-4 w-4 text-gray-600" />
                  <span className="font-medium text-gray-900">发布时间</span>
                </div>
                <p className="text-gray-700">
                  {new Date(article.publishedAt).toLocaleString('zh-CN')}
                </p>
              </div>
            )}
          </div>

          {/* Associated Product */}
          {article.product && (
            <div className="bg-pink-50 rounded-lg p-4 border border-pink-100">
              <div className="flex items-center gap-2 mb-3">
                <Tag className="h-4 w-4 text-pink-600" />
                <span className="font-medium text-gray-900">关联产品</span>
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-gray-900">{article.product.name}</p>
                  <p className="text-gray-600 text-sm capitalize">
                    {article.product.category.replace('-', ' ')}
                  </p>
                </div>
                <a
                  href={article.product.amazonUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-pink-600 hover:text-pink-700 text-sm font-medium flex items-center gap-1"
                >
                  查看产品
                  <ExternalLink className="h-3 w-3" />
                </a>
              </div>
            </div>
          )}

          {/* Keywords */}
          {article.keywords.length > 0 && (
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-3">
                <Tag className="h-4 w-4 text-gray-600" />
                <span className="font-medium text-gray-900">关键词</span>
              </div>
              <div className="flex flex-wrap gap-2">
                {article.keywords.map((keyword, index) => (
                  <span
                    key={index}
                    className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm"
                  >
                    {keyword}
                  </span>
                ))}
              </div>
            </div>
          )}

          {/* Content Preview */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-3">
              <FileText className="h-4 w-4 text-gray-600" />
              <span className="font-medium text-gray-900">内容预览</span>
            </div>
            <div className="max-h-40 overflow-y-auto bg-white rounded border p-3">
              <MarkdownRenderer
                content={article.content.substring(0, 500) + (article.content.length > 500 ? '...' : '')}
                className="text-sm"
              />
            </div>
          </div>

          {/* Action Buttons */}
          {article.published && (
            <div className="border-t pt-6">
              <div className="flex justify-center">
                <a
                  href={`/articulo/${article.slug}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center gap-2"
                >
                  查看已发布文章
                  <Eye className="h-4 w-4" />
                </a>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex justify-end gap-3 p-6 border-t bg-gray-50">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            关闭
          </button>
        </div>
      </div>
    </div>
  );
}

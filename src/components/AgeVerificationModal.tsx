'use client';

import { useState, useEffect } from 'react';
import { X } from 'lucide-react';

export default function AgeVerificationModal() {
  const [isOpen, setIsOpen] = useState(false);
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
    // 检查用户是否已经验证过年龄
    const hasVerified = localStorage.getItem('age-verified');
    if (!hasVerified) {
      setIsOpen(true);
    }
  }, []);

  const handleVerify = (isAdult: boolean) => {
    if (isAdult) {
      localStorage.setItem('age-verified', 'true');
      setIsOpen(false);
    } else {
      // 重定向到其他网站或显示警告
      window.location.href = 'https://www.google.com';
    }
  };

  // 防止hydration不匹配，只在客户端挂载后才渲染
  if (!isMounted) return null;
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-8 max-w-md mx-4 relative">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            Verificación de Edad
          </h2>
          <p className="text-gray-600 mb-6">
            Este sitio web contiene contenido para adultos. Para continuar, 
            debes confirmar que tienes al menos 18 años de edad.
          </p>
          <div className="flex gap-4 justify-center">
            <button
              onClick={() => handleVerify(true)}
              className="bg-pink-600 hover:bg-pink-700 text-white px-6 py-2 rounded-lg font-medium transition-colors"
            >
              Soy mayor de 18 años
            </button>
            <button
              onClick={() => handleVerify(false)}
              className="bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-2 rounded-lg font-medium transition-colors"
            >
              Soy menor de 18 años
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

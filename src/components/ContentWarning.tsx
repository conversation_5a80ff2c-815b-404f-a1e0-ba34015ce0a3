'use client';

import { useState, useEffect } from 'react';
import { AlertTriangle, X, Shield } from 'lucide-react';

interface ContentWarningProps {
  title?: string;
  message?: string;
  onAccept?: () => void;
  onDecline?: () => void;
  showOnce?: boolean;
  storageKey?: string;
}

export default function ContentWarning({
  title = "Contenido para Adultos",
  message = "Este contenido está dirigido exclusivamente a personas mayores de 18 años. Contiene información sobre productos íntimos y para adultos.",
  onAccept,
  onDecline,
  showOnce = true,
  storageKey = "content-warning-accepted"
}: ContentWarningProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
    if (!showOnce) {
      setIsVisible(true);
    } else {
      const hasAccepted = localStorage.getItem(storageKey);
      setIsVisible(!hasAccepted);
    }
  }, [showOnce, storageKey]);

  const handleAccept = () => {
    if (showOnce && storageKey) {
      localStorage.setItem(storageKey, 'true');
    }
    setIsVisible(false);
    onAccept?.();
  };

  const handleDecline = () => {
    setIsVisible(false);
    onDecline?.();
    // Redirect to a safe page
    window.location.href = 'https://www.google.com';
  };

  // Prevent hydration mismatch by only rendering after client mount
  if (!isMounted) return null;
  if (!isVisible) return null;

  return (
    <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
      <div className="flex">
        <div className="flex-shrink-0">
          <AlertTriangle className="h-5 w-5 text-yellow-400" />
        </div>
        <div className="ml-3 flex-1">
          <h3 className="text-sm font-medium text-yellow-800">
            {title}
          </h3>
          <div className="mt-2 text-sm text-yellow-700">
            <p>{message}</p>
          </div>
          <div className="mt-4 flex gap-3">
            <button
              onClick={handleAccept}
              className="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded text-sm font-medium transition-colors"
            >
              Entiendo y acepto
            </button>
            <button
              onClick={handleDecline}
              className="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded text-sm font-medium transition-colors"
            >
              Salir del sitio
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

// Componente para mostrar advertencias específicas de productos
interface ProductWarningProps {
  productType: string;
  className?: string;
}

export function ProductWarning({ productType, className = '' }: ProductWarningProps) {
  const warnings: Record<string, string> = {
    'vibradores': 'Uso exclusivo para adultos. Leer instrucciones antes del uso. Mantener fuera del alcance de menores.',
    'masturbadores': 'Producto para uso personal de adultos. Seguir instrucciones de limpieza y mantenimiento.',
    'pinzas-pezones': 'Usar con precaución. No exceder el tiempo de uso recomendado. Discontinuar si hay molestias.',
    'anillos': 'No usar por más de 30 minutos continuos. Retirar inmediatamente si hay molestias o cambios de color.',
    'default': 'Producto para adultos. Usar según las instrucciones del fabricante.'
  };

  const warning = warnings[productType] || warnings.default;

  return (
    <div className={`bg-red-50 border border-red-200 rounded-lg p-4 ${className}`}>
      <div className="flex items-start gap-3">
        <Shield className="h-5 w-5 text-red-600 flex-shrink-0 mt-0.5" />
        <div>
          <h4 className="text-sm font-semibold text-red-800 mb-1">
            Advertencia de Seguridad
          </h4>
          <p className="text-sm text-red-700">
            {warning}
          </p>
        </div>
      </div>
    </div>
  );
}

// Componente para mostrar información de privacidad
export function PrivacyNotice({ className = '' }: { className?: string }) {
  return (
    <div className={`bg-blue-50 border border-blue-200 rounded-lg p-4 ${className}`}>
      <div className="flex items-start gap-3">
        <Shield className="h-5 w-5 text-blue-600 flex-shrink-0 mt-0.5" />
        <div>
          <h4 className="text-sm font-semibold text-blue-800 mb-1">
            Privacidad y Discreción
          </h4>
          <p className="text-sm text-blue-700">
            Respetamos tu privacidad. Todos los enlaces a Amazon son seguros y discretos. 
            Tu historial de navegación en este sitio es privado.
          </p>
        </div>
      </div>
    </div>
  );
}

// Componente para mostrar información legal
export function LegalDisclaimer({ className = '' }: { className?: string }) {
  return (
    <div className={`bg-gray-50 border border-gray-200 rounded-lg p-4 text-xs text-gray-600 ${className}`}>
      <h4 className="font-semibold mb-2">Aviso Legal:</h4>
      <ul className="space-y-1">
        <li>• Este sitio contiene enlaces de afiliado a Amazon México.</li>
        <li>• Las reseñas son opiniones basadas en investigación y experiencia.</li>
        <li>• Los precios pueden variar sin previo aviso.</li>
        <li>• Consulta siempre las especificaciones del producto en Amazon.</li>
        <li>• Uso responsable y según las instrucciones del fabricante.</li>
      </ul>
    </div>
  );
}

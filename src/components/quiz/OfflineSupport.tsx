'use client';

import { useState, useEffect } from 'react';
import { Wifi, WifiOff, CloudOff, RefreshCw } from 'lucide-react';

interface OfflineSupportProps {
  children: React.ReactNode;
}

export default function OfflineSupport({ children }: OfflineSupportProps) {
  const [isOnline, setIsOnline] = useState(true);
  const [showOfflineMessage, setShowOfflineMessage] = useState(false);

  useEffect(() => {
    // 检查初始网络状态
    setIsOnline(navigator.onLine);

    // 监听网络状态变化
    const handleOnline = () => {
      setIsOnline(true);
      setShowOfflineMessage(false);
    };

    const handleOffline = () => {
      setIsOnline(false);
      setShowOfflineMessage(true);
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const handleRetry = () => {
    if (navigator.onLine) {
      setIsOnline(true);
      setShowOfflineMessage(false);
      window.location.reload();
    }
  };

  return (
    <>
      {children}
      
      {/* 网络状态指示器 */}
      <div className={`fixed top-4 right-4 z-50 transition-all duration-300 ${
        isOnline ? 'opacity-0 pointer-events-none' : 'opacity-100'
      }`}>
        <div className="bg-orange-500 text-white px-3 py-2 rounded-lg shadow-lg flex items-center gap-2">
          <WifiOff className="h-4 w-4" />
          <span className="text-sm font-medium">Modo Sin Conexión</span>
        </div>
      </div>

      {/* 离线消息弹窗 */}
      {showOfflineMessage && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl shadow-2xl max-w-md w-full p-6">
            <div className="text-center">
              <div className="bg-orange-100 rounded-full p-4 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                <CloudOff className="h-8 w-8 text-orange-600" />
              </div>
              
              <h3 className="text-xl font-bold text-gray-900 mb-2">
                Conexión de Red Interrumpida
              </h3>
              
              <p className="text-gray-600 mb-6">
                Se detectó una interrupción de la conexión de red. Los datos del quiz se han guardado localmente y se sincronizarán automáticamente cuando se restaure la conexión.
              </p>

              <div className="bg-blue-50 rounded-lg p-4 mb-6">
                <h4 className="font-medium text-blue-900 mb-2">Funciones sin conexión:</h4>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• El progreso del quiz se guarda automáticamente localmente</li>
                  <li>• Puedes continuar respondiendo preguntas</li>
                  <li>• Los datos se sincronizarán automáticamente cuando se restaure la red</li>
                  <li>• El cálculo de resultados puede requerir conexión de red</li>
                </ul>
              </div>

              <div className="flex gap-3">
                <button
                  onClick={() => setShowOfflineMessage(false)}
                  className="flex-1 bg-gray-200 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-300 transition-all"
                >
                  Continuar sin conexión
                </button>
                <button
                  onClick={handleRetry}
                  className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-all flex items-center justify-center gap-2"
                >
                  <RefreshCw className="h-4 w-4" />
                  Reintentar conexión
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}

// 网络状态Hook
export function useNetworkStatus() {
  const [isOnline, setIsOnline] = useState(true);
  const [wasOffline, setWasOffline] = useState(false);

  useEffect(() => {
    setIsOnline(navigator.onLine);

    const handleOnline = () => {
      setIsOnline(true);
      if (wasOffline) {
        // 网络恢复，可以执行同步操作
        console.log('Network restored, syncing data...');
        setWasOffline(false);
      }
    };

    const handleOffline = () => {
      setIsOnline(false);
      setWasOffline(true);
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [wasOffline]);

  return { isOnline, wasOffline };
}

// 数据同步Hook
export function useDataSync() {
  const { isOnline, wasOffline } = useNetworkStatus();
  const [isSyncing, setIsSyncing] = useState(false);
  const [syncError, setSyncError] = useState<string | null>(null);

  const syncData = async () => {
    if (!isOnline) return;

    setIsSyncing(true);
    setSyncError(null);

    try {
      // 这里实现数据同步逻辑
      // 例如：上传本地保存的测验数据到服务器
      
      // 模拟同步过程
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      console.log('Data synced successfully');
    } catch (error) {
      console.error('Sync failed:', error);
      setSyncError('Error en la sincronización de datos');
    } finally {
      setIsSyncing(false);
    }
  };

  useEffect(() => {
    if (isOnline && wasOffline) {
      syncData();
    }
  }, [isOnline, wasOffline]);

  return { isSyncing, syncError, syncData };
}

// 离线存储管理器
export class OfflineStorageManager {
  private static readonly OFFLINE_PREFIX = 'offline_';
  
  static saveOfflineData(key: string, data: any): boolean {
    try {
      const offlineKey = this.OFFLINE_PREFIX + key;
      const offlineData = {
        data,
        timestamp: Date.now(),
        synced: false
      };
      localStorage.setItem(offlineKey, JSON.stringify(offlineData));
      return true;
    } catch (error) {
      console.error('Failed to save offline data:', error);
      return false;
    }
  }

  static getOfflineData(key: string): any | null {
    try {
      const offlineKey = this.OFFLINE_PREFIX + key;
      const stored = localStorage.getItem(offlineKey);
      if (!stored) return null;

      const offlineData = JSON.parse(stored);
      return offlineData.data;
    } catch (error) {
      console.error('Failed to get offline data:', error);
      return null;
    }
  }

  static markAsSynced(key: string): boolean {
    try {
      const offlineKey = this.OFFLINE_PREFIX + key;
      const stored = localStorage.getItem(offlineKey);
      if (!stored) return false;

      const offlineData = JSON.parse(stored);
      offlineData.synced = true;
      offlineData.syncedAt = Date.now();
      
      localStorage.setItem(offlineKey, JSON.stringify(offlineData));
      return true;
    } catch (error) {
      console.error('Failed to mark as synced:', error);
      return false;
    }
  }

  static getUnsyncedData(): Array<{ key: string; data: any; timestamp: number }> {
    const unsyncedData: Array<{ key: string; data: any; timestamp: number }> = [];

    try {
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith(this.OFFLINE_PREFIX)) {
          const stored = localStorage.getItem(key);
          if (stored) {
            const offlineData = JSON.parse(stored);
            if (!offlineData.synced) {
              unsyncedData.push({
                key: key.replace(this.OFFLINE_PREFIX, ''),
                data: offlineData.data,
                timestamp: offlineData.timestamp
              });
            }
          }
        }
      }
    } catch (error) {
      console.error('Failed to get unsynced data:', error);
    }

    return unsyncedData.sort((a, b) => a.timestamp - b.timestamp);
  }

  static clearSyncedData(): number {
    let clearedCount = 0;

    try {
      const keysToRemove: string[] = [];
      
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith(this.OFFLINE_PREFIX)) {
          const stored = localStorage.getItem(key);
          if (stored) {
            const offlineData = JSON.parse(stored);
            if (offlineData.synced) {
              keysToRemove.push(key);
            }
          }
        }
      }

      keysToRemove.forEach(key => {
        localStorage.removeItem(key);
        clearedCount++;
      });
    } catch (error) {
      console.error('Failed to clear synced data:', error);
    }

    return clearedCount;
  }
}

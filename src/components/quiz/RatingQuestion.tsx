'use client';

import { useState, useEffect } from 'react';
import { QuizQuestion } from '@/types';

interface RatingQuestionProps {
  question: QuizQuestion;
  value?: number;
  onChange: (value: number) => void;
}

export default function RatingQuestion({
  question,
  value,
  onChange
}: RatingQuestionProps) {
  const min = question.min || 1;
  const max = question.max || 5;
  const [internalValue, setInternalValue] = useState<number>(value || 0);
  const [hoverValue, setHoverValue] = useState<number>(0);

  useEffect(() => {
    if (value !== undefined) {
      setInternalValue(value);
    }
  }, [value]);

  const handleRating = (rating: number) => {
    setInternalValue(rating);
    onChange(rating);
  };

  const getStarIcon = (index: number) => {
    const filled = index <= (hoverValue || internalValue);
    return (
      <svg
        className={`w-8 h-8 transition-all duration-200 ${
          filled ? 'text-yellow-400' : 'text-gray-300'
        }`}
        fill="currentColor"
        viewBox="0 0 20 20"
      >
        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
      </svg>
    );
  };

  const getEmojiForRating = (rating: number) => {
    const emojis = ['😞', '😐', '🙂', '😊', '😍'];
    return emojis[Math.max(0, Math.min(rating - 1, emojis.length - 1))];
  };

  const getRatingLabel = (rating: number) => {
    if (question.labels && question.labels.length > 0) {
      return question.labels[Math.max(0, Math.min(rating - 1, question.labels.length - 1))];
    }
    
    const defaultLabels = ['Muy malo', 'Malo', 'Regular', 'Bueno', 'Excelente'];
    return defaultLabels[Math.max(0, Math.min(rating - 1, defaultLabels.length - 1))];
  };

  return (
    <div className="space-y-6">
      {/* Current Rating Display */}
      {internalValue > 0 && (
        <div className="text-center">
          <div className="text-6xl mb-2">
            {getEmojiForRating(internalValue)}
          </div>
          <div className="text-xl font-semibold text-gray-800">
            {getRatingLabel(internalValue)}
          </div>
          <div className="text-sm text-gray-600">
            {internalValue} de {max} estrellas
          </div>
        </div>
      )}

      {/* Star Rating */}
      <div className="flex justify-center space-x-2">
        {Array.from({ length: max }, (_, index) => {
          const starValue = index + 1;
          return (
            <button
              key={starValue}
              onClick={() => handleRating(starValue)}
              onMouseEnter={() => setHoverValue(starValue)}
              onMouseLeave={() => setHoverValue(0)}
              className="transition-transform duration-200 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:ring-opacity-50 rounded"
            >
              {getStarIcon(starValue)}
            </button>
          );
        })}
      </div>

      {/* Rating Scale Labels */}
      <div className="flex justify-between text-xs text-gray-500 px-4">
        <span>{getRatingLabel(min)}</span>
        <span>{getRatingLabel(max)}</span>
      </div>

      {/* Number Rating Buttons */}
      <div className="flex justify-center space-x-2">
        {Array.from({ length: max }, (_, index) => {
          const buttonValue = index + 1;
          return (
            <button
              key={buttonValue}
              onClick={() => handleRating(buttonValue)}
              className={`w-12 h-12 rounded-full text-lg font-bold transition-all ${
                internalValue === buttonValue
                  ? 'bg-pink-600 text-white scale-110'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200 hover:scale-105'
              }`}
            >
              {buttonValue}
            </button>
          );
        })}
      </div>

      {/* Custom Labels Grid (if available) */}
      {question.labels && question.labels.length > 0 && (
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
          {question.labels.map((label, index) => {
            const labelValue = index + 1;
            return (
              <button
                key={index}
                onClick={() => handleRating(labelValue)}
                className={`p-3 rounded-lg text-sm font-medium transition-all ${
                  internalValue === labelValue
                    ? 'bg-pink-100 border-2 border-pink-300 text-pink-800'
                    : 'bg-gray-50 border-2 border-transparent text-gray-700 hover:bg-gray-100'
                }`}
              >
                <div className="flex items-center justify-center space-x-2">
                  <span className="text-lg">{getEmojiForRating(labelValue)}</span>
                  <span>{label}</span>
                </div>
              </button>
            );
          })}
        </div>
      )}

      {/* Clear Rating Button */}
      {internalValue > 0 && (
        <div className="text-center">
          <button
            onClick={() => handleRating(0)}
            className="text-sm text-gray-500 hover:text-gray-700 underline"
          >
            Limpiar calificación
          </button>
        </div>
      )}
    </div>
  );
}

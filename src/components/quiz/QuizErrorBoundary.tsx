'use client';

import React, { Component, ReactNode } from 'react';
import { AlertTriangle, RefreshCw, Home, Bug } from 'lucide-react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: React.ErrorInfo | null;
  errorId: string;
}

export default class QuizErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: ''
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error,
      errorId: Date.now().toString(36) + Math.random().toString(36).substr(2)
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    this.setState({
      error,
      errorInfo
    });

    // 调用错误回调
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // 记录错误到控制台
    console.error('Quiz Error Boundary caught an error:', error, errorInfo);

    // 可以在这里发送错误报告到监控服务
    this.reportError(error, errorInfo);
  }

  private reportError = (error: Error, errorInfo: React.ErrorInfo) => {
    try {
      // 创建错误报告
      const errorReport = {
        message: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        errorId: this.state.errorId,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href,
        userId: 'anonymous', // 可以从用户上下文获取
      };

      // 这里可以发送到错误监控服务
      // 例如: Sentry, LogRocket, 或自定义错误收集服务
      console.log('Error Report:', errorReport);

      // 保存到本地存储以便调试
      const errorKey = `quiz_error_${this.state.errorId}`;
      localStorage.setItem(errorKey, JSON.stringify(errorReport));

    } catch (reportingError) {
      console.error('Failed to report error:', reportingError);
    }
  };

  private handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: ''
    });
  };

  private handleReload = () => {
    window.location.reload();
  };

  private handleGoHome = () => {
    window.location.href = '/';
  };

  private copyErrorDetails = () => {
    const errorDetails = {
      errorId: this.state.errorId,
      message: this.state.error?.message,
      stack: this.state.error?.stack,
      componentStack: this.state.errorInfo?.componentStack,
      timestamp: new Date().toISOString()
    };

    navigator.clipboard.writeText(JSON.stringify(errorDetails, null, 2))
      .then(() => {
        alert('Detalles del error copiados al portapapeles');
      })
      .catch(() => {
        alert('Error al copiar, por favor copia manualmente el ID de error: ' + this.state.errorId);
      });
  };

  render() {
    if (this.state.hasError) {
      // 如果提供了自定义fallback，使用它
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // 默认错误UI
      return (
        <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
          <div className="max-w-md w-full bg-white rounded-xl shadow-lg p-8 text-center">
            {/* 错误图标 */}
            <div className="bg-red-100 rounded-full p-4 w-20 h-20 mx-auto mb-6 flex items-center justify-center">
              <AlertTriangle className="h-10 w-10 text-red-600" />
            </div>

            {/* 错误标题 */}
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              Problema con el Quiz
            </h1>

            {/* 错误描述 */}
            <p className="text-gray-600 mb-6">
              Lo sentimos, el sistema de quiz ha encontrado un error inesperado. Hemos registrado este problema, por favor intenta las siguientes soluciones.
            </p>

            {/* 错误ID */}
            <div className="bg-gray-50 rounded-lg p-3 mb-6">
              <p className="text-sm text-gray-700">
                ID de Error: <code className="bg-gray-200 px-2 py-1 rounded text-xs">{this.state.errorId}</code>
              </p>
            </div>

            {/* 操作按钮 */}
            <div className="space-y-3">
              <button
                onClick={this.handleRetry}
                className="w-full bg-pink-600 text-white py-3 px-4 rounded-lg hover:bg-pink-700 transition-all flex items-center justify-center gap-2"
              >
                <RefreshCw className="h-4 w-4" />
                Reintentar Quiz
              </button>

              <button
                onClick={this.handleReload}
                className="w-full bg-gray-600 text-white py-3 px-4 rounded-lg hover:bg-gray-700 transition-all flex items-center justify-center gap-2"
              >
                <RefreshCw className="h-4 w-4" />
                Recargar Página
              </button>

              <button
                onClick={this.handleGoHome}
                className="w-full bg-gray-200 text-gray-700 py-3 px-4 rounded-lg hover:bg-gray-300 transition-all flex items-center justify-center gap-2"
              >
                <Home className="h-4 w-4" />
                Volver al Inicio
              </button>
            </div>

            {/* 错误详情（开发模式） */}
            {process.env.NODE_ENV === 'development' && this.state.error && (
              <details className="mt-6 text-left">
                <summary className="cursor-pointer text-sm text-gray-600 hover:text-gray-800 flex items-center gap-2">
                  <Bug className="h-4 w-4" />
                  Ver detalles del error (modo desarrollo)
                </summary>
                <div className="mt-3 p-3 bg-red-50 rounded border text-xs">
                  <div className="mb-2">
                    <strong>Mensaje de error:</strong>
                    <pre className="mt-1 text-red-700 whitespace-pre-wrap">
                      {this.state.error.message}
                    </pre>
                  </div>
                  
                  {this.state.error.stack && (
                    <div className="mb-2">
                      <strong>Stack de error:</strong>
                      <pre className="mt-1 text-red-700 whitespace-pre-wrap text-xs overflow-x-auto">
                        {this.state.error.stack}
                      </pre>
                    </div>
                  )}

                  {this.state.errorInfo?.componentStack && (
                    <div>
                      <strong>Stack de componentes:</strong>
                      <pre className="mt-1 text-red-700 whitespace-pre-wrap text-xs overflow-x-auto">
                        {this.state.errorInfo.componentStack}
                      </pre>
                    </div>
                  )}

                  <button
                    onClick={this.copyErrorDetails}
                    className="mt-3 text-xs bg-red-600 text-white px-2 py-1 rounded hover:bg-red-700"
                  >
                    Copiar detalles del error
                  </button>
                </div>
              </details>
            )}

            {/* 帮助信息 */}
            <div className="mt-6 text-xs text-gray-500">
              <p>Si el problema persiste, contacta soporte técnico y proporciona el ID de error</p>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// 简化的错误边界Hook版本
export function useErrorHandler() {
  const handleError = (error: Error, errorInfo?: any) => {
    console.error('Quiz error:', error, errorInfo);
    
    // 可以在这里添加错误报告逻辑
    const errorReport = {
      message: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString(),
      url: window.location.href,
      userAgent: navigator.userAgent,
    };

    // 保存错误到本地存储
    const errorKey = `quiz_error_${Date.now()}`;
    try {
      localStorage.setItem(errorKey, JSON.stringify(errorReport));
    } catch (e) {
      console.error('Failed to save error to localStorage:', e);
    }
  };

  return { handleError };
}

// 错误恢复组件
interface ErrorRecoveryProps {
  error: Error;
  onRetry: () => void;
  onReset: () => void;
}

export function ErrorRecovery({ error, onRetry, onReset }: ErrorRecoveryProps) {
  return (
    <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
      <div className="text-red-600 mb-4">
        <AlertTriangle className="h-8 w-8 mx-auto" />
      </div>
      <h3 className="text-lg font-semibold text-red-900 mb-2">
        Error Ocurrido
      </h3>
      <p className="text-red-700 mb-4">
        {error.message || 'Ocurrió un error inesperado durante el quiz'}
      </p>
      <div className="flex gap-3 justify-center">
        <button
          onClick={onRetry}
          className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 transition-all"
        >
          Reintentar
        </button>
        <button
          onClick={onReset}
          className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 transition-all"
        >
          Reiniciar
        </button>
      </div>
    </div>
  );
}

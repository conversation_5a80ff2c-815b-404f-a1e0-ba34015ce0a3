'use client';

import { useState, useEffect } from 'react';
import { QuizQuestion } from '@/types';

interface SliderQuestionProps {
  question: QuizQuestion;
  value?: number;
  onChange: (value: number) => void;
}

export default function SliderQuestion({
  question,
  value,
  onChange
}: SliderQuestionProps) {
  const min = question.min || 0;
  const max = question.max || 10;
  const step = question.step || 1;
  const [internalValue, setInternalValue] = useState<number>(value || min);

  useEffect(() => {
    if (value !== undefined) {
      setInternalValue(value);
    }
  }, [value]);

  const handleChange = (newValue: number) => {
    setInternalValue(newValue);
    onChange(newValue);
  };

  const getValueLabel = (val: number) => {
    if (question.labels && question.labels.length > 0) {
      const index = Math.round(((val - min) / (max - min)) * (question.labels.length - 1));
      return question.labels[Math.max(0, Math.min(index, question.labels.length - 1))];
    }
    return val.toString();
  };

  const percentage = ((internalValue - min) / (max - min)) * 100;

  return (
    <div className="space-y-6">
      {/* Current Value Display */}
      <div className="text-center">
        <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-pink-500 to-purple-600 rounded-full text-white text-2xl font-bold mb-4">
          {internalValue}
        </div>
        <div className="text-lg font-medium text-gray-700">
          {getValueLabel(internalValue)}
        </div>
      </div>

      {/* Slider Container */}
      <div className="relative px-4">
        {/* Slider Track */}
        <div className="relative h-3 bg-gray-200 rounded-full">
          {/* Progress Fill */}
          <div 
            className="absolute top-0 left-0 h-3 bg-gradient-to-r from-pink-500 to-purple-600 rounded-full transition-all duration-200"
            style={{ width: `${percentage}%` }}
          />
          
          {/* Slider Thumb */}
          <div 
            className="absolute top-1/2 transform -translate-y-1/2 w-6 h-6 bg-white border-4 border-pink-500 rounded-full shadow-lg cursor-pointer transition-all duration-200 hover:scale-110"
            style={{ left: `calc(${percentage}% - 12px)` }}
          />
        </div>

        {/* Hidden Range Input */}
        <input
          type="range"
          min={min}
          max={max}
          step={step}
          value={internalValue}
          onChange={(e) => handleChange(parseFloat(e.target.value))}
          className="absolute top-0 left-0 w-full h-3 opacity-0 cursor-pointer"
        />
      </div>

      {/* Min/Max Labels */}
      <div className="flex justify-between text-sm text-gray-500 px-4">
        <span>{getValueLabel(min)}</span>
        <span>{getValueLabel(max)}</span>
      </div>

      {/* Value Buttons for Quick Selection */}
      <div className="flex justify-center space-x-2 flex-wrap">
        {Array.from({ length: Math.min(5, max - min + 1) }, (_, index) => {
          const buttonValue = min + Math.round((index / 4) * (max - min));
          return (
            <button
              key={buttonValue}
              onClick={() => handleChange(buttonValue)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${
                internalValue === buttonValue
                  ? 'bg-pink-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {buttonValue}
            </button>
          );
        })}
      </div>

      {/* Labels Display (if available) */}
      {question.labels && question.labels.length > 0 && (
        <div className="grid grid-cols-2 sm:grid-cols-3 gap-2 text-xs text-gray-600">
          {question.labels.map((label, index) => (
            <div key={index} className="text-center p-2 bg-gray-50 rounded">
              {label}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

'use client';

import { useState, useEffect } from 'react';
import { QuizQuestion } from '@/types';

interface MultipleChoiceQuestionProps {
  question: QuizQuestion;
  selectedValues: string[];
  onSelect: (values: string[]) => void;
}

export default function MultipleChoiceQuestion({
  question,
  selectedValues,
  onSelect
}: MultipleChoiceQuestionProps) {
  const [internalSelectedValues, setInternalSelectedValues] = useState<string[]>(selectedValues);

  useEffect(() => {
    setInternalSelectedValues(selectedValues);
  }, [selectedValues]);

  if (!question.options) {
    return <div className="text-red-500">No hay opciones disponibles para esta pregunta</div>;
  }

  const handleOptionToggle = (value: string) => {
    const newSelectedValues = internalSelectedValues.includes(value)
      ? internalSelectedValues.filter(v => v !== value)
      : [...internalSelectedValues, value];
    
    setInternalSelectedValues(newSelectedValues);
    onSelect(newSelectedValues);
  };

  const isSelected = (value: string) => internalSelectedValues.includes(value);

  return (
    <div className="space-y-4">
      <div className="text-sm text-gray-600 mb-4">
        Puedes seleccionar múltiples opciones
      </div>
      
      {question.options.map((option) => (
        <button
          key={option.id}
          onClick={() => handleOptionToggle(option.value)}
          className={`w-full p-6 rounded-xl transition-all transform hover:scale-105 flex items-center space-x-4 ${
            isSelected(option.value)
              ? 'bg-pink-50 border-2 border-pink-300 shadow-md'
              : 'bg-gray-50 hover:bg-pink-50 border-2 border-transparent hover:border-pink-300'
          }`}
        >
          {/* Option Emoji */}
          {option.emoji && (
            <div className="text-3xl flex-shrink-0">
              {option.emoji}
            </div>
          )}
          
          {/* Option Content */}
          <div className="text-left flex-grow">
            <div className="text-lg font-medium text-gray-900">
              {option.text}
            </div>
          </div>
          
          {/* Selection Indicator (Checkbox style) */}
          <div className={`w-6 h-6 rounded border-2 flex items-center justify-center flex-shrink-0 ${
            isSelected(option.value)
              ? 'border-pink-600 bg-pink-600'
              : 'border-gray-300'
          }`}>
            {isSelected(option.value) && (
              <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
            )}
          </div>
        </button>
      ))}
      
      {/* Selected Count */}
      {internalSelectedValues.length > 0 && (
        <div className="text-center text-sm text-gray-600 mt-4">
          {internalSelectedValues.length} opción{internalSelectedValues.length !== 1 ? 'es' : ''} seleccionada{internalSelectedValues.length !== 1 ? 's' : ''}
        </div>
      )}
    </div>
  );
}

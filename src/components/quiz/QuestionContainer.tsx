'use client';

import { useState } from 'react';
import { QuizQuestion, QuizAnswer, QuestionType } from '@/types';
import SingleChoiceQuestion from './SingleChoiceQuestion';
import MultipleChoiceQuestion from './MultipleChoiceQuestion';
import SliderQuestion from './SliderQuestion';
import RatingQuestion from './RatingQuestion';

interface QuestionContainerProps {
  question: QuizQuestion;
  currentAnswer?: QuizAnswer;
  onAnswer: (answer: QuizAnswer) => void;
  onNext: () => void;
  onPrevious?: () => void;
  canGoNext: boolean;
  canGoPrevious: boolean;
  isLastQuestion: boolean;
}

export default function QuestionContainer({
  question,
  currentAnswer,
  onAnswer,
  onNext,
  onPrevious,
  canGoNext,
  canGoPrevious,
  isLastQuestion
}: QuestionContainerProps) {
  const [hasAnswered, setHasAnswered] = useState(!!currentAnswer);

  const handleAnswer = (value: string | string[] | number) => {
    const answer: QuizAnswer = {
      questionId: question.id,
      value
    };
    
    onAnswer(answer);
    setHasAnswered(true);
  };

  const renderQuestion = () => {
    switch (question.type) {
      case QuestionType.SINGLE_CHOICE:
        return (
          <SingleChoiceQuestion
            question={question}
            selectedValue={typeof currentAnswer?.value === 'string' ? currentAnswer.value : undefined}
            onSelect={handleAnswer}
          />
        );
      
      case QuestionType.MULTIPLE_CHOICE:
        return (
          <MultipleChoiceQuestion
            question={question}
            selectedValues={Array.isArray(currentAnswer?.value) ? currentAnswer.value : []}
            onSelect={handleAnswer}
          />
        );
      
      case QuestionType.SLIDER:
        return (
          <SliderQuestion
            question={question}
            value={typeof currentAnswer?.value === 'number' ? currentAnswer.value : undefined}
            onChange={handleAnswer}
          />
        );
      
      case QuestionType.RATING:
        return (
          <RatingQuestion
            question={question}
            value={typeof currentAnswer?.value === 'number' ? currentAnswer.value : undefined}
            onChange={handleAnswer}
          />
        );
      
      default:
        return <div className="text-red-500">Tipo de pregunta no soportado</div>;
    }
  };

  return (
    <div className="bg-white rounded-xl shadow-lg p-8 max-w-4xl mx-auto">
      {/* Question Header */}
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">
          {question.text}
        </h2>
        {question.required !== false && (
          <p className="text-sm text-gray-500">* Pregunta obligatoria</p>
        )}
      </div>

      {/* Question Content */}
      <div className="mb-8">
        {renderQuestion()}
      </div>

      {/* Navigation Buttons */}
      <div className="flex justify-between items-center">
        <button
          onClick={onPrevious}
          disabled={!canGoPrevious}
          className={`px-6 py-3 rounded-lg font-medium transition-all ${
            canGoPrevious
              ? 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              : 'bg-gray-100 text-gray-400 cursor-not-allowed'
          }`}
        >
          Anterior
        </button>

        <div className="flex items-center space-x-2 text-sm text-gray-500">
          {question.required !== false && !hasAnswered && (
            <span className="text-orange-500">Por favor responde esta pregunta</span>
          )}
        </div>

        <button
          onClick={onNext}
          disabled={!canGoNext || (question.required !== false && !hasAnswered)}
          className={`px-6 py-3 rounded-lg font-medium transition-all ${
            canGoNext && (question.required === false || hasAnswered)
              ? 'bg-gradient-to-r from-pink-600 to-purple-600 text-white hover:from-pink-700 hover:to-purple-700'
              : 'bg-gray-100 text-gray-400 cursor-not-allowed'
          }`}
        >
          {isLastQuestion ? 'Ver Resultados' : 'Siguiente'}
        </button>
      </div>
    </div>
  );
}

// Progress Indicator Component
interface ProgressIndicatorProps {
  currentQuestion: number;
  totalQuestions: number;
  completedQuestions: number;
}

export function ProgressIndicator({ 
  currentQuestion, 
  totalQuestions, 
  completedQuestions 
}: ProgressIndicatorProps) {
  const progressPercentage = ((currentQuestion + 1) / totalQuestions) * 100;
  const completionPercentage = (completedQuestions / totalQuestions) * 100;

  return (
    <div className="mb-8">
      <div className="flex justify-between text-sm text-gray-600 mb-2">
        <span>Pregunta {currentQuestion + 1} de {totalQuestions}</span>
        <span>{Math.round(progressPercentage)}% completado</span>
      </div>
      
      {/* Progress Bar */}
      <div className="w-full bg-gray-200 rounded-full h-3 relative overflow-hidden">
        {/* Completion Progress (answered questions) */}
        <div 
          className="bg-green-400 h-3 rounded-full transition-all duration-300 absolute top-0 left-0"
          style={{ width: `${completionPercentage}%` }}
        />
        
        {/* Current Progress */}
        <div 
          className="bg-gradient-to-r from-pink-600 to-purple-600 h-3 rounded-full transition-all duration-300 absolute top-0 left-0"
          style={{ width: `${progressPercentage}%` }}
        />
      </div>
      
      {/* Question Dots */}
      <div className="flex justify-center mt-4 space-x-2">
        {Array.from({ length: totalQuestions }, (_, index) => (
          <div
            key={index}
            className={`w-3 h-3 rounded-full transition-all duration-200 ${
              index < completedQuestions
                ? 'bg-green-400'
                : index === currentQuestion
                ? 'bg-pink-600 scale-125'
                : 'bg-gray-300'
            }`}
          />
        ))}
      </div>
    </div>
  );
}

// Question Navigation Component
interface QuestionNavigationProps {
  currentQuestion: number;
  totalQuestions: number;
  onGoToQuestion: (questionIndex: number) => void;
  answeredQuestions: Set<number>;
}

export function QuestionNavigation({
  currentQuestion,
  totalQuestions,
  onGoToQuestion,
  answeredQuestions
}: QuestionNavigationProps) {
  return (
    <div className="bg-gray-50 rounded-lg p-4 mb-6">
      <h3 className="text-sm font-medium text-gray-700 mb-3">Navegación rápida</h3>
      <div className="grid grid-cols-5 sm:grid-cols-10 gap-2">
        {Array.from({ length: totalQuestions }, (_, index) => (
          <button
            key={index}
            onClick={() => onGoToQuestion(index)}
            className={`w-8 h-8 rounded-full text-xs font-medium transition-all ${
              index === currentQuestion
                ? 'bg-pink-600 text-white scale-110'
                : answeredQuestions.has(index)
                ? 'bg-green-400 text-white hover:bg-green-500'
                : 'bg-gray-200 text-gray-600 hover:bg-gray-300'
            }`}
          >
            {index + 1}
          </button>
        ))}
      </div>
    </div>
  );
}

// Quiz Header Component
interface QuizHeaderProps {
  title: string;
  description: string;
  currentQuestion: number;
  totalQuestions: number;
}

export function QuizHeader({ title, description, currentQuestion, totalQuestions }: QuizHeaderProps) {
  return (
    <div className="text-center mb-8">
      <div className="text-6xl mb-4">🎯</div>
      <h1 className="text-4xl font-bold text-gray-900 mb-4">
        {title}
      </h1>
      <p className="text-lg text-gray-600 mb-6">
        {description}
      </p>
      <div className="inline-flex items-center bg-white rounded-full px-4 py-2 shadow-sm">
        <span className="text-sm text-gray-500">
          Pregunta {currentQuestion + 1} de {totalQuestions}
        </span>
      </div>
    </div>
  );
}

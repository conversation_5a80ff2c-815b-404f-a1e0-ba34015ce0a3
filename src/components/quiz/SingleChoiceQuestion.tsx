'use client';

import { QuizQuestion } from '@/types';

interface SingleChoiceQuestionProps {
  question: QuizQuestion;
  selectedValue?: string;
  onSelect: (value: string) => void;
}

export default function SingleChoiceQuestion({
  question,
  selectedValue,
  onSelect
}: SingleChoiceQuestionProps) {
  if (!question.options) {
    return <div className="text-red-500">No hay opciones disponibles para esta pregunta</div>;
  }

  return (
    <div className="space-y-4">
      {question.options.map((option) => (
        <button
          key={option.id}
          onClick={() => onSelect(option.value)}
          className={`w-full p-6 rounded-xl transition-all transform hover:scale-105 flex items-center space-x-4 ${
            selectedValue === option.value
              ? 'bg-pink-50 border-2 border-pink-300 shadow-md'
              : 'bg-gray-50 hover:bg-pink-50 border-2 border-transparent hover:border-pink-300'
          }`}
        >
          {/* Option Emoji */}
          {option.emoji && (
            <div className="text-3xl flex-shrink-0">
              {option.emoji}
            </div>
          )}
          
          {/* Option Content */}
          <div className="text-left flex-grow">
            <div className="text-lg font-medium text-gray-900">
              {option.text}
            </div>
          </div>
          
          {/* Selection Indicator */}
          <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center flex-shrink-0 ${
            selectedValue === option.value
              ? 'border-pink-600 bg-pink-600'
              : 'border-gray-300'
          }`}>
            {selectedValue === option.value && (
              <div className="w-3 h-3 bg-white rounded-full"></div>
            )}
          </div>
        </button>
      ))}
    </div>
  );
}

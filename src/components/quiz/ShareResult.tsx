'use client';

import { useState } from 'react';
import { Share2, Co<PERSON>, Facebook, Twitter, MessageCircle, Download, Check } from 'lucide-react';
import { QuizResult } from '@/types';

interface ShareResultProps {
  result: QuizResult;
  onClose?: () => void;
}

export default function ShareResult({ result, onClose }: ShareResultProps) {
  const [copied, setCopied] = useState(false);
  const [isOpen, setIsOpen] = useState(false);

  const shareUrl = typeof window !== 'undefined' ? window.location.href : '';
  const shareTitle = `我在Tu Tienda Íntima的测验中获得了"${result.resultGroup.title}"的结果！`;
  const shareDescription = result.resultGroup.description;

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(shareUrl);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy link:', err);
    }
  };

  const handleShareFacebook = () => {
    const url = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}`;
    window.open(url, '_blank', 'width=600,height=400');
  };

  const handleShareTwitter = () => {
    const text = `${shareTitle} ${shareDescription}`;
    const url = `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(shareUrl)}`;
    window.open(url, '_blank', 'width=600,height=400');
  };

  const handleShareWhatsApp = () => {
    const text = `${shareTitle}\n\n${shareDescription}\n\n${shareUrl}`;
    const url = `https://wa.me/?text=${encodeURIComponent(text)}`;
    window.open(url, '_blank');
  };

  const generateShareImage = () => {
    // 创建一个简单的分享图片
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    canvas.width = 800;
    canvas.height = 600;

    // 背景渐变
    const gradient = ctx.createLinearGradient(0, 0, 800, 600);
    gradient.addColorStop(0, '#ec4899');
    gradient.addColorStop(1, '#8b5cf6');
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, 800, 600);

    // 文字样式
    ctx.fillStyle = 'white';
    ctx.textAlign = 'center';
    
    // 标题
    ctx.font = 'bold 48px Arial';
    ctx.fillText('Tu Tienda Íntima', 400, 100);
    
    // 结果表情符号
    ctx.font = '120px Arial';
    ctx.fillText(result.resultGroup.emoji, 400, 250);
    
    // 结果标题
    ctx.font = 'bold 36px Arial';
    ctx.fillText(result.resultGroup.title, 400, 350);
    
    // 描述（简化）
    ctx.font = '24px Arial';
    const words = result.resultGroup.description.split(' ');
    const lines = [];
    let currentLine = '';
    
    for (const word of words) {
      const testLine = currentLine + word + ' ';
      const metrics = ctx.measureText(testLine);
      if (metrics.width > 600 && currentLine !== '') {
        lines.push(currentLine);
        currentLine = word + ' ';
      } else {
        currentLine = testLine;
      }
    }
    lines.push(currentLine);
    
    lines.slice(0, 3).forEach((line, index) => {
      ctx.fillText(line.trim(), 400, 420 + index * 30);
    });
    
    // 置信度
    ctx.font = '20px Arial';
    ctx.fillText(`Confianza: ${(result.confidence * 100).toFixed(0)}%`, 400, 550);

    // 下载图片
    const link = document.createElement('a');
    link.download = `quiz-result-${result.resultGroup.id}.png`;
    link.href = canvas.toDataURL();
    link.click();
  };

  if (!isOpen) {
    return (
      <button
        onClick={() => setIsOpen(true)}
        className="flex items-center justify-center gap-2 bg-gray-100 text-gray-700 hover:bg-gray-200 px-8 py-3 rounded-lg font-medium transition-all"
      >
        <Share2 className="h-5 w-5" />
        Compartir Resultado
      </button>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-xl shadow-2xl max-w-md w-full mx-4 p-6">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-xl font-bold text-gray-900">Compartir Resultado del Quiz</h3>
          <button
            onClick={() => {
              setIsOpen(false);
              onClose?.();
            }}
            className="text-gray-400 hover:text-gray-600"
          >
            ✕
          </button>
        </div>

        {/* 结果预览 */}
        <div className="bg-gradient-to-br from-pink-50 to-purple-50 rounded-lg p-6 mb-6 text-center">
          <div className="text-4xl mb-3">{result.resultGroup.emoji}</div>
          <h4 className="text-lg font-bold text-gray-900 mb-2">
            {result.resultGroup.title}
          </h4>
          <p className="text-sm text-gray-600 mb-3">
            {result.resultGroup.description}
          </p>
          <div className="inline-flex items-center bg-white px-3 py-1 rounded-full text-sm">
            <span className="text-gray-600">Confianza:</span>
            <span className="ml-1 font-bold text-pink-600">
              {(result.confidence * 100).toFixed(0)}%
            </span>
          </div>
        </div>

        {/* 分享选项 */}
        <div className="space-y-4">
          {/* 复制链接 */}
          <button
            onClick={handleCopyLink}
            className="w-full flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-all"
          >
            <div className="flex items-center gap-3">
              {copied ? (
                <Check className="h-5 w-5 text-green-600" />
              ) : (
                <Copy className="h-5 w-5 text-gray-600" />
              )}
              <span className="font-medium text-gray-900">
                {copied ? 'Enlace Copiado' : 'Copiar Enlace'}
              </span>
            </div>
          </button>

          {/* 社交媒体分享 */}
          <div className="grid grid-cols-3 gap-3">
            <button
              onClick={handleShareFacebook}
              className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-blue-50 hover:border-blue-300 transition-all"
            >
              <Facebook className="h-6 w-6 text-blue-600 mb-2" />
              <span className="text-sm font-medium text-gray-700">Facebook</span>
            </button>

            <button
              onClick={handleShareTwitter}
              className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-blue-50 hover:border-blue-300 transition-all"
            >
              <Twitter className="h-6 w-6 text-blue-400 mb-2" />
              <span className="text-sm font-medium text-gray-700">Twitter</span>
            </button>

            <button
              onClick={handleShareWhatsApp}
              className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-green-50 hover:border-green-300 transition-all"
            >
              <MessageCircle className="h-6 w-6 text-green-600 mb-2" />
              <span className="text-sm font-medium text-gray-700">WhatsApp</span>
            </button>
          </div>

          {/* 下载图片 */}
          <button
            onClick={generateShareImage}
            className="w-full flex items-center justify-center gap-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-all"
          >
            <Download className="h-5 w-5 text-gray-600" />
            <span className="font-medium text-gray-900">Descargar Imagen</span>
          </button>
        </div>

        {/* 提示文字 */}
        <div className="mt-6 text-center">
          <p className="text-xs text-gray-500">
            ¡Comparte tu resultado y que tus amigos también hagan el quiz!
          </p>
        </div>
      </div>
    </div>
  );
}

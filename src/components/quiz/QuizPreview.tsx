'use client';

import { useState } from 'react';
import { X, <PERSON>, ArrowLeft, ArrowRight } from 'lucide-react';
import { QuizConfig, QuizAnswer } from '@/types';
import SingleChoiceQuestion from './SingleChoiceQuestion';
import MultipleChoiceQuestion from './MultipleChoiceQuestion';
import SliderQuestion from './SliderQuestion';
import RatingQuestion from './RatingQuestion';

interface QuizPreviewProps {
  config: QuizConfig;
  onClose: () => void;
}

export default function QuizPreview({ config, onClose }: QuizPreviewProps) {
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [answers, setAnswers] = useState<QuizAnswer[]>([]);
  const [isStarted, setIsStarted] = useState(false);

  const handleAnswer = (answer: QuizAnswer) => {
    const newAnswers = [...answers];
    const existingIndex = newAnswers.findIndex(a => a.questionId === answer.questionId);
    
    if (existingIndex >= 0) {
      newAnswers[existingIndex] = answer;
    } else {
      newAnswers.push(answer);
    }
    
    setAnswers(newAnswers);
  };

  const handleNext = () => {
    if (currentQuestion < config.questions.length - 1) {
      setCurrentQuestion(currentQuestion + 1);
    }
  };

  const handlePrevious = () => {
    if (currentQuestion > 0) {
      setCurrentQuestion(currentQuestion - 1);
    }
  };

  const getCurrentAnswer = () => {
    const questionId = config.questions[currentQuestion]?.id;
    return answers.find(a => a.questionId === questionId);
  };

  const renderQuestion = () => {
    const question = config.questions[currentQuestion];
    if (!question) return null;

    switch (question.type) {
      case 'single_choice':
        return (
          <SingleChoiceQuestion
            question={question}
            selectedValue={typeof getCurrentAnswer()?.value === 'string' ? getCurrentAnswer()?.value : undefined}
            onSelect={(value) => handleAnswer({ questionId: question.id, value })}
          />
        );
      
      case 'multiple_choice':
        return (
          <MultipleChoiceQuestion
            question={question}
            selectedValues={Array.isArray(getCurrentAnswer()?.value) ? getCurrentAnswer()?.value as string[] : []}
            onSelect={(values) => handleAnswer({ questionId: question.id, value: values })}
          />
        );
      
      case 'slider':
        return (
          <SliderQuestion
            question={question}
            value={typeof getCurrentAnswer()?.value === 'number' ? getCurrentAnswer()?.value : undefined}
            onChange={(value) => handleAnswer({ questionId: question.id, value })}
          />
        );
      
      case 'rating':
        return (
          <RatingQuestion
            question={question}
            value={typeof getCurrentAnswer()?.value === 'number' ? getCurrentAnswer()?.value : undefined}
            onChange={(value) => handleAnswer({ questionId: question.id, value })}
          />
        );
      
      default:
        return <div className="text-red-500">不支持的问题类型</div>;
    }
  };

  if (!isStarted) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-xl shadow-2xl max-w-2xl w-full mx-4 p-8">
          <div className="flex justify-between items-start mb-6">
            <div>
              <h2 className="text-2xl font-bold text-gray-900 mb-2">测验预览</h2>
              <p className="text-gray-600">这是一个预览模式，不会保存任何数据</p>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="h-6 w-6" />
            </button>
          </div>

          <div className="text-center mb-8">
            <div className="text-6xl mb-4">🎯</div>
            <h3 className="text-xl font-bold text-gray-900 mb-4">
              {config.title}
            </h3>
            <p className="text-gray-600 mb-6">
              {config.description}
            </p>
            <div className="bg-gray-50 rounded-lg p-4 mb-6">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium text-gray-700">问题数量:</span>
                  <span className="ml-2 text-gray-900">{config.questions.length}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">结果组:</span>
                  <span className="ml-2 text-gray-900">{config.resultGroups.length}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">评分维度:</span>
                  <span className="ml-2 text-gray-900">{config.dimensions.length}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">状态:</span>
                  <span className={`ml-2 ${config.active ? 'text-green-600' : 'text-gray-600'}`}>
                    {config.active ? '激活' : '停用'}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div className="flex gap-4 justify-center">
            <button
              onClick={() => setIsStarted(true)}
              className="bg-pink-600 text-white px-6 py-3 rounded-lg hover:bg-pink-700 flex items-center gap-2"
            >
              <Play className="h-5 w-5" />
              开始预览
            </button>
            <button
              onClick={onClose}
              className="bg-gray-500 text-white px-6 py-3 rounded-lg hover:bg-gray-600"
            >
              关闭
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-xl shadow-2xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="sticky top-0 bg-white border-b border-gray-200 px-8 py-4">
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-xl font-bold text-gray-900">测验预览</h2>
              <p className="text-sm text-gray-600">
                问题 {currentQuestion + 1} / {config.questions.length}
              </p>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="h-6 w-6" />
            </button>
          </div>
          
          {/* Progress Bar */}
          <div className="mt-4">
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-pink-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${((currentQuestion + 1) / config.questions.length) * 100}%` }}
              />
            </div>
          </div>
        </div>

        {/* Question Content */}
        <div className="p-8">
          <div className="mb-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-6">
              {config.questions[currentQuestion]?.text}
            </h3>
            
            {renderQuestion()}
          </div>

          {/* Navigation */}
          <div className="flex justify-between items-center">
            <button
              onClick={handlePrevious}
              disabled={currentQuestion === 0}
              className={`px-6 py-3 rounded-lg font-medium transition-all flex items-center gap-2 ${
                currentQuestion === 0
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              <ArrowLeft className="h-4 w-4" />
              上一题
            </button>

            <div className="text-sm text-gray-600">
              预览模式 - 数据不会保存
            </div>

            <button
              onClick={handleNext}
              disabled={currentQuestion === config.questions.length - 1}
              className={`px-6 py-3 rounded-lg font-medium transition-all flex items-center gap-2 ${
                currentQuestion === config.questions.length - 1
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-pink-600 text-white hover:bg-pink-700'
              }`}
            >
              {currentQuestion === config.questions.length - 1 ? '预览完成' : '下一题'}
              <ArrowRight className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

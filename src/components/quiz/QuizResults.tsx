'use client';

import { useState } from 'react';
import Link from 'next/link';
import { QuizResult, Product } from '@/types';
import ProductCard from '@/components/ProductCard';
import ShareResult from './ShareResult';
import { ExternalLink, RefreshCw, Share2, Download } from 'lucide-react';

interface QuizResultsProps {
  result: QuizResult;
  onRetakeQuiz: () => void;
  onShareResult?: () => void;
}

export default function QuizResults({ result, onRetakeQuiz, onShareResult }: QuizResultsProps) {
  const [showDimensionScores, setShowDimensionScores] = useState(false);

  const { resultGroup, dimensionScores, recommendedProducts, confidence } = result;

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-600 bg-green-100';
    if (confidence >= 0.6) return 'text-yellow-600 bg-yellow-100';
    return 'text-orange-600 bg-orange-100';
  };

  const getConfidenceText = (confidence: number) => {
    if (confidence >= 0.8) return 'Alta confianza';
    if (confidence >= 0.6) return 'Confianza media';
    return 'Confianza baja';
  };

  const formatDimensionName = (dimension: string) => {
    const dimensionNames: Record<string, string> = {
      experience: 'Experiencia',
      intensity: 'Intensidad',
      social: 'Uso Social',
      exploration: 'Exploración'
    };
    return dimensionNames[dimension] || dimension;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-50 to-purple-50">
      {/* Results Header */}
      <section className="py-20">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <div className="text-8xl mb-6">{resultGroup.emoji}</div>
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              ¡Tu Recomendación Personalizada!
            </h1>
            
            {/* Confidence Badge */}
            <div className={`inline-flex items-center px-4 py-2 rounded-full text-sm font-medium mb-6 ${getConfidenceColor(confidence)}`}>
              <span className="mr-2">🎯</span>
              {getConfidenceText(confidence)} ({Math.round(confidence * 100)}%)
            </div>
          </div>

          {/* Result Card */}
          <div className="bg-white rounded-xl shadow-lg p-8 mb-8 max-w-4xl mx-auto">
            <div className="text-center mb-6">
              <h2 className="text-3xl font-bold text-pink-600 mb-4">
                {resultGroup.title}
              </h2>
              <p className="text-xl text-gray-700 leading-relaxed">
                {resultGroup.description}
              </p>
            </div>

            {/* Dimension Scores Toggle */}
            <div className="text-center mb-6">
              <button
                onClick={() => setShowDimensionScores(!showDimensionScores)}
                className="text-pink-600 hover:text-pink-700 font-medium text-sm underline"
              >
                {showDimensionScores ? 'Ocultar' : 'Ver'} análisis detallado
              </button>
            </div>

            {/* Dimension Scores */}
            {showDimensionScores && (
              <div className="bg-gray-50 rounded-lg p-6 mb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Tu Perfil de Preferencias
                </h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  {Object.entries(dimensionScores).map(([dimension, score]) => (
                    <div key={dimension} className="bg-white rounded-lg p-4">
                      <div className="flex justify-between items-center mb-2">
                        <span className="font-medium text-gray-700">
                          {formatDimensionName(dimension)}
                        </span>
                        <span className="text-pink-600 font-bold">
                          {score.toFixed(1)}/3.0
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-gradient-to-r from-pink-500 to-purple-600 h-2 rounded-full transition-all duration-500"
                          style={{ width: `${(score / 3) * 100}%` }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                onClick={onRetakeQuiz}
                className="flex items-center justify-center gap-2 border-2 border-pink-600 text-pink-600 hover:bg-pink-600 hover:text-white px-8 py-3 rounded-lg font-medium transition-all"
              >
                <RefreshCw className="h-5 w-5" />
                Hacer Quiz de Nuevo
              </button>
              
              <ShareResult result={result} />
            </div>
          </div>
        </div>
      </section>

      {/* Recommended Products */}
      <section className="py-16 bg-white">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Productos Recomendados Para Ti
            </h2>
            <p className="text-lg text-gray-600">
              Basado en tus respuestas, estos productos son perfectos para tu perfil
            </p>
          </div>

          {recommendedProducts.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {recommendedProducts.map((product) => (
                <div key={product.id} className="relative">
                  <ProductCard product={product} />
                  
                  {/* Recommendation Badge */}
                  <div className="absolute -top-2 -right-2 bg-pink-600 text-white text-xs font-bold px-2 py-1 rounded-full">
                    Recomendado
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="text-6xl mb-4">🔍</div>
              <h3 className="text-xl font-semibold text-gray-700 mb-2">
                No se encontraron productos específicos
              </h3>
              <p className="text-gray-600 mb-6">
                Pero no te preocupes, tenemos muchas opciones para ti
              </p>
              <Link
                href="/productos"
                className="inline-flex items-center gap-2 bg-pink-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-pink-700 transition-all"
              >
                Ver Todos los Productos
                <ExternalLink className="h-4 w-4" />
              </Link>
            </div>
          )}

          {/* Amazon Store Link */}
          <div className="text-center mt-12">
            <div className="bg-gradient-to-r from-orange-50 to-orange-100 rounded-xl p-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                ¿Listo para tu compra?
              </h3>
              <p className="text-gray-700 mb-6">
                Visita nuestra tienda en Amazon México para ver todos los productos disponibles
              </p>
              <Link
                href="https://www.amazon.com.mx/s?me=AABH0E2XGWJR6&marketplaceID=A1AM78C64UM0Y8"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center gap-2 bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white px-8 py-4 rounded-lg font-medium transition-all transform hover:scale-105"
              >
                <ExternalLink className="h-5 w-5" />
                Ver Tienda Completa en Amazon
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Additional Information */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">
            ¿Tienes preguntas sobre tu resultado?
          </h2>
          <p className="text-gray-600 mb-8">
            Nuestro quiz está diseñado para ayudarte a encontrar productos que se adapten a tus preferencias y nivel de experiencia. Los resultados se basan en tus respuestas y nuestro algoritmo de recomendación.
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-white rounded-lg p-6">
              <div className="text-3xl mb-3">🔒</div>
              <h3 className="font-semibold text-gray-900 mb-2">Privacidad</h3>
              <p className="text-sm text-gray-600">
                Tus respuestas son completamente anónimas y seguras
              </p>
            </div>
            
            <div className="bg-white rounded-lg p-6">
              <div className="text-3xl mb-3">🎯</div>
              <h3 className="font-semibold text-gray-900 mb-2">Personalizado</h3>
              <p className="text-sm text-gray-600">
                Recomendaciones basadas en tu perfil único
              </p>
            </div>
            
            <div className="bg-white rounded-lg p-6">
              <div className="text-3xl mb-3">🔄</div>
              <h3 className="font-semibold text-gray-900 mb-2">Actualizable</h3>
              <p className="text-sm text-gray-600">
                Puedes repetir el quiz cuando quieras
              </p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}

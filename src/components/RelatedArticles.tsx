'use client';

import Link from 'next/link';
import { Calendar, User, ArrowRight } from 'lucide-react';
import { Article } from '@/types';
import { ArticleCoverCard } from './ArticleCover';
import { getArticleCoverImage } from '@/lib/utils';

interface RelatedArticlesProps {
  articles: Article[];
  className?: string;
}

export default function RelatedArticles({ articles, className = '' }: RelatedArticlesProps) {
  if (!articles || articles.length === 0) {
    return null;
  }

  return (
    <section className={`bg-gray-50 rounded-xl p-6 md:p-8 ${className}`}>
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-2xl font-bold text-gray-900">
          Artículos Relacionados
        </h3>
        <Link 
          href="/blog"
          className="text-pink-600 hover:text-pink-700 font-medium flex items-center gap-1 transition-colors"
        >
          Ver todos
          <ArrowRight className="h-4 w-4" />
        </Link>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {articles.map((article) => (
          <article 
            key={article.id}
            className="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300 overflow-hidden group"
          >
            {/* Article Cover */}
            <div className="relative">
              <ArticleCoverCard
                src={getArticleCoverImage(article)}
                alt={article.title}
                className="h-40 group-hover:scale-105 transition-transform duration-300"
              />
              {article.product && (
                <div className="absolute top-3 right-3 bg-pink-100 text-pink-700 px-2 py-1 rounded-full text-xs font-medium">
                  Producto
                </div>
              )}
            </div>

            {/* Article Content */}
            <div className="p-4">
              {/* Meta Information */}
              <div className="flex items-center gap-3 text-xs text-gray-500 mb-2">
                <div className="flex items-center gap-1">
                  <Calendar className="h-3 w-3" />
                  <time dateTime={article.publishedAt?.toISOString()}>
                    {article.publishedAt?.toLocaleDateString('es-MX', {
                      year: 'numeric',
                      month: 'short',
                      day: 'numeric'
                    })}
                  </time>
                </div>
                <div className="flex items-center gap-1">
                  <User className="h-3 w-3" />
                  <span>Tu Tienda Íntima</span>
                </div>
              </div>

              {/* Title */}
              <h4 className="font-semibold text-gray-900 mb-2 line-clamp-2 group-hover:text-pink-600 transition-colors">
                <Link href={`/articulo/${article.slug}`}>
                  {article.title}
                </Link>
              </h4>

              {/* Excerpt */}
              <p className="text-sm text-gray-600 line-clamp-2 mb-3">
                {article.excerpt}
              </p>

              {/* Keywords */}
              {article.keywords.length > 0 && (
                <div className="flex flex-wrap gap-1 mb-3">
                  {article.keywords.slice(0, 2).map((keyword, index) => (
                    <span
                      key={index}
                      className="bg-pink-50 text-pink-700 px-2 py-1 rounded text-xs"
                    >
                      {keyword}
                    </span>
                  ))}
                  {article.keywords.length > 2 && (
                    <span className="text-xs text-gray-400">
                      +{article.keywords.length - 2} más
                    </span>
                  )}
                </div>
              )}

              {/* Read More Link */}
              <Link
                href={`/articulo/${article.slug}`}
                className="inline-flex items-center gap-1 text-sm font-medium text-pink-600 hover:text-pink-700 transition-colors"
              >
                Leer más
                <ArrowRight className="h-3 w-3" />
              </Link>
            </div>
          </article>
        ))}
      </div>

      {/* Call to Action */}
      <div className="mt-8 text-center">
        <p className="text-gray-600 mb-4">
          ¿Te gustaron estos artículos? Descubre más contenido en nuestro blog.
        </p>
        <Link
          href="/blog"
          className="inline-flex items-center gap-2 bg-pink-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-pink-700 transition-colors"
        >
          Explorar Blog
          <ArrowRight className="h-4 w-4" />
        </Link>
      </div>
    </section>
  );
}

// 加载状态组件
export function RelatedArticlesSkeleton({ className = '' }: { className?: string }) {
  return (
    <section className={`bg-gray-50 rounded-xl p-6 md:p-8 ${className}`}>
      <div className="flex items-center justify-between mb-6">
        <div className="h-8 bg-gray-200 rounded w-48 animate-pulse"></div>
        <div className="h-6 bg-gray-200 rounded w-20 animate-pulse"></div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[1, 2, 3].map((i) => (
          <div key={i} className="bg-white rounded-lg shadow-sm overflow-hidden">
            <div className="h-40 bg-gray-200 animate-pulse"></div>
            <div className="p-4">
              <div className="flex gap-3 mb-2">
                <div className="h-3 bg-gray-200 rounded w-16 animate-pulse"></div>
                <div className="h-3 bg-gray-200 rounded w-20 animate-pulse"></div>
              </div>
              <div className="h-5 bg-gray-200 rounded w-full mb-2 animate-pulse"></div>
              <div className="h-5 bg-gray-200 rounded w-3/4 mb-3 animate-pulse"></div>
              <div className="h-4 bg-gray-200 rounded w-full mb-1 animate-pulse"></div>
              <div className="h-4 bg-gray-200 rounded w-2/3 mb-3 animate-pulse"></div>
              <div className="h-4 bg-gray-200 rounded w-16 animate-pulse"></div>
            </div>
          </div>
        ))}
      </div>
    </section>
  );
}

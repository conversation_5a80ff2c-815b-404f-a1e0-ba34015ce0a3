'use client';

import { useRef, useEffect, useState } from 'react';
import 'plyr/dist/plyr.css';

interface PlyrVideoPlayerProps {
  src: string;
  poster?: string;
  title?: string;
  className?: string;
  autoPlay?: boolean;
  muted?: boolean;
  preload?: 'none' | 'metadata' | 'auto';
  maxHeight?: string;
}

export default function PlyrVideoPlayer({
  src,
  poster,
  title,
  className = '',
  autoPlay = false,
  muted = false,
  preload = 'metadata',
  maxHeight = '500px'
}: PlyrVideoPlayerProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const playerRef = useRef<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [playerReady, setPlayerReady] = useState(false);

  useEffect(() => {
    let mounted = true;

    const initializePlayer = async () => {
      if (!videoRef.current || playerRef.current) return;

      try {
        // 动态导入Plyr以避免SSR问题
        const { default: Plyr } = await import('plyr');

        if (!mounted) return;

        // Plyr配置
        const options = {
          controls: [
            'play-large',
            'play',
            'progress',
            'current-time',
            'duration',
            'mute',
            'volume',
            'settings',
            'fullscreen'
          ],
          settings: ['quality', 'speed'],
          i18n: {
            // 西班牙语本地化
            play: 'Reproducir',
            pause: 'Pausar',
            mute: 'Silenciar',
            unmute: 'Activar sonido',
            volume: 'Volumen',
            currentTime: 'Tiempo actual',
            duration: 'Duración',
            fullscreen: 'Pantalla completa',
            exitFullscreen: 'Salir de pantalla completa',
            settings: 'Configuración',
            speed: 'Velocidad',
            quality: 'Calidad',
            normal: 'Normal',
            disabled: 'Deshabilitado'
          },
          keyboard: {
            focused: true,
            global: false
          },
          tooltips: {
            controls: true,
            seek: true
          },
          hideControls: true,
          clickToPlay: true,
          disableContextMenu: false
        };

        // 初始化Plyr
        playerRef.current = new Plyr(videoRef.current, options);

        // 添加事件监听器
        playerRef.current.on('ready', () => {
          if (mounted) {
            setPlayerReady(true);
            setIsLoading(false);
          }
        });

        playerRef.current.on('error', (event: any) => {
          console.error('Plyr error:', event);
          if (mounted) {
            setIsLoading(false);
          }
        });

        // 设置超时以确保加载状态正确更新
        if (mounted) {
          setTimeout(() => {
            if (mounted) {
              setIsLoading(false);
              setPlayerReady(true);
            }
          }, 1000);
        }

        // 监听全屏事件，动态调整样式
        playerRef.current.on('enterfullscreen', () => {
          const container = videoRef.current?.closest('.plyr-video-content') as HTMLElement;
          if (container) {
            container.style.maxHeight = '100vh';
          }
        });

        playerRef.current.on('exitfullscreen', () => {
          const container = videoRef.current?.closest('.plyr-video-content') as HTMLElement;
          if (container) {
            container.style.maxHeight = maxHeight;
          }
        });

      } catch (error) {
        console.error('Failed to load Plyr:', error);
        setIsLoading(false);
      }
    };

    initializePlayer();

    return () => {
      mounted = false;
      // 清理Plyr实例
      if (playerRef.current) {
        playerRef.current.destroy();
        playerRef.current = null;
      }
    };
  }, [src, maxHeight]);

  return (
    <div
      className={`plyr-video-container relative ${className}`}
      style={{ maxHeight }}
    >
      {(isLoading || !playerReady) && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 rounded-xl">
          <div className="text-gray-500">Cargando reproductor...</div>
        </div>
      )}
      <video
        ref={videoRef}
        src={src}
        poster={poster}
        autoPlay={autoPlay}
        muted={muted}
        preload={preload}
        className="w-full h-auto plyr-video-content"
        style={{ maxHeight }}
        aria-label={title || 'Video de demostración del producto'}
        crossOrigin="anonymous"
      />
      
      {/* 自定义样式 */}
      <style jsx>{`
        .plyr-video-container {
          --plyr-color-main: #ec4899;
          --plyr-video-background: #000;
          --plyr-menu-background: rgba(0, 0, 0, 0.9);
          --plyr-menu-color: #fff;
        }

        .plyr-video-container :global(.plyr) {
          border-radius: 12px;
          overflow: hidden;
        }

        /* 修复poster图片超出容器的问题 */
        .plyr-video-container :global(.plyr__poster) {
          border-radius: 12px;
          object-fit: cover;
          overflow: hidden;
        }

        .plyr-video-container :global(.plyr video) {
          border-radius: 12px;
          object-fit: contain;
        }

        /* 全屏模式优化 */
        .plyr-video-container :global(.plyr--fullscreen-active) {
          max-height: 100vh !important;
        }

        .plyr-video-container :global(.plyr--fullscreen-active video) {
          width: 100vw !important;
          height: 100vh !important;
          max-width: none !important;
          max-height: none !important;
          object-fit: contain;
        }

        .plyr-video-container :global(.plyr__control--overlaid) {
          background: rgba(236, 72, 153, 0.9);
        }

        .plyr-video-container :global(.plyr__control--overlaid:hover) {
          background: rgba(219, 39, 119, 0.9);
        }

        .plyr-video-container :global(.plyr__progress__buffer) {
          color: rgba(236, 72, 153, 0.3);
        }

        .plyr-video-container :global(.plyr__volume__input) {
          color: #ec4899;
        }

        .plyr-video-container :global(.plyr__control:hover) {
          background: rgba(236, 72, 153, 0.1);
        }

        .plyr-video-container :global(.plyr__control[aria-expanded="true"]) {
          background: rgba(236, 72, 153, 0.1);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
          .plyr-video-container :global(.plyr__controls) {
            padding: 10px;
          }

          .plyr-video-container :global(.plyr__control) {
            padding: 8px;
          }
        }
      `}</style>
    </div>
  );
}

// 预设样式的便捷组件
export function PlyrVideoPlayerCard({
  src,
  poster,
  title,
  className = ''
}: Pick<PlyrVideoPlayerProps, 'src' | 'poster' | 'title' | 'className'>) {
  return (
    <PlyrVideoPlayer
      src={src}
      poster={poster}
      title={title}
      className={`rounded-xl ${className}`}
      preload="metadata"
      maxHeight="400px"
    />
  );
}

export function PlyrVideoPlayerHero({
  src,
  poster,
  title,
  className = ''
}: Pick<PlyrVideoPlayerProps, 'src' | 'poster' | 'title' | 'className'>) {
  return (
    <PlyrVideoPlayer
      src={src}
      poster={poster}
      title={title}
      className={`rounded-xl ${className}`}
      preload="metadata"
      maxHeight="500px"
    />
  );
}

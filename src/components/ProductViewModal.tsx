'use client';

import { X, ExternalLink, Calendar, Tag } from 'lucide-react';
import { Product } from '@/types';

interface ProductViewModalProps {
  product: Product;
  onClose: () => void;
}

export default function ProductViewModal({ product, onClose }: ProductViewModalProps) {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b">
          <h2 className="text-2xl font-bold text-gray-900">产品详情</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Product Info */}
          <div className="bg-gradient-to-r from-pink-50 to-purple-50 rounded-lg p-6">
            <div className="flex items-center justify-center mb-4">
              <div className="text-6xl">🎁</div>
            </div>
            <h3 className="text-xl font-bold text-gray-900 text-center mb-2">
              {product.name}
            </h3>
            <div className="flex justify-center">
              <span className="bg-pink-100 text-pink-800 px-3 py-1 rounded-full text-sm font-medium capitalize">
                {product.category.replace('-', ' ')}
              </span>
            </div>
          </div>

          {/* Details Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <Tag className="h-4 w-4 text-gray-600" />
                <span className="font-medium text-gray-900">产品ID</span>
              </div>
              <p className="text-gray-700 font-mono text-sm">{product.id}</p>
            </div>

            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <Calendar className="h-4 w-4 text-gray-600" />
                <span className="font-medium text-gray-900">创建时间</span>
              </div>
              <p className="text-gray-700">
                {new Date(product.createdAt).toLocaleString('zh-CN')}
              </p>
            </div>

            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <Calendar className="h-4 w-4 text-gray-600" />
                <span className="font-medium text-gray-900">更新时间</span>
              </div>
              <p className="text-gray-700">
                {new Date(product.updatedAt).toLocaleString('zh-CN')}
              </p>
            </div>

            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <ExternalLink className="h-4 w-4 text-gray-600" />
                <span className="font-medium text-gray-900">Amazon链接</span>
              </div>
              <a
                href={product.amazonUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 hover:text-blue-800 text-sm break-all"
              >
                {product.amazonUrl}
              </a>
            </div>
          </div>

          {/* Amazon Button */}
          <div className="border-t pt-6">
            <div className="flex justify-center">
              <a
                href={product.amazonUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="bg-pink-600 hover:bg-pink-700 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center gap-2"
              >
                在Amazon上查看
                <ExternalLink className="h-4 w-4" />
              </a>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end gap-3 p-6 border-t bg-gray-50">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            关闭
          </button>
        </div>
      </div>
    </div>
  );
}

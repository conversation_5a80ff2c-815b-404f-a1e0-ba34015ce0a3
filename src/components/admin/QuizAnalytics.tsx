'use client';

import { useState, useEffect } from 'react';
import { BarChart3, TrendingUp, Users, Clock, Target, RefreshCw } from 'lucide-react';
import { QuizConfig } from '@/types';
import { QuizService } from '@/lib/quizEngine';

interface QuizAnalyticsProps {
  configs: QuizConfig[];
}

interface QuizStats {
  totalSessions: number;
  completedSessions: number;
  completionRate: number;
  averageCompletionTime: number;
  popularAnswers: Record<string, Record<string, number>>;
}

export default function QuizAnalytics({ configs }: QuizAnalyticsProps) {
  const [selectedConfigId, setSelectedConfigId] = useState<string>('all');
  const [stats, setStats] = useState<QuizStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadStats();
  }, [selectedConfigId]);

  const loadStats = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const configId = selectedConfigId === 'all' ? undefined : selectedConfigId;
      const statsData = await QuizService.getQuizStats(configId);
      setStats(statsData);
    } catch (err) {
      console.error('Error loading quiz stats:', err);
      setError('加载统计数据失败');
    } finally {
      setLoading(false);
    }
  };

  const formatTime = (seconds: number): string => {
    if (seconds < 60) {
      return `${Math.round(seconds)}秒`;
    } else if (seconds < 3600) {
      return `${Math.round(seconds / 60)}分钟`;
    } else {
      return `${Math.round(seconds / 3600)}小时`;
    }
  };

  const getCompletionRateColor = (rate: number): string => {
    if (rate >= 80) return 'text-green-600 bg-green-100';
    if (rate >= 60) return 'text-yellow-600 bg-yellow-100';
    return 'text-red-600 bg-red-100';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-pink-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">测验数据分析</h2>
          <p className="text-gray-600">查看测验使用情况和用户行为分析</p>
        </div>
        <div className="flex items-center gap-3">
          <select
            value={selectedConfigId}
            onChange={(e) => setSelectedConfigId(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
          >
            <option value="all">所有测验</option>
            {configs.map(config => (
              <option key={config.id} value={config.id}>
                {config.title}
              </option>
            ))}
          </select>
          <button
            onClick={loadStats}
            className="bg-pink-600 text-white px-4 py-2 rounded-lg hover:bg-pink-700 flex items-center gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            刷新
          </button>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      )}

      {stats && (
        <>
          {/* 关键指标卡片 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">总会话数</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalSessions}</p>
                </div>
                <div className="bg-blue-100 p-3 rounded-full">
                  <Users className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">完成会话数</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.completedSessions}</p>
                </div>
                <div className="bg-green-100 p-3 rounded-full">
                  <Target className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">完成率</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.completionRate.toFixed(1)}%</p>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getCompletionRateColor(stats.completionRate)}`}>
                    {stats.completionRate >= 80 ? '优秀' : stats.completionRate >= 60 ? '良好' : '需改进'}
                  </span>
                </div>
                <div className="bg-purple-100 p-3 rounded-full">
                  <TrendingUp className="h-6 w-6 text-purple-600" />
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">平均完成时间</p>
                  <p className="text-2xl font-bold text-gray-900">{formatTime(stats.averageCompletionTime)}</p>
                </div>
                <div className="bg-orange-100 p-3 rounded-full">
                  <Clock className="h-6 w-6 text-orange-600" />
                </div>
              </div>
            </div>
          </div>

          {/* 热门答案分析 */}
          {Object.keys(stats.popularAnswers).length > 0 && (
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center gap-2 mb-6">
                <BarChart3 className="h-5 w-5 text-gray-600" />
                <h3 className="text-lg font-semibold text-gray-900">用户偏好分析</h3>
              </div>
              
              <div className="space-y-6">
                {Object.entries(stats.popularAnswers).map(([questionId, answers]) => {
                  const totalAnswers = Object.values(answers).reduce((sum, count) => sum + count, 0);
                  const sortedAnswers = Object.entries(answers).sort(([,a], [,b]) => b - a);
                  
                  return (
                    <div key={questionId} className="border-b border-gray-200 pb-4 last:border-b-0">
                      <h4 className="font-medium text-gray-900 mb-3">{questionId}</h4>
                      <div className="space-y-2">
                        {sortedAnswers.map(([answer, count]) => {
                          const percentage = (count / totalAnswers) * 100;
                          return (
                            <div key={answer} className="flex items-center gap-3">
                              <div className="flex-1">
                                <div className="flex justify-between items-center mb-1">
                                  <span className="text-sm text-gray-700">{answer}</span>
                                  <span className="text-sm font-medium text-gray-900">
                                    {count} ({percentage.toFixed(1)}%)
                                  </span>
                                </div>
                                <div className="w-full bg-gray-200 rounded-full h-2">
                                  <div
                                    className="bg-pink-600 h-2 rounded-full transition-all duration-300"
                                    style={{ width: `${percentage}%` }}
                                  />
                                </div>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {/* 改进建议 */}
          <div className="bg-blue-50 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-blue-900 mb-4">优化建议</h3>
            <div className="space-y-3">
              {stats.completionRate < 60 && (
                <div className="flex items-start gap-3">
                  <div className="bg-red-100 p-1 rounded-full mt-1">
                    <div className="w-2 h-2 bg-red-600 rounded-full"></div>
                  </div>
                  <div>
                    <p className="font-medium text-red-900">完成率偏低</p>
                    <p className="text-sm text-red-700">
                      建议简化问题、减少问题数量或优化用户界面以提高完成率
                    </p>
                  </div>
                </div>
              )}
              
              {stats.averageCompletionTime > 300 && (
                <div className="flex items-start gap-3">
                  <div className="bg-yellow-100 p-1 rounded-full mt-1">
                    <div className="w-2 h-2 bg-yellow-600 rounded-full"></div>
                  </div>
                  <div>
                    <p className="font-medium text-yellow-900">完成时间较长</p>
                    <p className="text-sm text-yellow-700">
                      用户平均需要{formatTime(stats.averageCompletionTime)}完成测验，考虑优化问题表述或减少问题数量
                    </p>
                  </div>
                </div>
              )}
              
              {stats.totalSessions < 10 && (
                <div className="flex items-start gap-3">
                  <div className="bg-blue-100 p-1 rounded-full mt-1">
                    <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                  </div>
                  <div>
                    <p className="font-medium text-blue-900">参与度较低</p>
                    <p className="text-sm text-blue-700">
                      测验参与人数较少，建议加强推广或优化测验入口的可见性
                    </p>
                  </div>
                </div>
              )}
              
              {stats.completionRate >= 80 && stats.averageCompletionTime <= 180 && stats.totalSessions >= 50 && (
                <div className="flex items-start gap-3">
                  <div className="bg-green-100 p-1 rounded-full mt-1">
                    <div className="w-2 h-2 bg-green-600 rounded-full"></div>
                  </div>
                  <div>
                    <p className="font-medium text-green-900">表现优秀</p>
                    <p className="text-sm text-green-700">
                      测验的完成率、用时和参与度都表现良好，继续保持！
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </>
      )}

      {!stats && !loading && !error && (
        <div className="text-center py-12 bg-gray-50 rounded-lg">
          <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">暂无数据</h3>
          <p className="text-gray-600">还没有测验会话数据可供分析</p>
        </div>
      )}
    </div>
  );
}

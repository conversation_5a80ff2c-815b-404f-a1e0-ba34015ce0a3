'use client';

import { useState, useEffect } from 'react';
import { Plus, Edit, Trash2, Eye, Play, Settings, Save, X, BarChart3 } from 'lucide-react';
import { QuizConfig, QuizQuestion, QuizResultGroup, QuestionType } from '@/types';
import { QuizService } from '@/lib/quizEngine';
import { useToast } from '@/components/Toast';
import ConfirmDialog, { useConfirmDialog } from '@/components/ConfirmDialog';
import QuestionEditor from './QuestionEditor';
import ResultGroupEditor from './ResultGroupEditor';
import QuizAnalytics from './QuizAnalytics';
import QuizPreview from '../quiz/QuizPreview';

interface QuizConfigManagerProps {
  onClose?: () => void;
}

export default function QuizConfigManager({ onClose }: QuizConfigManagerProps) {
  const [activeTab, setActiveTab] = useState<'configs' | 'analytics'>('configs');
  const [configs, setConfigs] = useState<QuizConfig[]>([]);
  const [selectedConfig, setSelectedConfig] = useState<QuizConfig | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [previewConfig, setPreviewConfig] = useState<QuizConfig | null>(null);
  
  const toast = useToast();
  const confirmDialog = useConfirmDialog();

  useEffect(() => {
    loadConfigs();
  }, []);

  const loadConfigs = async () => {
    try {
      setLoading(true);
      const allConfigs = await QuizService.getAllQuizConfigs();
      setConfigs(allConfigs);
    } catch (error) {
      console.error('Error loading quiz configs:', error);
      toast.error('加载失败', '无法加载测验配置');
    } finally {
      setLoading(false);
    }
  };

  const handleNewConfig = () => {
    const newConfig: Partial<QuizConfig> = {
      id: 'new', // 临时ID，保存时会被替换
      title: '新测验配置',
      description: '请输入测验描述',
      questions: [],
      resultGroups: [],
      dimensions: ['experience', 'intensity', 'social', 'exploration'],
      active: false,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    setSelectedConfig(newConfig as QuizConfig);
    setIsEditing(true);
  };

  const handleEditConfig = (config: QuizConfig) => {
    setSelectedConfig(config);
    setIsEditing(true);
  };

  const handleDeleteConfig = async (config: QuizConfig) => {
    const confirmed = await confirmDialog.confirm(
      '删除测验配置',
      `确定要删除测验配置"${config.title}"吗？此操作不可撤销。`
    );

    if (!confirmed) return;

    try {
      const success = await QuizService.deleteQuizConfig(config.id);
      if (success) {
        toast.success('删除成功', '测验配置已删除');
        loadConfigs();
      } else {
        toast.error('删除失败', '无法删除测验配置');
      }
    } catch (error) {
      console.error('Error deleting config:', error);
      toast.error('删除失败', '无法删除测验配置');
    }
  };

  const handleSaveConfig = async () => {
    if (!selectedConfig) return;

    try {
      setSaving(true);
      let success = false;

      if (selectedConfig.id && selectedConfig.id !== 'new') {
        // 更新现有配置
        const updatedConfig = await QuizService.updateQuizConfig(selectedConfig.id, selectedConfig);
        success = !!updatedConfig;
      } else {
        // 创建新配置
        const newConfig = await QuizService.createQuizConfig(selectedConfig);
        success = !!newConfig;
      }

      if (success) {
        toast.success('保存成功', '测验配置已保存');
        setIsEditing(false);
        setSelectedConfig(null);
        loadConfigs();
      } else {
        toast.error('保存失败', '无法保存测验配置');
      }
    } catch (error) {
      console.error('Error saving config:', error);
      toast.error('保存失败', '无法保存测验配置');
    } finally {
      setSaving(false);
    }
  };

  const handlePreviewConfig = (config: QuizConfig) => {
    setPreviewConfig(config);
  };

  const handleToggleActive = async (config: QuizConfig) => {
    try {
      const success = await QuizService.toggleQuizConfigActive(config.id);
      if (success) {
        toast.success('状态更新', `测验配置已${config.active ? '停用' : '激活'}`);
        loadConfigs();
      } else {
        toast.error('更新失败', '无法更新测验配置状态');
      }
    } catch (error) {
      console.error('Error toggling config status:', error);
      toast.error('更新失败', '无法更新测验配置状态');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-pink-600"></div>
      </div>
    );
  }

  if (isEditing && selectedConfig) {
    return (
      <QuizConfigEditor
        config={selectedConfig}
        onSave={handleSaveConfig}
        onCancel={() => {
          setIsEditing(false);
          setSelectedConfig(null);
        }}
        saving={saving}
      />
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">测验系统管理</h2>
          <p className="text-gray-600">管理产品推荐测验的配置、问题和数据分析</p>
        </div>
        <div className="flex gap-3">
          {activeTab === 'configs' && (
            <button
              onClick={handleNewConfig}
              className="bg-pink-600 text-white px-4 py-2 rounded-lg hover:bg-pink-700 flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              新建配置
            </button>
          )}
          {onClose && (
            <button
              onClick={onClose}
              className="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600"
            >
              关闭
            </button>
          )}
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b">
        <div className="flex">
          <button
            onClick={() => setActiveTab('configs')}
            className={`px-6 py-3 font-medium flex items-center gap-2 ${
              activeTab === 'configs'
                ? 'border-b-2 border-pink-600 text-pink-600'
                : 'text-gray-600 hover:text-pink-600'
            }`}
          >
            <Settings className="h-4 w-4" />
            配置管理
          </button>
          <button
            onClick={() => setActiveTab('analytics')}
            className={`px-6 py-3 font-medium flex items-center gap-2 ${
              activeTab === 'analytics'
                ? 'border-b-2 border-pink-600 text-pink-600'
                : 'text-gray-600 hover:text-pink-600'
            }`}
          >
            <BarChart3 className="h-4 w-4" />
            数据分析
          </button>
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === 'configs' && (
        <>
          {/* Configs List */}
          {configs.length === 0 ? (
        <div className="text-center py-12 bg-gray-50 rounded-lg">
          <Settings className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">暂无测验配置</h3>
          <p className="text-gray-600 mb-4">创建您的第一个测验配置来开始</p>
          <button
            onClick={handleNewConfig}
            className="bg-pink-600 text-white px-6 py-2 rounded-lg hover:bg-pink-700"
          >
            创建配置
          </button>
        </div>
      ) : (
        <div className="grid gap-6">
          {configs.map((config) => (
            <div key={config.id} className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex justify-between items-start mb-4">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <h3 className="text-lg font-semibold text-gray-900">
                      {config.title}
                    </h3>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      config.active 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {config.active ? '激活' : '停用'}
                    </span>
                  </div>
                  <p className="text-gray-600 mb-3">{config.description}</p>
                  <div className="flex items-center gap-4 text-sm text-gray-500">
                    <span>{config.questions.length} 个问题</span>
                    <span>{config.resultGroups.length} 个结果组</span>
                    <span>{config.dimensions.length} 个维度</span>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => handlePreviewConfig(config)}
                    className="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded"
                    title="预览"
                  >
                    <Eye className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => handleEditConfig(config)}
                    className="p-2 text-gray-600 hover:text-green-600 hover:bg-green-50 rounded"
                    title="编辑"
                  >
                    <Edit className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => handleToggleActive(config)}
                    className={`p-2 rounded ${
                      config.active
                        ? 'text-gray-600 hover:text-orange-600 hover:bg-orange-50'
                        : 'text-gray-600 hover:text-green-600 hover:bg-green-50'
                    }`}
                    title={config.active ? '停用' : '激活'}
                  >
                    <Play className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => handleDeleteConfig(config)}
                    className="p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded"
                    title="删除"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
        </>
      )}

      {activeTab === 'analytics' && (
        <QuizAnalytics configs={configs} />
      )}

      <ConfirmDialog {...confirmDialog.props} />

      {/* Quiz Preview Modal */}
      {previewConfig && (
        <QuizPreview
          config={previewConfig}
          onClose={() => setPreviewConfig(null)}
        />
      )}
    </div>
  );
}

// Quiz Config Editor Component
interface QuizConfigEditorProps {
  config: QuizConfig;
  onSave: () => void;
  onCancel: () => void;
  saving: boolean;
}

function QuizConfigEditor({ config, onSave, onCancel, saving }: QuizConfigEditorProps) {
  const [editedConfig, setEditedConfig] = useState<QuizConfig>(config);

  const updateConfig = (updates: Partial<QuizConfig>) => {
    setEditedConfig(prev => ({ ...prev, ...updates }));
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900">
          {config.id ? '编辑测验配置' : '新建测验配置'}
        </h2>
        <div className="flex gap-3">
          <button
            onClick={onSave}
            disabled={saving}
            className="bg-pink-600 text-white px-4 py-2 rounded-lg hover:bg-pink-700 flex items-center gap-2 disabled:opacity-50"
          >
            {saving ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            ) : (
              <Save className="h-4 w-4" />
            )}
            保存
          </button>
          <button
            onClick={onCancel}
            className="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 flex items-center gap-2"
          >
            <X className="h-4 w-4" />
            取消
          </button>
        </div>
      </div>

      {/* Basic Info */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">基本信息</h3>
        <div className="grid grid-cols-1 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              测验标题
            </label>
            <input
              type="text"
              value={editedConfig.title}
              onChange={(e) => updateConfig({ title: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
              placeholder="输入测验标题"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              测验描述
            </label>
            <textarea
              value={editedConfig.description}
              onChange={(e) => updateConfig({ description: e.target.value })}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
              placeholder="输入测验描述"
            />
          </div>
          <div className="flex items-center">
            <input
              type="checkbox"
              id="active"
              checked={editedConfig.active}
              onChange={(e) => updateConfig({ active: e.target.checked })}
              className="h-4 w-4 text-pink-600 focus:ring-pink-500 border-gray-300 rounded"
            />
            <label htmlFor="active" className="ml-2 block text-sm text-gray-900">
              激活此测验配置
            </label>
          </div>
        </div>
      </div>

      {/* 维度管理 */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">评分维度</h3>
        <div className="flex flex-wrap gap-2">
          {editedConfig.dimensions.map((dimension, index) => (
            <div key={index} className="bg-blue-100 text-blue-700 px-3 py-1 rounded-full flex items-center gap-2">
              <span>{dimension}</span>
              <button
                onClick={() => {
                  const newDimensions = [...editedConfig.dimensions];
                  newDimensions.splice(index, 1);
                  updateConfig({ dimensions: newDimensions });
                }}
                className="text-blue-500 hover:text-blue-700"
              >
                <X className="h-3 w-3" />
              </button>
            </div>
          ))}
          <button
            onClick={() => {
              const dimension = prompt('请输入新的维度名称:');
              if (dimension && dimension.trim()) {
                const newDimensions = [...editedConfig.dimensions, dimension.trim()];
                updateConfig({ dimensions: newDimensions });
              }
            }}
            className="bg-gray-200 text-gray-700 px-3 py-1 rounded-full hover:bg-gray-300 flex items-center gap-1"
          >
            <Plus className="h-3 w-3" />
            添加维度
          </button>
        </div>
      </div>

      {/* 问题编辑器 */}
      <QuestionEditor
        questions={editedConfig.questions}
        dimensions={editedConfig.dimensions}
        onChange={(questions) => updateConfig({ questions })}
      />

      {/* 结果组编辑器 */}
      <ResultGroupEditor
        resultGroups={editedConfig.resultGroups}
        dimensions={editedConfig.dimensions}
        onChange={(resultGroups) => updateConfig({ resultGroups })}
      />
    </div>
  );
}

'use client';

import { useState } from 'react';
import { Plus, Trash2, Edit, Save, X } from 'lucide-react';
import { QuizResultGroup } from '@/types';

interface ResultGroupEditorProps {
  resultGroups: QuizResultGroup[];
  dimensions: string[];
  onChange: (resultGroups: QuizResultGroup[]) => void;
}

export default function ResultGroupEditor({ resultGroups, dimensions, onChange }: ResultGroupEditorProps) {
  const [editingGroup, setEditingGroup] = useState<QuizResultGroup | null>(null);
  const [isCreating, setIsCreating] = useState(false);

  const handleAddGroup = () => {
    const newGroup: QuizResultGroup = {
      id: `group_${Date.now()}`,
      title: '新结果组',
      description: '请输入结果组描述',
      emoji: '🎯',
      dimensionScores: {},
      productCategories: []
    };
    
    // 为每个维度设置默认范围
    dimensions.forEach(dimension => {
      newGroup.dimensionScores[dimension] = [0, 5];
    });
    
    setEditingGroup(newGroup);
    setIsCreating(true);
  };

  const handleSaveGroup = (group: QuizResultGroup) => {
    if (isCreating) {
      onChange([...resultGroups, group]);
      setIsCreating(false);
    } else {
      const updatedGroups = resultGroups.map(g => 
        g.id === group.id ? group : g
      );
      onChange(updatedGroups);
    }
    setEditingGroup(null);
  };

  const handleDeleteGroup = (groupId: string) => {
    const updatedGroups = resultGroups.filter(g => g.id !== groupId);
    onChange(updatedGroups);
  };

  const handleCancelEdit = () => {
    setEditingGroup(null);
    setIsCreating(false);
  };

  if (editingGroup) {
    return (
      <ResultGroupEditForm
        group={editingGroup}
        dimensions={dimensions}
        onSave={handleSaveGroup}
        onCancel={handleCancelEdit}
        isCreating={isCreating}
      />
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold text-gray-900">结果组管理</h3>
        <button
          onClick={handleAddGroup}
          className="bg-pink-600 text-white px-4 py-2 rounded-lg hover:bg-pink-700 flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          添加结果组
        </button>
      </div>

      {resultGroups.length === 0 ? (
        <div className="text-center py-8 bg-gray-50 rounded-lg">
          <p className="text-gray-600">暂无结果组，点击"添加结果组"开始创建</p>
        </div>
      ) : (
        <div className="grid gap-4">
          {resultGroups.map((group, index) => (
            <div key={group.id} className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex items-start justify-between">
                <div className="flex items-start gap-3 flex-1">
                  <div className="text-3xl">{group.emoji}</div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <span className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-sm font-medium">
                        #{index + 1}
                      </span>
                    </div>
                    <h4 className="font-medium text-gray-900 mb-2">{group.title}</h4>
                    <p className="text-sm text-gray-600 mb-3">{group.description}</p>
                    
                    {/* 维度范围显示 */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-2 mb-3">
                      {Object.entries(group.dimensionScores).map(([dimension, range]) => (
                        <div key={dimension} className="bg-gray-50 p-2 rounded text-xs">
                          <div className="font-medium text-gray-700">{dimension}</div>
                          <div className="text-blue-600">{range[0]} - {range[1]}</div>
                        </div>
                      ))}
                    </div>
                    
                    {/* 产品分类显示 */}
                    <div className="flex flex-wrap gap-1">
                      {group.productCategories.map(category => (
                        <span key={category} className="bg-blue-100 text-blue-700 px-2 py-1 rounded text-xs">
                          {category}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => setEditingGroup(group)}
                    className="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded"
                    title="编辑"
                  >
                    <Edit className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => handleDeleteGroup(group.id)}
                    className="p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded"
                    title="删除"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

// Result Group Edit Form Component
interface ResultGroupEditFormProps {
  group: QuizResultGroup;
  dimensions: string[];
  onSave: (group: QuizResultGroup) => void;
  onCancel: () => void;
  isCreating: boolean;
}

function ResultGroupEditForm({ group, dimensions, onSave, onCancel, isCreating }: ResultGroupEditFormProps) {
  const [editedGroup, setEditedGroup] = useState<QuizResultGroup>(group);

  const updateGroup = (updates: Partial<QuizResultGroup>) => {
    setEditedGroup(prev => ({ ...prev, ...updates }));
  };

  const updateDimensionScore = (dimension: string, index: number, value: number) => {
    const newDimensionScores = { ...editedGroup.dimensionScores };
    if (!newDimensionScores[dimension]) {
      newDimensionScores[dimension] = [0, 5];
    }
    newDimensionScores[dimension][index] = value;
    updateGroup({ dimensionScores: newDimensionScores });
  };

  const addProductCategory = () => {
    const category = prompt('请输入产品分类名称:');
    if (category && category.trim()) {
      const newCategories = [...editedGroup.productCategories, category.trim()];
      updateGroup({ productCategories: newCategories });
    }
  };

  const removeProductCategory = (index: number) => {
    const newCategories = [...editedGroup.productCategories];
    newCategories.splice(index, 1);
    updateGroup({ productCategories: newCategories });
  };

  const handleSave = () => {
    // 验证结果组
    if (!editedGroup.title.trim()) {
      alert('请输入结果组标题');
      return;
    }

    if (!editedGroup.description.trim()) {
      alert('请输入结果组描述');
      return;
    }

    onSave(editedGroup);
  };

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6">
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-lg font-semibold text-gray-900">
          {isCreating ? '创建新结果组' : '编辑结果组'}
        </h3>
        <div className="flex gap-2">
          <button
            onClick={handleSave}
            className="bg-pink-600 text-white px-4 py-2 rounded-lg hover:bg-pink-700 flex items-center gap-2"
          >
            <Save className="h-4 w-4" />
            保存
          </button>
          <button
            onClick={onCancel}
            className="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 flex items-center gap-2"
          >
            <X className="h-4 w-4" />
            取消
          </button>
        </div>
      </div>

      <div className="space-y-6">
        {/* 基本信息 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              标题 *
            </label>
            <input
              type="text"
              value={editedGroup.title}
              onChange={(e) => updateGroup({ title: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
              placeholder="输入结果组标题"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              表情符号
            </label>
            <input
              type="text"
              value={editedGroup.emoji}
              onChange={(e) => updateGroup({ emoji: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
              placeholder="🎯"
            />
          </div>
          <div className="md:col-span-1">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              描述 *
            </label>
            <textarea
              value={editedGroup.description}
              onChange={(e) => updateGroup({ description: e.target.value })}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
              placeholder="输入结果组描述"
            />
          </div>
        </div>

        {/* 维度评分范围 */}
        <div>
          <h4 className="text-md font-medium text-gray-900 mb-4">维度评分范围</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {dimensions.map(dimension => (
              <div key={dimension} className="bg-gray-50 p-4 rounded-lg">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {dimension}
                </label>
                <div className="flex items-center gap-2">
                  <input
                    type="number"
                    min="0"
                    max="5"
                    step="0.1"
                    value={editedGroup.dimensionScores[dimension]?.[0] || 0}
                    onChange={(e) => updateDimensionScore(dimension, 0, parseFloat(e.target.value))}
                    className="w-20 px-2 py-1 border border-gray-300 rounded focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                  />
                  <span className="text-gray-500">-</span>
                  <input
                    type="number"
                    min="0"
                    max="5"
                    step="0.1"
                    value={editedGroup.dimensionScores[dimension]?.[1] || 5}
                    onChange={(e) => updateDimensionScore(dimension, 1, parseFloat(e.target.value))}
                    className="w-20 px-2 py-1 border border-gray-300 rounded focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                  />
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 产品分类 */}
        <div>
          <div className="flex justify-between items-center mb-4">
            <h4 className="text-md font-medium text-gray-900">关联产品分类</h4>
            <button
              onClick={addProductCategory}
              className="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 flex items-center gap-1"
            >
              <Plus className="h-3 w-3" />
              添加分类
            </button>
          </div>
          
          {editedGroup.productCategories.length === 0 ? (
            <div className="text-center py-4 bg-gray-50 rounded-lg">
              <p className="text-gray-600 text-sm">暂无关联的产品分类</p>
            </div>
          ) : (
            <div className="flex flex-wrap gap-2">
              {editedGroup.productCategories.map((category, index) => (
                <div key={index} className="bg-blue-100 text-blue-700 px-3 py-1 rounded-full flex items-center gap-2">
                  <span>{category}</span>
                  <button
                    onClick={() => removeProductCategory(index)}
                    className="text-blue-500 hover:text-blue-700"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

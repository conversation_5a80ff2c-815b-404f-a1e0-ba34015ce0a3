'use client';

import { useState } from 'react';
import { Plus, Trash2, Edit, Save, X, GripVertical } from 'lucide-react';
import { QuizQuestion, QuizOption, QuestionType } from '@/types';

interface QuestionEditorProps {
  questions: QuizQuestion[];
  dimensions: string[];
  onChange: (questions: QuizQuestion[]) => void;
}

export default function QuestionEditor({ questions, dimensions, onChange }: QuestionEditorProps) {
  const [editingQuestion, setEditingQuestion] = useState<QuizQuestion | null>(null);
  const [isCreating, setIsCreating] = useState(false);

  const handleAddQuestion = () => {
    const newQuestion: QuizQuestion = {
      id: `question_${Date.now()}`,
      text: '新问题',
      type: QuestionType.SINGLE_CHOICE,
      weight: 1,
      required: true,
      options: [
        {
          id: 'option_1',
          text: '选项 1',
          value: 'option_1',
          emoji: '🔵',
          score: {}
        }
      ]
    };
    setEditingQuestion(newQuestion);
    setIsCreating(true);
  };

  const handleSaveQuestion = (question: QuizQuestion) => {
    if (isCreating) {
      onChange([...questions, question]);
      setIsCreating(false);
    } else {
      const updatedQuestions = questions.map(q => 
        q.id === question.id ? question : q
      );
      onChange(updatedQuestions);
    }
    setEditingQuestion(null);
  };

  const handleDeleteQuestion = (questionId: string) => {
    const updatedQuestions = questions.filter(q => q.id !== questionId);
    onChange(updatedQuestions);
  };

  const handleCancelEdit = () => {
    setEditingQuestion(null);
    setIsCreating(false);
  };

  const moveQuestion = (fromIndex: number, toIndex: number) => {
    const newQuestions = [...questions];
    const [movedQuestion] = newQuestions.splice(fromIndex, 1);
    newQuestions.splice(toIndex, 0, movedQuestion);
    onChange(newQuestions);
  };

  if (editingQuestion) {
    return (
      <QuestionEditForm
        question={editingQuestion}
        dimensions={dimensions}
        onSave={handleSaveQuestion}
        onCancel={handleCancelEdit}
        isCreating={isCreating}
      />
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold text-gray-900">问题管理</h3>
        <button
          onClick={handleAddQuestion}
          className="bg-pink-600 text-white px-4 py-2 rounded-lg hover:bg-pink-700 flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          添加问题
        </button>
      </div>

      {questions.length === 0 ? (
        <div className="text-center py-8 bg-gray-50 rounded-lg">
          <p className="text-gray-600">暂无问题，点击"添加问题"开始创建</p>
        </div>
      ) : (
        <div className="space-y-3">
          {questions.map((question, index) => (
            <div key={question.id} className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex items-start justify-between">
                <div className="flex items-start gap-3 flex-1">
                  <button className="mt-1 text-gray-400 hover:text-gray-600">
                    <GripVertical className="h-4 w-4" />
                  </button>
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <span className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-sm font-medium">
                        {index + 1}
                      </span>
                      <span className="bg-blue-100 text-blue-700 px-2 py-1 rounded text-sm">
                        {question.type}
                      </span>
                      {question.weight !== 1 && (
                        <span className="bg-orange-100 text-orange-700 px-2 py-1 rounded text-sm">
                          权重: {question.weight}
                        </span>
                      )}
                      {!question.required && (
                        <span className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-sm">
                          可选
                        </span>
                      )}
                    </div>
                    <h4 className="font-medium text-gray-900 mb-2">{question.text}</h4>
                    {question.options && (
                      <div className="text-sm text-gray-600">
                        {question.options.length} 个选项
                      </div>
                    )}
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => setEditingQuestion(question)}
                    className="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded"
                    title="编辑"
                  >
                    <Edit className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => handleDeleteQuestion(question.id)}
                    className="p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded"
                    title="删除"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

// Question Edit Form Component
interface QuestionEditFormProps {
  question: QuizQuestion;
  dimensions: string[];
  onSave: (question: QuizQuestion) => void;
  onCancel: () => void;
  isCreating: boolean;
}

function QuestionEditForm({ question, dimensions, onSave, onCancel, isCreating }: QuestionEditFormProps) {
  const [editedQuestion, setEditedQuestion] = useState<QuizQuestion>(question);

  const updateQuestion = (updates: Partial<QuizQuestion>) => {
    setEditedQuestion(prev => ({ ...prev, ...updates }));
  };

  const addOption = () => {
    const newOption: QuizOption = {
      id: `option_${Date.now()}`,
      text: '新选项',
      value: `option_${Date.now()}`,
      emoji: '🔵',
      score: {}
    };
    updateQuestion({
      options: [...(editedQuestion.options || []), newOption]
    });
  };

  const updateOption = (optionIndex: number, updates: Partial<QuizOption>) => {
    const updatedOptions = [...(editedQuestion.options || [])];
    updatedOptions[optionIndex] = { ...updatedOptions[optionIndex], ...updates };
    updateQuestion({ options: updatedOptions });
  };

  const removeOption = (optionIndex: number) => {
    const updatedOptions = [...(editedQuestion.options || [])];
    updatedOptions.splice(optionIndex, 1);
    updateQuestion({ options: updatedOptions });
  };

  const handleSave = () => {
    // 验证问题
    if (!editedQuestion.text.trim()) {
      alert('请输入问题文本');
      return;
    }

    if (editedQuestion.type === QuestionType.SINGLE_CHOICE || editedQuestion.type === QuestionType.MULTIPLE_CHOICE) {
      if (!editedQuestion.options || editedQuestion.options.length === 0) {
        alert('请至少添加一个选项');
        return;
      }
    }

    onSave(editedQuestion);
  };

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6">
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-lg font-semibold text-gray-900">
          {isCreating ? '创建新问题' : '编辑问题'}
        </h3>
        <div className="flex gap-2">
          <button
            onClick={handleSave}
            className="bg-pink-600 text-white px-4 py-2 rounded-lg hover:bg-pink-700 flex items-center gap-2"
          >
            <Save className="h-4 w-4" />
            保存
          </button>
          <button
            onClick={onCancel}
            className="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 flex items-center gap-2"
          >
            <X className="h-4 w-4" />
            取消
          </button>
        </div>
      </div>

      <div className="space-y-6">
        {/* 基本信息 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              问题文本 *
            </label>
            <textarea
              value={editedQuestion.text}
              onChange={(e) => updateQuestion({ text: e.target.value })}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
              placeholder="输入问题文本"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              问题类型 *
            </label>
            <select
              value={editedQuestion.type}
              onChange={(e) => updateQuestion({ type: e.target.value as QuestionType })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
            >
              <option value={QuestionType.SINGLE_CHOICE}>单选题</option>
              <option value={QuestionType.MULTIPLE_CHOICE}>多选题</option>
              <option value={QuestionType.SLIDER}>滑块题</option>
              <option value={QuestionType.RATING}>评分题</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              权重
            </label>
            <input
              type="number"
              min="0.1"
              max="5"
              step="0.1"
              value={editedQuestion.weight || 1}
              onChange={(e) => updateQuestion({ weight: parseFloat(e.target.value) })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
            />
          </div>
          <div className="flex items-center">
            <input
              type="checkbox"
              id="required"
              checked={editedQuestion.required !== false}
              onChange={(e) => updateQuestion({ required: e.target.checked })}
              className="h-4 w-4 text-pink-600 focus:ring-pink-500 border-gray-300 rounded"
            />
            <label htmlFor="required" className="ml-2 block text-sm text-gray-900">
              必答题
            </label>
          </div>
        </div>

        {/* 选项编辑 */}
        {(editedQuestion.type === QuestionType.SINGLE_CHOICE || editedQuestion.type === QuestionType.MULTIPLE_CHOICE) && (
          <div>
            <div className="flex justify-between items-center mb-4">
              <h4 className="text-md font-medium text-gray-900">选项设置</h4>
              <button
                onClick={addOption}
                className="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 flex items-center gap-1"
              >
                <Plus className="h-3 w-3" />
                添加选项
              </button>
            </div>
            
            <div className="space-y-3">
              {editedQuestion.options?.map((option, index) => (
                <div key={option.id} className="bg-gray-50 p-4 rounded-lg">
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-3 mb-3">
                    <input
                      type="text"
                      value={option.text}
                      onChange={(e) => updateOption(index, { text: e.target.value })}
                      placeholder="选项文本"
                      className="px-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                    />
                    <input
                      type="text"
                      value={option.value}
                      onChange={(e) => updateOption(index, { value: e.target.value })}
                      placeholder="选项值"
                      className="px-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                    />
                    <input
                      type="text"
                      value={option.emoji || ''}
                      onChange={(e) => updateOption(index, { emoji: e.target.value })}
                      placeholder="表情符号"
                      className="px-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                    />
                    <button
                      onClick={() => removeOption(index)}
                      className="bg-red-600 text-white px-3 py-2 rounded hover:bg-red-700 flex items-center justify-center"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                  
                  {/* 维度评分 */}
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                    {dimensions.map(dimension => (
                      <div key={dimension}>
                        <label className="block text-xs text-gray-600 mb-1">
                          {dimension}
                        </label>
                        <input
                          type="number"
                          min="0"
                          max="5"
                          step="0.1"
                          value={option.score?.[dimension] || ''}
                          onChange={(e) => {
                            const score = { ...option.score };
                            if (e.target.value) {
                              score[dimension] = parseFloat(e.target.value);
                            } else {
                              delete score[dimension];
                            }
                            updateOption(index, { score });
                          }}
                          placeholder="0-5"
                          className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-pink-500 focus:border-pink-500"
                        />
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 滑块和评分题的特殊设置 */}
        {(editedQuestion.type === QuestionType.SLIDER || editedQuestion.type === QuestionType.RATING) && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                最小值
              </label>
              <input
                type="number"
                value={editedQuestion.min || 0}
                onChange={(e) => updateQuestion({ min: parseInt(e.target.value) })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                最大值
              </label>
              <input
                type="number"
                value={editedQuestion.max || 5}
                onChange={(e) => updateQuestion({ max: parseInt(e.target.value) })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                步长
              </label>
              <input
                type="number"
                min="0.1"
                step="0.1"
                value={editedQuestion.step || 1}
                onChange={(e) => updateQuestion({ step: parseFloat(e.target.value) })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

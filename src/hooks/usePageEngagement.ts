'use client';

import { useEffect, useRef, useCallback } from 'react';
import { usePathname } from 'next/navigation';
import { analyticsService } from '@/lib/analytics';
import { SessionManager } from '@/lib/session';

interface EngagementData {
  startTime: number;
  scrollDepth: number;
  clickCount: number;
  maxScrollDepth: number;
  isActive: boolean;
  lastActivityTime: number;
}

export function usePageEngagement() {
  const pathname = usePathname();
  const engagementRef = useRef<EngagementData>({
    startTime: Date.now(),
    scrollDepth: 0,
    clickCount: 0,
    maxScrollDepth: 0,
    isActive: true,
    lastActivityTime: Date.now()
  });
  const sessionManager = SessionManager.getInstance();

  // 计算滚动深度
  const calculateScrollDepth = useCallback(() => {
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const documentHeight = document.documentElement.scrollHeight - document.documentElement.clientHeight;
    
    if (documentHeight === 0) return 0;
    
    const scrollPercentage = Math.round((scrollTop / documentHeight) * 100);
    return Math.min(100, Math.max(0, scrollPercentage));
  }, []);

  // 更新滚动深度
  const updateScrollDepth = useCallback(() => {
    const currentScrollDepth = calculateScrollDepth();
    engagementRef.current.scrollDepth = currentScrollDepth;
    engagementRef.current.maxScrollDepth = Math.max(
      engagementRef.current.maxScrollDepth,
      currentScrollDepth
    );
    engagementRef.current.lastActivityTime = Date.now();
  }, [calculateScrollDepth]);

  // 记录点击事件
  const recordClick = useCallback(() => {
    engagementRef.current.clickCount++;
    engagementRef.current.lastActivityTime = Date.now();
  }, []);

  // 检查用户是否活跃
  const checkUserActivity = useCallback(() => {
    const now = Date.now();
    const timeSinceLastActivity = now - engagementRef.current.lastActivityTime;
    
    // 如果超过30秒没有活动，认为用户不活跃
    if (timeSinceLastActivity > 30000) {
      engagementRef.current.isActive = false;
    }
  }, []);

  // 发送参与度数据
  const sendEngagementData = useCallback(async (isExit: boolean = false) => {
    const engagement = engagementRef.current;
    const now = Date.now();
    const timeOnPage = Math.round((now - engagement.startTime) / 1000); // 转换为秒
    
    // 只有在页面停留超过5秒时才发送数据
    if (timeOnPage < 5) return;

    const sessionId = sessionManager.getSessionId();
    
    // 判断是否为跳出（停留时间少于30秒且滚动深度小于25%）
    const bounced = timeOnPage < 30 && engagement.maxScrollDepth < 25;

    try {
      await analyticsService.trackPageEngagement({
        sessionId,
        pagePath: pathname,
        pageTitle: document.title,
        timeOnPage,
        scrollDepth: engagement.maxScrollDepth,
        clickCount: engagement.clickCount,
        exitPage: isExit,
        bounced
      });
    } catch (error) {
      console.error('Failed to send engagement data:', error);
    }
  }, [pathname, sessionManager]);

  // 页面可见性变化处理
  const handleVisibilityChange = useCallback(() => {
    if (document.hidden) {
      // 页面隐藏时发送数据
      sendEngagementData(true);
    } else {
      // 页面重新可见时重置计时
      engagementRef.current.startTime = Date.now();
      engagementRef.current.isActive = true;
      engagementRef.current.lastActivityTime = Date.now();
    }
  }, [sendEngagementData]);

  // 页面卸载前发送数据
  const handleBeforeUnload = useCallback(() => {
    sendEngagementData(true);
  }, [sendEngagementData]);

  // 鼠标移动处理（重置活跃状态）
  const handleMouseMove = useCallback(() => {
    engagementRef.current.isActive = true;
    engagementRef.current.lastActivityTime = Date.now();
  }, []);

  // 键盘事件处理（重置活跃状态）
  const handleKeyPress = useCallback(() => {
    engagementRef.current.isActive = true;
    engagementRef.current.lastActivityTime = Date.now();
  }, []);

  useEffect(() => {
    // 重置参与度数据（新页面）
    engagementRef.current = {
      startTime: Date.now(),
      scrollDepth: 0,
      clickCount: 0,
      maxScrollDepth: 0,
      isActive: true,
      lastActivityTime: Date.now()
    };

    // 添加事件监听器
    window.addEventListener('scroll', updateScrollDepth, { passive: true });
    window.addEventListener('click', recordClick, { passive: true });
    window.addEventListener('mousemove', handleMouseMove, { passive: true });
    window.addEventListener('keydown', handleKeyPress, { passive: true });
    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('beforeunload', handleBeforeUnload);

    // 定期检查用户活跃状态
    const activityCheckInterval = setInterval(checkUserActivity, 10000); // 每10秒检查一次

    // 定期发送参与度数据（每2分钟）
    const engagementInterval = setInterval(() => {
      if (engagementRef.current.isActive) {
        sendEngagementData(false);
        // 重置计时器
        engagementRef.current.startTime = Date.now();
      }
    }, 120000); // 2分钟

    return () => {
      // 页面卸载时发送最终数据
      sendEngagementData(true);
      
      // 清理事件监听器
      window.removeEventListener('scroll', updateScrollDepth);
      window.removeEventListener('click', recordClick);
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('keydown', handleKeyPress);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('beforeunload', handleBeforeUnload);
      
      // 清理定时器
      clearInterval(activityCheckInterval);
      clearInterval(engagementInterval);
    };
  }, [
    pathname,
    updateScrollDepth,
    recordClick,
    handleMouseMove,
    handleKeyPress,
    handleVisibilityChange,
    handleBeforeUnload,
    checkUserActivity,
    sendEngagementData
  ]);

  // 返回当前参与度数据（用于调试或显示）
  const getCurrentEngagement = useCallback(() => {
    const now = Date.now();
    const timeOnPage = Math.round((now - engagementRef.current.startTime) / 1000);
    
    return {
      timeOnPage,
      scrollDepth: engagementRef.current.scrollDepth,
      maxScrollDepth: engagementRef.current.maxScrollDepth,
      clickCount: engagementRef.current.clickCount,
      isActive: engagementRef.current.isActive
    };
  }, []);

  return {
    getCurrentEngagement
  };
}

// 简化版的参与度跟踪Hook（仅用于关键页面）
export function useSimpleEngagement() {
  const pathname = usePathname();
  const startTimeRef = useRef<number>(Date.now());
  const sessionManager = SessionManager.getInstance();

  useEffect(() => {
    startTimeRef.current = Date.now();

    const handleBeforeUnload = async () => {
      const timeOnPage = Math.round((Date.now() - startTimeRef.current) / 1000);
      
      if (timeOnPage > 5) { // 只记录停留超过5秒的页面
        const sessionId = sessionManager.getSessionId();
        
        try {
          await analyticsService.trackPageEngagement({
            sessionId,
            pagePath: pathname,
            pageTitle: document.title,
            timeOnPage,
            scrollDepth: 0, // 简化版不跟踪滚动
            clickCount: 0, // 简化版不跟踪点击
            exitPage: true,
            bounced: timeOnPage < 30
          });
        } catch (error) {
          console.error('Failed to send simple engagement data:', error);
        }
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [pathname, sessionManager]);
}

'use client';

import { useEffect, useRef } from 'react';
import { usePathname } from 'next/navigation';
import { 
  analyticsService, 
  detectBrowser, 
  detectOS, 
  detectDeviceType, 
  generateSessionId 
} from '@/lib/analytics';

// 会话管理
class SessionManager {
  private static instance: SessionManager;
  private sessionId: string | null = null;
  private sessionStartTime: number = 0;

  private constructor() {}

  static getInstance(): SessionManager {
    if (!SessionManager.instance) {
      SessionManager.instance = new SessionManager();
    }
    return SessionManager.instance;
  }

  getSessionId(): string {
    if (!this.sessionId) {
      // 尝试从sessionStorage获取现有会话ID
      if (typeof window !== 'undefined') {
        const stored = sessionStorage.getItem('analytics_session_id');
        const sessionStart = sessionStorage.getItem('analytics_session_start');
        
        // 检查会话是否过期（30分钟）
        if (stored && sessionStart) {
          const startTime = parseInt(sessionStart);
          const now = Date.now();
          if (now - startTime < 30 * 60 * 1000) { // 30分钟
            this.sessionId = stored;
            this.sessionStartTime = startTime;
            return this.sessionId;
          }
        }
      }
      
      // 创建新会话
      this.sessionId = generateSessionId();
      this.sessionStartTime = Date.now();
      
      if (typeof window !== 'undefined') {
        sessionStorage.setItem('analytics_session_id', this.sessionId);
        sessionStorage.setItem('analytics_session_start', this.sessionStartTime.toString());
      }
    }
    
    return this.sessionId;
  }

  getSessionDuration(): number {
    return Date.now() - this.sessionStartTime;
  }
}

// 获取客户端信息
const getClientInfo = () => {
  if (typeof window === 'undefined') {
    return {
      userAgent: '',
      referrer: '',
      deviceType: 'desktop' as const,
      browser: 'Unknown',
      os: 'Unknown'
    };
  }

  const userAgent = navigator.userAgent;
  
  return {
    userAgent,
    referrer: document.referrer,
    deviceType: detectDeviceType(userAgent),
    browser: detectBrowser(userAgent),
    os: detectOS(userAgent)
  };
};

// 页面访问跟踪Hook
export const usePageTracking = () => {
  const pathname = usePathname();
  const sessionManager = SessionManager.getInstance();
  const trackedPages = useRef(new Set<string>());

  useEffect(() => {
    // 避免重复跟踪同一页面
    if (trackedPages.current.has(pathname)) {
      return;
    }

    const trackPageView = async () => {
      try {
        const clientInfo = getClientInfo();
        const sessionId = sessionManager.getSessionId();

        await analyticsService.trackPageView({
          pagePath: pathname,
          pageTitle: typeof document !== 'undefined' ? document.title : '',
          sessionId,
          ...clientInfo
        });

        trackedPages.current.add(pathname);
      } catch (error) {
        console.error('Failed to track page view:', error);
      }
    };

    // 延迟执行，确保页面完全加载
    const timer = setTimeout(trackPageView, 1000);

    return () => {
      clearTimeout(timer);
    };
  }, [pathname, sessionManager]);
};

// Amazon点击跟踪Hook
export const useAmazonClickTracking = () => {
  const pathname = usePathname();
  const sessionManager = SessionManager.getInstance();

  const trackAmazonClick = async (productId?: string, productName?: string, amazonUrl?: string) => {
    try {
      const clientInfo = getClientInfo();
      const sessionId = sessionManager.getSessionId();

      await analyticsService.trackAmazonClick({
        productId,
        productName,
        amazonUrl: amazonUrl || '',
        pagePath: pathname,
        sessionId,
        ...clientInfo
      });

      // 同时发送到Google Analytics（如果配置了）
      if (typeof window !== 'undefined' && window.gtag) {
        window.gtag('event', 'amazon_click', {
          event_category: 'Amazon Link',
          event_label: productName || productId || 'Unknown Product',
          value: 1
        });
      }
    } catch (error) {
      console.error('Failed to track Amazon click:', error);
    }
  };

  return { trackAmazonClick };
};

// 通用事件跟踪Hook
export const useEventTracking = () => {
  const trackEvent = (eventName: string, eventData?: Record<string, any>) => {
    try {
      // 发送到Google Analytics
      if (typeof window !== 'undefined' && window.gtag) {
        window.gtag('event', eventName, {
          event_category: 'User Interaction',
          ...eventData
        });
      }

      // 这里可以添加其他分析服务的事件跟踪
      console.log('Event tracked:', eventName, eventData);
    } catch (error) {
      console.error('Failed to track event:', error);
    }
  };

  return { trackEvent };
};

// 性能监控Hook
export const usePerformanceTracking = () => {
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const trackPerformance = () => {
      try {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        
        if (navigation) {
          const metrics = {
            loadTime: navigation.loadEventEnd - navigation.loadEventStart,
            domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
            firstPaint: 0,
            firstContentfulPaint: 0
          };

          // 获取Paint Timing
          const paintEntries = performance.getEntriesByType('paint');
          paintEntries.forEach((entry) => {
            if (entry.name === 'first-paint') {
              metrics.firstPaint = entry.startTime;
            } else if (entry.name === 'first-contentful-paint') {
              metrics.firstContentfulPaint = entry.startTime;
            }
          });

          // 发送性能数据到Google Analytics
          if (window.gtag) {
            window.gtag('event', 'page_performance', {
              event_category: 'Performance',
              custom_map: {
                load_time: metrics.loadTime,
                dom_content_loaded: metrics.domContentLoaded,
                first_paint: metrics.firstPaint,
                first_contentful_paint: metrics.firstContentfulPaint
              }
            });
          }
        }
      } catch (error) {
        console.error('Failed to track performance:', error);
      }
    };

    // 等待页面完全加载后再收集性能数据
    if (document.readyState === 'complete') {
      trackPerformance();
    } else {
      window.addEventListener('load', trackPerformance);
      return () => window.removeEventListener('load', trackPerformance);
    }
  }, []);
};

// 滚动深度跟踪Hook
export const useScrollTracking = () => {
  useEffect(() => {
    if (typeof window === 'undefined') return;

    let maxScrollDepth = 0;
    const scrollMilestones = [25, 50, 75, 90, 100];
    const trackedMilestones = new Set<number>();

    const handleScroll = () => {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const documentHeight = document.documentElement.scrollHeight - window.innerHeight;
      const scrollDepth = Math.round((scrollTop / documentHeight) * 100);

      if (scrollDepth > maxScrollDepth) {
        maxScrollDepth = scrollDepth;

        // 检查是否达到新的里程碑
        scrollMilestones.forEach(milestone => {
          if (scrollDepth >= milestone && !trackedMilestones.has(milestone)) {
            trackedMilestones.add(milestone);
            
            // 发送滚动深度事件
            if (window.gtag) {
              window.gtag('event', 'scroll_depth', {
                event_category: 'User Engagement',
                event_label: `${milestone}%`,
                value: milestone
              });
            }
          }
        });
      }
    };

    const throttledHandleScroll = throttle(handleScroll, 500);
    window.addEventListener('scroll', throttledHandleScroll);

    return () => {
      window.removeEventListener('scroll', throttledHandleScroll);
    };
  }, []);
};

// 节流函数
function throttle<T extends (...args: any[]) => any>(func: T, limit: number): T {
  let inThrottle: boolean;
  return ((...args: any[]) => {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  }) as T;
}

import { useEffect, useRef, useState } from 'react';

interface UseScrollRevealOptions {
  threshold?: number;
  rootMargin?: string;
  triggerOnce?: boolean;
  delay?: number;
  duration?: number;
}

interface ScrollRevealState {
  isVisible: boolean;
  hasAnimated: boolean;
}

export function useScrollReveal(options: UseScrollRevealOptions = {}) {
  const {
    threshold = 0.2, // Trigger when 20% visible
    rootMargin = '0px 0px -10% 0px', // Trigger slightly before element enters viewport
    triggerOnce = true,
    delay = 0,
    duration = 600
  } = options;

  const elementRef = useRef<HTMLElement>(null);
  const [state, setState] = useState<ScrollRevealState>({
    isVisible: false,
    hasAnimated: false
  });

  // Check for reduced motion preference
  const prefersReducedMotion = typeof window !== 'undefined' 
    ? window.matchMedia('(prefers-reduced-motion: reduce)').matches 
    : false;

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    // If user prefers reduced motion, show immediately
    if (prefersReducedMotion) {
      setState({ isVisible: true, hasAnimated: true });
      return;
    }

    // If already animated and triggerOnce is true, don't observe again
    if (state.hasAnimated && triggerOnce) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            // Apply delay if specified
            if (delay > 0) {
              setTimeout(() => {
                setState(prev => ({ 
                  isVisible: true, 
                  hasAnimated: true 
                }));
              }, delay);
            } else {
              setState(prev => ({ 
                isVisible: true, 
                hasAnimated: true 
              }));
            }

            // If triggerOnce is true, stop observing after first trigger
            if (triggerOnce) {
              observer.unobserve(entry.target);
            }
          } else if (!triggerOnce) {
            // If not triggerOnce, hide when out of view
            setState(prev => ({ 
              ...prev, 
              isVisible: false 
            }));
          }
        });
      },
      {
        threshold,
        rootMargin
      }
    );

    observer.observe(element);

    return () => {
      observer.disconnect();
    };
  }, [threshold, rootMargin, triggerOnce, delay, state.hasAnimated, prefersReducedMotion]);

  return {
    ref: elementRef,
    isVisible: state.isVisible,
    hasAnimated: state.hasAnimated,
    prefersReducedMotion,
    duration
  };
}

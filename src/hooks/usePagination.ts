'use client';

import { useState, useCallback, useMemo } from 'react';
import { PaginationInfo, PaginationParams, calculatePagination, createPaginationInfo } from '@/components/Pagination';

interface UsePaginationOptions {
  initialPage?: number;
  initialPageSize?: number;
  totalItems?: number;
}

interface UsePaginationReturn {
  // 分页状态
  currentPage: number;
  pageSize: number;
  totalItems: number;
  
  // 分页信息
  paginationInfo: PaginationInfo;
  paginationParams: PaginationParams;
  
  // 操作方法
  setPage: (page: number) => void;
  setPageSize: (size: number) => void;
  setTotalItems: (total: number) => void;
  nextPage: () => void;
  prevPage: () => void;
  firstPage: () => void;
  lastPage: () => void;
  
  // 重置方法
  reset: () => void;
}

export function usePagination({
  initialPage = 1,
  initialPageSize = 20,
  totalItems = 0
}: UsePaginationOptions = {}): UsePaginationReturn {
  const [currentPage, setCurrentPage] = useState(initialPage);
  const [pageSize, setPageSizeState] = useState(initialPageSize);
  const [totalItemsState, setTotalItemsState] = useState(totalItems);

  // 计算分页信息
  const paginationInfo = useMemo(() => {
    return createPaginationInfo(currentPage, pageSize, totalItemsState);
  }, [currentPage, pageSize, totalItemsState]);

  // 计算分页参数
  const paginationParams = useMemo(() => {
    return calculatePagination(currentPage, pageSize);
  }, [currentPage, pageSize]);

  // 设置当前页
  const setPage = useCallback((page: number) => {
    const maxPage = Math.ceil(totalItemsState / pageSize) || 1;
    const validPage = Math.max(1, Math.min(page, maxPage));
    setCurrentPage(validPage);
  }, [totalItemsState, pageSize]);

  // 设置每页大小
  const setPageSize = useCallback((size: number) => {
    const newSize = Math.max(1, size);
    setPageSizeState(newSize);
    
    // 重新计算当前页，确保不超出范围
    const maxPage = Math.ceil(totalItemsState / newSize) || 1;
    if (currentPage > maxPage) {
      setCurrentPage(maxPage);
    }
  }, [totalItemsState, currentPage]);

  // 设置总项目数
  const setTotalItems = useCallback((total: number) => {
    const newTotal = Math.max(0, total);
    setTotalItemsState(newTotal);
    
    // 如果当前页超出范围，调整到最后一页
    const maxPage = Math.ceil(newTotal / pageSize) || 1;
    if (currentPage > maxPage) {
      setCurrentPage(maxPage);
    }
  }, [pageSize, currentPage]);

  // 下一页
  const nextPage = useCallback(() => {
    if (paginationInfo.hasNextPage) {
      setPage(currentPage + 1);
    }
  }, [currentPage, paginationInfo.hasNextPage, setPage]);

  // 上一页
  const prevPage = useCallback(() => {
    if (paginationInfo.hasPrevPage) {
      setPage(currentPage - 1);
    }
  }, [currentPage, paginationInfo.hasPrevPage, setPage]);

  // 第一页
  const firstPage = useCallback(() => {
    setPage(1);
  }, [setPage]);

  // 最后一页
  const lastPage = useCallback(() => {
    setPage(paginationInfo.totalPages);
  }, [paginationInfo.totalPages, setPage]);

  // 重置分页
  const reset = useCallback(() => {
    setCurrentPage(initialPage);
    setPageSizeState(initialPageSize);
    setTotalItemsState(0);
  }, [initialPage, initialPageSize]);

  return {
    // 状态
    currentPage,
    pageSize,
    totalItems: totalItemsState,
    
    // 信息
    paginationInfo,
    paginationParams,
    
    // 方法
    setPage,
    setPageSize,
    setTotalItems,
    nextPage,
    prevPage,
    firstPage,
    lastPage,
    reset
  };
}

// 搜索和过滤的Hook
interface UseSearchAndFilterOptions {
  initialSearchTerm?: string;
  initialFilters?: Record<string, any>;
}

interface UseSearchAndFilterReturn {
  searchTerm: string;
  filters: Record<string, any>;
  setSearchTerm: (term: string) => void;
  setFilter: (key: string, value: any) => void;
  setFilters: (filters: Record<string, any>) => void;
  clearFilters: () => void;
  clearAll: () => void;
}

export function useSearchAndFilter({
  initialSearchTerm = '',
  initialFilters = {}
}: UseSearchAndFilterOptions = {}): UseSearchAndFilterReturn {
  const [searchTerm, setSearchTerm] = useState(initialSearchTerm);
  const [filters, setFiltersState] = useState(initialFilters);

  const setFilter = useCallback((key: string, value: any) => {
    setFiltersState(prev => ({
      ...prev,
      [key]: value
    }));
  }, []);

  const setFilters = useCallback((newFilters: Record<string, any>) => {
    setFiltersState(newFilters);
  }, []);

  const clearFilters = useCallback(() => {
    setFiltersState({});
  }, []);

  const clearAll = useCallback(() => {
    setSearchTerm('');
    setFiltersState({});
  }, []);

  return {
    searchTerm,
    filters,
    setSearchTerm,
    setFilter,
    setFilters,
    clearFilters,
    clearAll
  };
}

// 组合分页和搜索过滤的Hook
interface UsePaginatedDataOptions extends UsePaginationOptions, UseSearchAndFilterOptions {
  debounceMs?: number;
}

interface UsePaginatedDataReturn extends UsePaginationReturn, UseSearchAndFilterReturn {
  // 组合状态
  isFiltered: boolean;
  hasData: boolean;
  
  // 重置所有
  resetAll: () => void;
}

export function usePaginatedData({
  initialPage = 1,
  initialPageSize = 20,
  totalItems = 0,
  initialSearchTerm = '',
  initialFilters = {},
  debounceMs = 300
}: UsePaginatedDataOptions = {}): UsePaginatedDataReturn {
  const pagination = usePagination({
    initialPage,
    initialPageSize,
    totalItems
  });

  const searchAndFilter = useSearchAndFilter({
    initialSearchTerm,
    initialFilters
  });

  // 检查是否有过滤条件
  const isFiltered = useMemo(() => {
    return searchAndFilter.searchTerm.length > 0 || 
           Object.keys(searchAndFilter.filters).length > 0;
  }, [searchAndFilter.searchTerm, searchAndFilter.filters]);

  // 检查是否有数据
  const hasData = useMemo(() => {
    return pagination.totalItems > 0;
  }, [pagination.totalItems]);

  // 重置所有状态
  const resetAll = useCallback(() => {
    pagination.reset();
    searchAndFilter.clearAll();
  }, [pagination, searchAndFilter]);

  return {
    ...pagination,
    ...searchAndFilter,
    isFiltered,
    hasData,
    resetAll
  };
}

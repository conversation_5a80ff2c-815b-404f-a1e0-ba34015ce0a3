import { productService, articleService } from '@/lib/database';
import { sampleProducts, generateSampleArticle } from '@/data/sampleData';

async function seedDatabase() {
  try {
    console.log('🌱 开始初始化数据库...');

    // 1. 创建示例产品
    console.log('📦 创建示例产品...');
    const createdProducts = [];
    
    for (const productData of sampleProducts) {
      try {
        const product = await productService.create(productData);
        createdProducts.push(product);
        console.log(`✅ 创建产品: ${product.name}`);
      } catch (error) {
        console.error(`❌ 创建产品失败: ${productData.name}`, error);
      }
    }

    // 2. 为每个产品创建对应的文章
    console.log('📝 创建示例文章...');
    
    for (const product of createdProducts) {
      try {
        const articleData = generateSampleArticle(product);
        const article = await articleService.create(articleData);
        console.log(`✅ 创建文章: ${article.title}`);
      } catch (error) {
        console.error(`❌ 创建文章失败: ${product.name}`, error);
      }
    }

    console.log('🎉 数据库初始化完成！');
    console.log(`📊 统计信息:`);
    console.log(`   - 产品数量: ${createdProducts.length}`);
    console.log(`   - 文章数量: ${createdProducts.length}`);

  } catch (error) {
    console.error('💥 数据库初始化失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  seedDatabase();
}

export default seedDatabase;

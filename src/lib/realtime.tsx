'use client';

import React from 'react';
import { statsService } from './database';
import { analyticsService } from './analytics';

// 实时数据类型定义
export interface RealtimeStats {
  totalPageViews: number;
  totalAmazonClicks: number;
  uniqueVisitors: number;
  conversionRate: string;
  lastUpdated: Date;
}

export interface RealtimeEvent {
  type: 'stats_update' | 'new_visitor' | 'amazon_click' | 'page_view';
  data: any;
  timestamp: Date;
}

// 实时数据管理器
class RealtimeManager {
  private static instance: RealtimeManager;
  private subscribers: Map<string, (event: RealtimeEvent) => void> = new Map();
  private updateInterval: NodeJS.Timeout | null = null;
  private lastStats: RealtimeStats | null = null;
  private isActive = false;

  private constructor() {}

  static getInstance(): RealtimeManager {
    if (!RealtimeManager.instance) {
      RealtimeManager.instance = new RealtimeManager();
    }
    return RealtimeManager.instance;
  }

  // 订阅实时更新
  subscribe(id: string, callback: (event: RealtimeEvent) => void): () => void {
    this.subscribers.set(id, callback);
    
    // 如果是第一个订阅者，启动实时更新
    if (this.subscribers.size === 1) {
      this.startRealtime();
    }

    // 返回取消订阅函数
    return () => {
      this.subscribers.delete(id);
      
      // 如果没有订阅者了，停止实时更新
      if (this.subscribers.size === 0) {
        this.stopRealtime();
      }
    };
  }

  // 启动实时更新
  private startRealtime() {
    if (this.isActive) return;
    
    this.isActive = true;
    console.log('🔄 启动实时数据更新');

    // 立即获取一次数据
    this.fetchAndBroadcastStats();

    // 设置定时更新（每30秒）
    this.updateInterval = setInterval(() => {
      this.fetchAndBroadcastStats();
    }, 30000);
  }

  // 停止实时更新
  private stopRealtime() {
    if (!this.isActive) return;
    
    this.isActive = false;
    console.log('⏹️ 停止实时数据更新');

    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
  }

  // 获取并广播统计数据
  private async fetchAndBroadcastStats() {
    try {
      const [
        totalPageViews,
        totalAmazonClicks,
        uniqueVisitors
      ] = await Promise.all([
        analyticsService.getPageViewStats(),
        analyticsService.getAmazonClickStats(),
        analyticsService.getUniqueVisitors()
      ]);

      const newStats: RealtimeStats = {
        totalPageViews,
        totalAmazonClicks,
        uniqueVisitors,
        conversionRate: totalPageViews > 0 ? (totalAmazonClicks / totalPageViews * 100).toFixed(2) : '0',
        lastUpdated: new Date()
      };

      // 检查数据是否有变化
      const hasChanged = !this.lastStats || 
        this.lastStats.totalPageViews !== newStats.totalPageViews ||
        this.lastStats.totalAmazonClicks !== newStats.totalAmazonClicks ||
        this.lastStats.uniqueVisitors !== newStats.uniqueVisitors;

      if (hasChanged) {
        this.lastStats = newStats;
        this.broadcast({
          type: 'stats_update',
          data: newStats,
          timestamp: new Date()
        });
      }
    } catch (error) {
      console.error('获取实时统计数据失败:', error);
    }
  }

  // 广播事件给所有订阅者
  private broadcast(event: RealtimeEvent) {
    this.subscribers.forEach(callback => {
      try {
        callback(event);
      } catch (error) {
        console.error('实时事件回调错误:', error);
      }
    });
  }

  // 手动触发特定事件
  triggerEvent(type: RealtimeEvent['type'], data: any) {
    this.broadcast({
      type,
      data,
      timestamp: new Date()
    });

    // 如果是重要事件，立即刷新统计数据
    if (type === 'amazon_click' || type === 'page_view') {
      setTimeout(() => {
        this.fetchAndBroadcastStats();
      }, 1000); // 延迟1秒确保数据已写入数据库
    }
  }

  // 获取当前统计数据
  getCurrentStats(): RealtimeStats | null {
    return this.lastStats;
  }

  // 强制刷新数据
  async forceRefresh() {
    await this.fetchAndBroadcastStats();
  }
}

// 导出单例实例
export const realtimeManager = RealtimeManager.getInstance();

// React Hook for 实时统计数据
export function useRealtimeStats() {
  const [stats, setStats] = React.useState<RealtimeStats | null>(null);
  const [isConnected, setIsConnected] = React.useState(false);
  const [lastEvent, setLastEvent] = React.useState<RealtimeEvent | null>(null);

  React.useEffect(() => {
    const subscriberId = `stats-${Date.now()}-${Math.random()}`;
    
    const unsubscribe = realtimeManager.subscribe(subscriberId, (event) => {
      setLastEvent(event);
      
      if (event.type === 'stats_update') {
        setStats(event.data);
      }
    });

    setIsConnected(true);

    // 获取当前数据
    const currentStats = realtimeManager.getCurrentStats();
    if (currentStats) {
      setStats(currentStats);
    }

    return () => {
      unsubscribe();
      setIsConnected(false);
    };
  }, []);

  const forceRefresh = React.useCallback(() => {
    realtimeManager.forceRefresh();
  }, []);

  return {
    stats,
    isConnected,
    lastEvent,
    forceRefresh
  };
}

// React Hook for 实时事件
export function useRealtimeEvents() {
  const [events, setEvents] = React.useState<RealtimeEvent[]>([]);
  const [isConnected, setIsConnected] = React.useState(false);

  React.useEffect(() => {
    const subscriberId = `events-${Date.now()}-${Math.random()}`;
    
    const unsubscribe = realtimeManager.subscribe(subscriberId, (event) => {
      setEvents(prev => [event, ...prev.slice(0, 49)]); // 保留最近50个事件
    });

    setIsConnected(true);

    return () => {
      unsubscribe();
      setIsConnected(false);
    };
  }, []);

  const clearEvents = React.useCallback(() => {
    setEvents([]);
  }, []);

  return {
    events,
    isConnected,
    clearEvents
  };
}

// 触发实时事件的辅助函数
export function triggerRealtimeEvent(type: RealtimeEvent['type'], data: any) {
  realtimeManager.triggerEvent(type, data);
}

// 实时连接状态指示器组件
export function RealtimeIndicator() {
  const [isConnected, setIsConnected] = React.useState(false);
  const [lastUpdate, setLastUpdate] = React.useState<Date | null>(null);

  React.useEffect(() => {
    const subscriberId = `indicator-${Date.now()}`;
    
    const unsubscribe = realtimeManager.subscribe(subscriberId, (event) => {
      setIsConnected(true);
      setLastUpdate(event.timestamp);
    });

    return unsubscribe;
  }, []);

  return (
    <div className={`flex items-center gap-2 text-sm ${isConnected ? 'text-green-600' : 'text-gray-400'}`}>
      <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500 animate-pulse' : 'bg-gray-400'}`} />
      <span>
        {isConnected ? '实时连接' : '离线'}
        {lastUpdate && (
          <span className="text-xs text-gray-500 ml-1">
            {lastUpdate.toLocaleTimeString()}
          </span>
        )}
      </span>
    </div>
  );
}



// Sitemap 验证工具
import { MetadataRoute } from 'next';

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  stats: {
    totalUrls: number;
    validUrls: number;
    invalidUrls: number;
    duplicateUrls: number;
  };
}

export interface UrlValidationResult {
  url: string;
  isValid: boolean;
  statusCode?: number;
  error?: string;
  responseTime?: number;
}

export class SitemapValidator {
  private readonly MAX_URLS = 50000; // Google sitemap 限制
  private readonly MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB
  private readonly VALID_CHANGEFREQ = ['always', 'hourly', 'daily', 'weekly', 'monthly', 'yearly', 'never'];

  // 验证 sitemap 格式
  validateSitemapFormat(sitemap: MetadataRoute.Sitemap): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    const seenUrls = new Set<string>();
    let duplicateCount = 0;

    // 检查 URL 数量限制
    if (sitemap.length > this.MAX_URLS) {
      errors.push(`Sitemap 包含 ${sitemap.length} 个 URL，超过了 ${this.MAX_URLS} 的限制`);
    }

    if (sitemap.length === 0) {
      errors.push('Sitemap 为空，没有包含任何 URL');
    }

    // 验证每个 URL 条目
    sitemap.forEach((entry, index) => {
      this.validateSitemapEntry(entry, index, errors, warnings, seenUrls);
      
      // 检查重复 URL
      if (seenUrls.has(entry.url)) {
        duplicateCount++;
        warnings.push(`重复的 URL: ${entry.url}`);
      } else {
        seenUrls.add(entry.url);
      }
    });

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      stats: {
        totalUrls: sitemap.length,
        validUrls: sitemap.length - duplicateCount,
        invalidUrls: errors.length,
        duplicateUrls: duplicateCount
      }
    };
  }

  // 验证单个 sitemap 条目
  private validateSitemapEntry(
    entry: MetadataRoute.Sitemap[0], 
    index: number, 
    errors: string[], 
    warnings: string[]
  ): void {
    // 验证 URL 格式
    if (!entry.url) {
      errors.push(`条目 ${index + 1}: URL 不能为空`);
      return;
    }

    try {
      const url = new URL(entry.url);
      
      // 检查协议
      if (!['http:', 'https:'].includes(url.protocol)) {
        errors.push(`条目 ${index + 1}: URL 必须使用 HTTP 或 HTTPS 协议: ${entry.url}`);
      }

      // 检查 URL 长度
      if (entry.url.length > 2048) {
        errors.push(`条目 ${index + 1}: URL 长度超过 2048 字符: ${entry.url}`);
      }

    } catch (error) {
      errors.push(`条目 ${index + 1}: 无效的 URL 格式: ${entry.url}`);
    }

    // 验证 lastModified
    if (entry.lastModified) {
      const lastMod = new Date(entry.lastModified);
      if (isNaN(lastMod.getTime())) {
        errors.push(`条目 ${index + 1}: 无效的 lastModified 日期: ${entry.lastModified}`);
      } else if (lastMod > new Date()) {
        warnings.push(`条目 ${index + 1}: lastModified 日期在未来: ${entry.lastModified}`);
      }
    }

    // 验证 changeFrequency
    if (entry.changeFrequency && !this.VALID_CHANGEFREQ.includes(entry.changeFrequency)) {
      errors.push(`条目 ${index + 1}: 无效的 changeFrequency: ${entry.changeFrequency}`);
    }

    // 验证 priority
    if (entry.priority !== undefined) {
      if (typeof entry.priority !== 'number' || entry.priority < 0 || entry.priority > 1) {
        errors.push(`条目 ${index + 1}: priority 必须是 0.0 到 1.0 之间的数字: ${entry.priority}`);
      }
    }

    // 验证 alternates
    if (entry.alternates?.languages) {
      Object.entries(entry.alternates.languages).forEach(([lang, url]) => {
        try {
          new URL(url);
        } catch {
          errors.push(`条目 ${index + 1}: 无效的 alternate URL for ${lang}: ${url}`);
        }
      });
    }
  }

  // 验证 XML 格式（用于检查生成的 XML）
  validateXmlFormat(xmlContent: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // 基本 XML 格式检查
    if (!xmlContent.includes('<?xml version="1.0"')) {
      errors.push('缺少 XML 声明');
    }

    if (!xmlContent.includes('<urlset')) {
      errors.push('缺少 urlset 根元素');
    }

    if (!xmlContent.includes('xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"')) {
      errors.push('缺少正确的 sitemap 命名空间');
    }

    // 检查是否有未闭合的标签
    const openTags = xmlContent.match(/<[^/][^>]*>/g) || [];
    const closeTags = xmlContent.match(/<\/[^>]*>/g) || [];
    
    if (openTags.length !== closeTags.length) {
      errors.push('XML 标签不匹配，可能存在未闭合的标签');
    }

    // 检查文件大小
    const sizeInBytes = new TextEncoder().encode(xmlContent).length;
    if (sizeInBytes > this.MAX_FILE_SIZE) {
      errors.push(`Sitemap 文件大小 ${(sizeInBytes / 1024 / 1024).toFixed(2)}MB 超过 50MB 限制`);
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // 批量验证 URL 可访问性
  async validateUrlAccessibility(urls: string[], options: {
    timeout?: number;
    maxConcurrent?: number;
    userAgent?: string;
  } = {}): Promise<UrlValidationResult[]> {
    const {
      timeout = 10000,
      maxConcurrent = 10,
      userAgent = 'SitemapValidator/1.0'
    } = options;

    const results: UrlValidationResult[] = [];
    
    // 分批处理 URL
    for (let i = 0; i < urls.length; i += maxConcurrent) {
      const batch = urls.slice(i, i + maxConcurrent);
      const batchPromises = batch.map(url => this.validateSingleUrl(url, timeout, userAgent));
      
      try {
        const batchResults = await Promise.allSettled(batchPromises);
        batchResults.forEach((result, index) => {
          if (result.status === 'fulfilled') {
            results.push(result.value);
          } else {
            results.push({
              url: batch[index],
              isValid: false,
              error: result.reason?.message || 'Unknown error'
            });
          }
        });
      } catch (error) {
        console.error('Batch validation error:', error);
      }

      // 避免过于频繁的请求
      if (i + maxConcurrent < urls.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    return results;
  }

  // 验证单个 URL 可访问性
  private async validateSingleUrl(url: string, timeout: number, userAgent: string): Promise<UrlValidationResult> {
    const startTime = Date.now();
    
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);

      const response = await fetch(url, {
        method: 'HEAD', // 只获取头部信息
        signal: controller.signal,
        headers: {
          'User-Agent': userAgent
        }
      });

      clearTimeout(timeoutId);
      const responseTime = Date.now() - startTime;

      return {
        url,
        isValid: response.ok,
        statusCode: response.status,
        responseTime
      };

    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      return {
        url,
        isValid: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        responseTime
      };
    }
  }
}

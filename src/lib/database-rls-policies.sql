-- 行级安全策略（RLS）配置
-- 这个文件包含了管理系统所需的所有RLS策略

-- 删除现有的策略（如果存在）
DROP POLICY IF EXISTS "Allow public read access" ON products;
DROP POLICY IF EXISTS "Allow public read access" ON articles;
DROP POLICY IF EXISTS "Allow public read access" ON categories;

-- 产品表的RLS策略
-- 允许所有人读取产品
CREATE POLICY "Allow public read products" ON products FOR SELECT USING (true);

-- 允许匿名用户插入产品（用于管理系统）
CREATE POLICY "Allow anon insert products" ON products FOR INSERT WITH CHECK (true);

-- 允许匿名用户更新产品（用于管理系统）
CREATE POLICY "Allow anon update products" ON products FOR UPDATE USING (true) WITH CHECK (true);

-- 允许匿名用户删除产品（用于管理系统）
CREATE POLICY "Allow anon delete products" ON products FOR DELETE USING (true);

-- 文章表的RLS策略
-- 允许所有人读取已发布的文章
CREATE POLICY "Allow public read published articles" ON articles FOR SELECT USING (published = true);

-- 允许匿名用户读取所有文章（用于管理系统）
CREATE POLICY "Allow anon read all articles" ON articles FOR SELECT USING (true);

-- 允许匿名用户插入文章（用于管理系统）
CREATE POLICY "Allow anon insert articles" ON articles FOR INSERT WITH CHECK (true);

-- 允许匿名用户更新文章（用于管理系统）
CREATE POLICY "Allow anon update articles" ON articles FOR UPDATE USING (true) WITH CHECK (true);

-- 允许匿名用户删除文章（用于管理系统）
CREATE POLICY "Allow anon delete articles" ON articles FOR DELETE USING (true);

-- 分类表的RLS策略
-- 允许所有人读取分类
CREATE POLICY "Allow public read categories" ON categories FOR SELECT USING (true);

-- 允许匿名用户插入分类（用于管理系统）
CREATE POLICY "Allow anon insert categories" ON categories FOR INSERT WITH CHECK (true);

-- 允许匿名用户更新分类（用于管理系统）
CREATE POLICY "Allow anon update categories" ON categories FOR UPDATE USING (true) WITH CHECK (true);

-- 允许匿名用户删除分类（用于管理系统）
CREATE POLICY "Allow anon delete categories" ON categories FOR DELETE USING (true);

-- 注意：在生产环境中，您应该考虑更严格的安全策略
-- 例如：
-- 1. 创建专门的管理员角色
-- 2. 只允许认证用户进行写操作
-- 3. 基于用户角色限制访问权限
-- 
-- 当前配置允许匿名用户进行所有操作，这是为了简化开发过程
-- 在部署到生产环境之前，请根据您的安全需求调整这些策略

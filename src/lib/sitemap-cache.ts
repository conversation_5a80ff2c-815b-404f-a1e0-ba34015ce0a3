// Sitemap 缓存管理系统
interface SitemapCacheEntry {
  data: any;
  timestamp: number;
  lastModified: Date;
}

class SitemapCache {
  private cache = new Map<string, SitemapCacheEntry>();
  private readonly CACHE_DURATION = 3600 * 1000; // 1 小时

  // 获取缓存的 sitemap 数据
  get(key: string): any | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return null;
    }

    // 检查缓存是否过期
    const now = Date.now();
    if (now - entry.timestamp > this.CACHE_DURATION) {
      this.cache.delete(key);
      return null;
    }

    return entry.data;
  }

  // 设置缓存数据
  set(key: string, data: any, lastModified?: Date): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      lastModified: lastModified || new Date()
    });
  }

  // 检查缓存是否需要更新（基于内容的最后修改时间）
  shouldUpdate(key: string, contentLastModified: Date): boolean {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return true; // 没有缓存，需要更新
    }

    // 如果内容的最后修改时间比缓存的时间新，需要更新
    return contentLastModified > entry.lastModified;
  }

  // 清除特定缓存
  clear(key: string): void {
    this.cache.delete(key);
  }

  // 清除所有缓存
  clearAll(): void {
    this.cache.clear();
  }

  // 获取缓存统计信息
  getStats() {
    const entries = Array.from(this.cache.entries());
    const now = Date.now();
    
    return {
      totalEntries: entries.length,
      validEntries: entries.filter(([_, entry]) => 
        now - entry.timestamp <= this.CACHE_DURATION
      ).length,
      expiredEntries: entries.filter(([_, entry]) => 
        now - entry.timestamp > this.CACHE_DURATION
      ).length,
      cacheKeys: entries.map(([key]) => key)
    };
  }

  // 清理过期缓存
  cleanup(): void {
    const now = Date.now();
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > this.CACHE_DURATION) {
        this.cache.delete(key);
      }
    }
  }
}

// 全局缓存实例
export const sitemapCache = new SitemapCache();

// 生成缓存键
export function generateCacheKey(type: string, params?: any): string {
  if (!params) {
    return `sitemap-${type}`;
  }
  
  const paramString = Object.keys(params)
    .sort()
    .map(key => `${key}=${params[key]}`)
    .join('&');
    
  return `sitemap-${type}-${paramString}`;
}

// 获取内容的最后修改时间
export function getContentLastModified(products: any[], articles: any[], categories: any[]): Date {
  const dates = [
    ...products.map(p => new Date(p.updatedAt || p.createdAt)),
    ...articles.map(a => new Date(a.updatedAt || a.publishedAt || a.createdAt)),
    ...categories.map(c => new Date(c.createdAt))
  ].filter(date => !isNaN(date.getTime()));

  return dates.length > 0 
    ? new Date(Math.max(...dates.map(d => d.getTime())))
    : new Date();
}

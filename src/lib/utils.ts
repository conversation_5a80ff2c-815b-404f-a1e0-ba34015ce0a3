import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"
import { Article, Product } from "@/types"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * 获取文章封面图片URL
 * 如果文章有封面图片则使用，否则使用关联产品的第一张图片
 * @param article 文章对象
 * @returns 封面图片URL或undefined
 */
export function getArticleCoverImage(article: Article): string | undefined {
  // 如果文章有封面图片，直接返回
  if (article.coverImage) {
    return article.coverImage;
  }
  
  // 如果没有封面图片但有关联产品，使用产品的第一张图片
  if (article.product && article.product.images && article.product.images.length > 0) {
    return article.product.images[0];
  }
  
  // 都没有则返回undefined，让组件使用默认占位图
  return undefined;
}

import { Product, ProductGroupRule, QuizResult } from '@/types';
import { supabase } from './supabase';
import { productService } from './database';

/**
 * 产品分组引擎类 - 处理产品分组和匹配逻辑
 */
export class ProductGroupingEngine {
  private rules: ProductGroupRule[];

  constructor(rules: ProductGroupRule[]) {
    this.rules = rules.sort((a, b) => b.priority - a.priority); // 按优先级排序
  }

  /**
   * 根据测验结果为产品分组
   */
  groupProductsByQuizResult(products: Product[], quizResult: QuizResult): Record<string, Product[]> {
    const groups: Record<string, Product[]> = {};

    // 根据测验结果的维度得分来分组产品
    const { dimensionScores } = quizResult;

    products.forEach(product => {
      const matchingRules = this.findMatchingRules(product, dimensionScores);
      
      matchingRules.forEach(rule => {
        if (!groups[rule.id]) {
          groups[rule.id] = [];
        }
        groups[rule.id].push(product);
      });
    });

    return groups;
  }

  /**
   * 为单个产品找到匹配的规则
   */
  private findMatchingRules(product: Product, dimensionScores: Record<string, number>): ProductGroupRule[] {
    const matchingRules: ProductGroupRule[] = [];

    this.rules.forEach(rule => {
      if (this.doesProductMatchRule(product, rule, dimensionScores)) {
        matchingRules.push(rule);
      }
    });

    return matchingRules;
  }

  /**
   * 检查产品是否匹配特定规则
   */
  private doesProductMatchRule(
    product: Product, 
    rule: ProductGroupRule, 
    dimensionScores: Record<string, number>
  ): boolean {
    const { rules: ruleConfig } = rule;

    // 检查产品分类是否匹配
    if (ruleConfig.categories && ruleConfig.categories.length > 0) {
      if (!ruleConfig.categories.includes(product.category)) {
        return false;
      }
    }

    // 根据维度得分检查经验级别
    if (ruleConfig.experienceLevels && ruleConfig.experienceLevels.length > 0) {
      const experienceLevel = this.getExperienceLevelFromScore(dimensionScores.experience || 0);
      if (!ruleConfig.experienceLevels.includes(experienceLevel)) {
        return false;
      }
    }

    // 根据维度得分检查使用类型
    if (ruleConfig.usageTypes && ruleConfig.usageTypes.length > 0) {
      const usageType = this.getUsageTypeFromScore(dimensionScores.social || 0);
      if (!ruleConfig.usageTypes.includes(usageType)) {
        return false;
      }
    }

    // 根据维度得分检查体验类型
    if (ruleConfig.experienceTypes && ruleConfig.experienceTypes.length > 0) {
      const experienceType = this.getExperienceTypeFromScore(
        dimensionScores.intensity || 0,
        dimensionScores.exploration || 0
      );
      if (!ruleConfig.experienceTypes.includes(experienceType)) {
        return false;
      }
    }

    return true;
  }

  /**
   * 根据经验得分获取经验级别
   */
  private getExperienceLevelFromScore(score: number): string {
    if (score <= 1.5) return 'beginner';
    if (score <= 2.5) return 'intermediate';
    return 'advanced';
  }

  /**
   * 根据社交得分获取使用类型
   */
  private getUsageTypeFromScore(score: number): string {
    if (score <= 1.5) return 'solo';
    if (score <= 2.5) return 'both';
    return 'couple';
  }

  /**
   * 根据强度和探索得分获取体验类型
   */
  private getExperienceTypeFromScore(intensityScore: number, explorationScore: number): string {
    if (intensityScore <= 1.5 && explorationScore <= 1.5) return 'gentle';
    if (intensityScore >= 2.5 && explorationScore <= 2) return 'intense';
    return 'varied';
  }

  /**
   * 获取推荐产品（基于规则优先级）
   */
  getRecommendedProducts(
    allProducts: Product[], 
    quizResult: QuizResult, 
    maxProducts: number = 6
  ): Product[] {
    const groupedProducts = this.groupProductsByQuizResult(allProducts, quizResult);
    const recommendedProducts: Product[] = [];
    const addedProductIds = new Set<string>();

    // 按规则优先级添加产品
    this.rules.forEach(rule => {
      if (groupedProducts[rule.id] && recommendedProducts.length < maxProducts) {
        groupedProducts[rule.id].forEach(product => {
          if (!addedProductIds.has(product.id) && recommendedProducts.length < maxProducts) {
            recommendedProducts.push(product);
            addedProductIds.add(product.id);
          }
        });
      }
    });

    return recommendedProducts;
  }

  /**
   * 为产品计算匹配分数
   */
  calculateProductMatchScore(product: Product, quizResult: QuizResult): number {
    const { dimensionScores } = quizResult;
    let totalScore = 0;
    let ruleCount = 0;

    this.rules.forEach(rule => {
      if (this.doesProductMatchRule(product, rule, dimensionScores)) {
        totalScore += rule.priority;
        ruleCount++;
      }
    });

    return ruleCount > 0 ? totalScore / ruleCount : 0;
  }
}

/**
 * 产品分组服务类 - 处理数据库操作和业务逻辑
 */
export class ProductGroupingService {
  /**
   * 获取所有活跃的产品分组规则
   */
  static async getActiveRules(): Promise<ProductGroupRule[]> {
    try {
      const { data, error } = await supabase
        .from('product_group_rules')
        .select('*')
        .eq('active', true)
        .order('priority', { ascending: false });

      if (error) {
        console.error('Error fetching product group rules:', error);
        return [];
      }

      return data.map(rule => ({
        id: rule.id,
        name: rule.name,
        description: rule.description,
        emoji: rule.emoji,
        rules: rule.rules,
        priority: rule.priority,
        active: rule.active,
        createdAt: new Date(rule.created_at),
        updatedAt: new Date(rule.updated_at)
      }));
    } catch (error) {
      console.error('Error in getActiveRules:', error);
      return [];
    }
  }

  /**
   * 创建新的产品分组规则
   */
  static async createRule(rule: Omit<ProductGroupRule, 'id' | 'createdAt' | 'updatedAt'>): Promise<ProductGroupRule | null> {
    try {
      const { data, error } = await supabase
        .from('product_group_rules')
        .insert({
          name: rule.name,
          description: rule.description,
          emoji: rule.emoji,
          rules: rule.rules,
          priority: rule.priority,
          active: rule.active
        })
        .select('*')
        .single();

      if (error) {
        console.error('Error creating product group rule:', error);
        return null;
      }

      return {
        id: data.id,
        name: data.name,
        description: data.description,
        emoji: data.emoji,
        rules: data.rules,
        priority: data.priority,
        active: data.active,
        createdAt: new Date(data.created_at),
        updatedAt: new Date(data.updated_at)
      };
    } catch (error) {
      console.error('Error in createRule:', error);
      return null;
    }
  }

  /**
   * 更新产品分组规则
   */
  static async updateRule(id: string, updates: Partial<ProductGroupRule>): Promise<boolean> {
    try {
      const updateData: any = {};
      
      if (updates.name !== undefined) updateData.name = updates.name;
      if (updates.description !== undefined) updateData.description = updates.description;
      if (updates.emoji !== undefined) updateData.emoji = updates.emoji;
      if (updates.rules !== undefined) updateData.rules = updates.rules;
      if (updates.priority !== undefined) updateData.priority = updates.priority;
      if (updates.active !== undefined) updateData.active = updates.active;

      const { error } = await supabase
        .from('product_group_rules')
        .update(updateData)
        .eq('id', id);

      if (error) {
        console.error('Error updating product group rule:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error in updateRule:', error);
      return false;
    }
  }

  /**
   * 删除产品分组规则
   */
  static async deleteRule(id: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('product_group_rules')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Error deleting product group rule:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error in deleteRule:', error);
      return false;
    }
  }

  /**
   * 为产品分配到分组规则
   */
  static async assignProductToGroup(productId: string, groupRuleId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('product_group_assignments')
        .insert({
          product_id: productId,
          group_rule_id: groupRuleId
        });

      if (error) {
        console.error('Error assigning product to group:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error in assignProductToGroup:', error);
      return false;
    }
  }

  /**
   * 移除产品的分组分配
   */
  static async removeProductFromGroup(productId: string, groupRuleId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('product_group_assignments')
        .delete()
        .eq('product_id', productId)
        .eq('group_rule_id', groupRuleId);

      if (error) {
        console.error('Error removing product from group:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error in removeProductFromGroup:', error);
      return false;
    }
  }

  /**
   * 获取产品的分组分配
   */
  static async getProductGroupAssignments(productId: string): Promise<ProductGroupRule[]> {
    try {
      const { data, error } = await supabase
        .from('product_group_assignments')
        .select(`
          group_rule_id,
          product_group_rules (*)
        `)
        .eq('product_id', productId);

      if (error) {
        console.error('Error fetching product group assignments:', error);
        return [];
      }

      return data.map(assignment => ({
        id: assignment.product_group_rules.id,
        name: assignment.product_group_rules.name,
        description: assignment.product_group_rules.description,
        emoji: assignment.product_group_rules.emoji,
        rules: assignment.product_group_rules.rules,
        priority: assignment.product_group_rules.priority,
        active: assignment.product_group_rules.active,
        createdAt: new Date(assignment.product_group_rules.created_at),
        updatedAt: new Date(assignment.product_group_rules.updated_at)
      }));
    } catch (error) {
      console.error('Error in getProductGroupAssignments:', error);
      return [];
    }
  }

  /**
   * 根据测验结果获取推荐产品
   */
  static async getRecommendedProductsByQuizResult(
    quizResult: QuizResult, 
    maxProducts: number = 6
  ): Promise<Product[]> {
    try {
      // 获取活跃的分组规则
      const rules = await this.getActiveRules();
      if (rules.length === 0) {
        return [];
      }

      // 获取相关分类的所有产品
      const categories = quizResult.resultGroup.productCategories;
      const allProducts: Product[] = [];
      
      for (const category of categories) {
        const categoryProducts = await productService.getByCategory(category, 20);
        allProducts.push(...categoryProducts);
      }

      // 去重
      const uniqueProducts = allProducts.filter((product, index, self) => 
        index === self.findIndex(p => p.id === product.id)
      );

      // 使用分组引擎获取推荐产品
      const groupingEngine = new ProductGroupingEngine(rules);
      const recommendedProducts = groupingEngine.getRecommendedProducts(
        uniqueProducts, 
        quizResult, 
        maxProducts
      );

      return recommendedProducts;
    } catch (error) {
      console.error('Error in getRecommendedProductsByQuizResult:', error);
      return [];
    }
  }
}

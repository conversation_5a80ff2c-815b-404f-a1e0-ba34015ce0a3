import { Metadata } from 'next';
import { SEOData } from '@/types';

// 网站基础配置
export const siteConfig = {
  name: 'Tu Tienda Íntima',
  description: 'Tu guía confiable para productos íntimos y bienestar personal en México. Información honesta, reseñas detalladas y recomendaciones expertas.',
  url: process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000',
  ogImage: '/images/og-image.jpg',
  keywords: [
    'productos íntimos',
    'adultos',
    'México',
    'juguetes',
    'bienestar',
    'reseñas',
    'guías',
    'Amazon México'
  ]
};

// 生成基础SEO元数据
export function generateSEO(seoData: SEOData): Metadata {
  const {
    title,
    description,
    keywords = [],
    ogImage,
    canonical
  } = seoData;

  const fullTitle = title.includes(siteConfig.name) ? title : `${title} | ${siteConfig.name}`;
  const url = canonical ? `${siteConfig.url}${canonical}` : siteConfig.url;
  const imageUrl = ogImage ? `${siteConfig.url}${ogImage}` : `${siteConfig.url}${siteConfig.ogImage}`;

  return {
    title: fullTitle,
    description,
    keywords: [...keywords, ...siteConfig.keywords].join(', '),
    
    // Open Graph
    openGraph: {
      title: fullTitle,
      description,
      url,
      siteName: siteConfig.name,
      images: [
        {
          url: imageUrl,
          width: 1200,
          height: 630,
          alt: title,
        }
      ],
      locale: 'es_MX',
      type: 'website',
    },

    // Twitter Card
    twitter: {
      card: 'summary_large_image',
      title: fullTitle,
      description,
      images: [imageUrl],
    },

    // 其他元数据
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },

    // 规范URL
    alternates: canonical ? {
      canonical: url
    } : undefined,

    // 语言和地区
    other: {
      'geo.region': 'MX',
      'geo.country': 'Mexico',
      'language': 'es-MX',
    }
  };
}



// 为文章页面生成SEO
export function generateArticleSEO(title: string, excerpt: string, keywords: string[], slug: string): Metadata {
  return generateSEO({
    title,
    description: excerpt,
    keywords,
    canonical: `/articulo/${slug}`
  });
}

// 为分类页面生成SEO
export function generateCategorySEO(categoryName: string, categorySlug: string): Metadata {
  const categoryDescriptions: Record<string, string> = {
    'vibradores': 'Descubre los mejores vibradores disponibles en México. Reseñas detalladas, guías de compra y recomendaciones expertas para encontrar el vibrador perfecto.',
    'balas-vibradoras': 'Guía completa de balas vibradoras: pequeñas, discretas y poderosas. Encuentra la bala vibradora ideal con nuestras reseñas y comparativas.',
    'masturbadores': 'Los mejores masturbadores masculinos del mercado. Reseñas honestas, comparativas y guías para elegir el masturbador perfecto.',
    'pinzas-pezones': 'Todo sobre pinzas para pezones: tipos, materiales, seguridad y recomendaciones. Guía completa para principiantes y expertos.',
    'succionadores': 'Descubre los succionadores más populares. Tecnología de succión, reseñas y guías para una experiencia única.',
    'anillos': 'Guía completa de anillos vibradores y de restricción. Características, materiales y recomendaciones para todos los niveles.'
  };

  const description = categoryDescriptions[categorySlug] || 
    `Explora nuestra selección de ${categoryName.toLowerCase()} con reseñas detalladas y recomendaciones expertas.`;

  return generateSEO({
    title: `${categoryName} - Guías y Reseñas Completas`,
    description,
    keywords: [
      categoryName.toLowerCase(),
      'guía',
      'reseñas',
      'comparativa',
      'mejores',
      'México',
      'productos íntimos'
    ],
    canonical: `/categoria/${categorySlug}`
  });
}

import { generateAmazonMexicoUrl } from './asinUtils';

// Generar datos estructurados JSON-LD
export function generateProductJsonLd(product: any, article?: any) {
  const jsonLd = {
    '@context': 'https://schema.org',
    '@type': 'Product',
    name: product.name,
    category: product.category,
    offers: {
      '@type': 'Offer',
      availability: 'https://schema.org/InStock',
      url: generateAmazonMexicoUrl(product.asin)
    }
  };

  if (article) {
    return {
      '@context': 'https://schema.org',
      '@type': 'Review',
      itemReviewed: jsonLd,
      author: {
        '@type': 'Organization',
        name: siteConfig.name
      },

      name: article.title,
      reviewBody: article.excerpt,
      datePublished: article.publishedAt
    };
  }

  return jsonLd;
}

// Generar sitemap XML
export function generateSitemap(products: any[], articles: any[], categories: any[]) {
  const baseUrl = siteConfig.url;
  const currentDate = new Date().toISOString();

  let sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>${baseUrl}</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>daily</changefreq>
    <priority>1.0</priority>
  </url>`;

  // 添加分类页面
  categories.forEach((category: any) => {
    sitemap += `
  <url>
    <loc>${baseUrl}/categoria/${category.slug}</loc>
    <lastmod>${currentDate}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.8</priority>
  </url>`;
  });

  // 添加文章页面
  articles.forEach((article: any) => {
    sitemap += `
  <url>
    <loc>${baseUrl}/articulo/${article.slug}</loc>
    <lastmod>${article.updatedAt || article.publishedAt}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.7</priority>
  </url>`;
  });

  sitemap += `
</urlset>`;

  return sitemap;
}

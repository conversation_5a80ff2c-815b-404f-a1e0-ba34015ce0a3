-- 创建产品表
CREATE TABLE products (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  category VARCHAR(100) NOT NULL,
  amazon_url TEXT NOT NULL,
  images TEXT[], -- 产品图片URL数组
  features TEXT, -- 产品特性描述
  rating DECIMAL(2,1) DEFAULT 0.0, -- 产品评分 (0.0-5.0)
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建文章表
CREATE TABLE articles (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  content TEXT NOT NULL,
  excerpt TEXT NOT NULL,
  slug VARCHAR(255) UNIQUE NOT NULL,
  product_id UUID REFERENCES products(id) ON DELETE CASCADE,
  cover_image TEXT, -- 文章封面图片URL
  video_url TEXT, -- 产品展示视频URL
  meta_title VARCHAR(255) NOT NULL,
  meta_description TEXT NOT NULL,
  keywords TEXT[], -- SEO关键词数组
  published BOOLEAN DEFAULT FALSE,
  published_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建分类表（可选，用于更好的分类管理）
CREATE TABLE categories (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(100) NOT NULL UNIQUE,
  slug VARCHAR(100) NOT NULL UNIQUE,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 插入基础分类数据
INSERT INTO categories (name, slug, description) VALUES
('Vibradores', 'vibradores', 'Vibradores de alta calidad para el placer femenino'),
('Balas Vibradoras', 'balas-vibradoras', 'Pequeños vibradores discretos y potentes'),
('Pinzas para Pezones', 'pinzas-pezones', 'Accesorios para estimulación de pezones'),
('Succionadores', 'succionadores', 'Juguetes con tecnología de succión'),
('Anillos', 'anillos', 'Anillos vibradores y de restricción'),
('Masturbadores', 'masturbadores', 'Juguetes masculinos para el placer personal');

-- 创建索引以提高查询性能
CREATE INDEX idx_products_category ON products(category);
CREATE INDEX idx_articles_slug ON articles(slug);
CREATE INDEX idx_articles_published ON articles(published);
CREATE INDEX idx_articles_product_id ON articles(product_id);

-- 创建更新时间戳的触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为产品表创建更新触发器
CREATE TRIGGER update_products_updated_at 
    BEFORE UPDATE ON products 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- 为文章表创建更新触发器
CREATE TRIGGER update_articles_updated_at 
    BEFORE UPDATE ON articles 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- 启用行级安全策略（RLS）
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE articles ENABLE ROW LEVEL SECURITY;
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;

-- 产品表的RLS策略
-- 允许所有人读取产品
CREATE POLICY "Allow public read products" ON products FOR SELECT USING (true);
-- 允许匿名用户插入产品（用于管理系统）
CREATE POLICY "Allow anon insert products" ON products FOR INSERT WITH CHECK (true);
-- 允许匿名用户更新产品（用于管理系统）
CREATE POLICY "Allow anon update products" ON products FOR UPDATE USING (true) WITH CHECK (true);
-- 允许匿名用户删除产品（用于管理系统）
CREATE POLICY "Allow anon delete products" ON products FOR DELETE USING (true);

-- 文章表的RLS策略
-- 允许所有人读取已发布的文章
CREATE POLICY "Allow public read published articles" ON articles FOR SELECT USING (published = true);
-- 允许匿名用户读取所有文章（用于管理系统）
CREATE POLICY "Allow anon read all articles" ON articles FOR SELECT USING (true);
-- 允许匿名用户插入文章（用于管理系统）
CREATE POLICY "Allow anon insert articles" ON articles FOR INSERT WITH CHECK (true);
-- 允许匿名用户更新文章（用于管理系统）
CREATE POLICY "Allow anon update articles" ON articles FOR UPDATE USING (true) WITH CHECK (true);
-- 允许匿名用户删除文章（用于管理系统）
CREATE POLICY "Allow anon delete articles" ON articles FOR DELETE USING (true);

-- 分类表的RLS策略
-- 允许所有人读取分类
CREATE POLICY "Allow public read categories" ON categories FOR SELECT USING (true);
-- 允许匿名用户插入分类（用于管理系统）
CREATE POLICY "Allow anon insert categories" ON categories FOR INSERT WITH CHECK (true);
-- 允许匿名用户更新分类（用于管理系统）
CREATE POLICY "Allow anon update categories" ON categories FOR UPDATE USING (true) WITH CHECK (true);
-- 允许匿名用户删除分类（用于管理系统）
CREATE POLICY "Allow anon delete categories" ON categories FOR DELETE USING (true);

-- 注意：在生产环境中，您应该考虑更严格的安全策略
-- 例如：创建专门的管理员角色，只允许认证用户进行写操作等

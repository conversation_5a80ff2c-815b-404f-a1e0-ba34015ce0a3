import { supabase } from './supabase';

// 分析数据类型定义
export interface PageView {
  id?: string;
  pagePath: string;
  pageTitle?: string;
  referrer?: string;
  userAgent?: string;
  ipAddress?: string;
  sessionId: string;
  userId?: string;
  country?: string;
  city?: string;
  deviceType?: 'desktop' | 'mobile' | 'tablet';
  browser?: string;
  os?: string;
  createdAt?: Date;
}

export interface AmazonClick {
  id?: string;
  productId?: string;
  productName?: string;
  amazonUrl: string;
  pagePath?: string;
  referrer?: string;
  userAgent?: string;
  ipAddress?: string;
  sessionId: string;
  userId?: string;
  country?: string;
  city?: string;
  deviceType?: 'desktop' | 'mobile' | 'tablet';
  browser?: string;
  os?: string;
  createdAt?: Date;
}

export interface DailyStats {
  date: string;
  totalPageViews: number;
  uniqueVisitors: number;
  totalAmazonClicks: number;
  bounceRate?: number;
  avgSessionDuration?: number;
  topPages?: any;
  topProducts?: any;
  trafficSources?: any;
  deviceBreakdown?: any;
}

export interface UserSession {
  sessionId: string;
  firstPage?: string;
  lastPage?: string;
  pageCount: number;
  duration?: number;
  referrer?: string;
  userAgent?: string;
  ipAddress?: string;
  country?: string;
  city?: string;
  deviceType?: 'desktop' | 'mobile' | 'tablet';
  browser?: string;
  os?: string;
  startedAt?: Date;
  endedAt?: Date;
}

export interface PageEngagement {
  id?: string;
  sessionId: string;
  pagePath: string;
  pageTitle?: string;
  timeOnPage: number; // 页面停留时间（秒）
  scrollDepth: number; // 滚动深度（百分比）
  clickCount: number; // 页面内点击次数
  exitPage: boolean; // 是否为退出页面
  bounced: boolean; // 是否为跳出
  createdAt?: Date;
}

// 分析数据服务
export const analyticsService = {
  // 记录页面访问
  async trackPageView(pageView: Omit<PageView, 'id' | 'createdAt'>): Promise<void> {
    try {
      const { error } = await supabase
        .from('page_views')
        .insert({
          page_path: pageView.pagePath,
          page_title: pageView.pageTitle,
          referrer: pageView.referrer,
          user_agent: pageView.userAgent,
          ip_address: pageView.ipAddress,
          session_id: pageView.sessionId,
          user_id: pageView.userId,
          country: pageView.country,
          city: pageView.city,
          device_type: pageView.deviceType,
          browser: pageView.browser,
          os: pageView.os
        });

      if (error) throw error;

      // 更新或创建会话记录
      await this.updateSession(pageView.sessionId, pageView.pagePath, pageView);

      // 触发实时事件
      if (typeof window !== 'undefined') {
        const { triggerRealtimeEvent } = await import('./realtime');
        triggerRealtimeEvent('page_view', {
          pagePath: pageView.pagePath,
          pageTitle: pageView.pageTitle,
          sessionId: pageView.sessionId
        });
      }
    } catch (error) {
      console.error('Error tracking page view:', error);
      // 不抛出错误，避免影响用户体验
    }
  },

  // 记录Amazon链接点击
  async trackAmazonClick(click: Omit<AmazonClick, 'id' | 'createdAt'>): Promise<void> {
    try {
      const { error } = await supabase
        .from('amazon_clicks')
        .insert({
          product_id: click.productId || null, // 允许为null
          product_name: click.productName,
          amazon_url: click.amazonUrl,
          page_path: click.pagePath,
          referrer: click.referrer,
          user_agent: click.userAgent,
          ip_address: click.ipAddress,
          session_id: click.sessionId,
          user_id: click.userId,
          country: click.country,
          city: click.city,
          device_type: click.deviceType,
          browser: click.browser,
          os: click.os
        });

      if (error) {
        console.error('Error tracking Amazon click:', error);
        throw error; // 暂时抛出错误以便调试
      }

      // 触发实时事件
      if (typeof window !== 'undefined') {
        const { triggerRealtimeEvent } = await import('./realtime');
        triggerRealtimeEvent('amazon_click', {
          productId: click.productId,
          productName: click.productName,
          amazonUrl: click.amazonUrl,
          pagePath: click.pagePath
        });
      }
    } catch (error) {
      console.error('Error tracking Amazon click:', error);
      // 不抛出错误，避免影响用户体验
    }
  },

  // 更新会话信息
  async updateSession(sessionId: string, currentPage: string, pageView: Partial<PageView>): Promise<void> {
    try {
      // 首先尝试获取现有会话
      const { data: existingSession } = await supabase
        .from('user_sessions')
        .select('*')
        .eq('session_id', sessionId)
        .single();

      if (existingSession) {
        // 更新现有会话
        const { error } = await supabase
          .from('user_sessions')
          .update({
            last_page: currentPage,
            page_count: existingSession.page_count + 1,
            ended_at: new Date().toISOString()
          })
          .eq('session_id', sessionId);

        if (error) throw error;
      } else {
        // 创建新会话
        const { error } = await supabase
          .from('user_sessions')
          .insert({
            session_id: sessionId,
            first_page: currentPage,
            last_page: currentPage,
            page_count: 1,
            referrer: pageView.referrer,
            user_agent: pageView.userAgent,
            ip_address: pageView.ipAddress,
            country: pageView.country,
            city: pageView.city,
            device_type: pageView.deviceType,
            browser: pageView.browser,
            os: pageView.os
          });

        if (error) throw error;
      }
    } catch (error) {
      console.error('Error updating session:', error);
    }
  },

  // 获取页面访问统计
  async getPageViewStats(dateRange?: { start: Date; end: Date }) {
    try {
      let query = supabase
        .from('page_views')
        .select('*', { count: 'exact' });

      if (dateRange) {
        query = query
          .gte('created_at', dateRange.start.toISOString())
          .lte('created_at', dateRange.end.toISOString());
      }

      const { count, error } = await query;
      if (error) throw error;

      return count || 0;
    } catch (error) {
      console.error('Error getting page view stats:', error);
      return 0;
    }
  },

  // 获取Amazon点击统计
  async getAmazonClickStats(dateRange?: { start: Date; end: Date }) {
    try {
      let query = supabase
        .from('amazon_clicks')
        .select('*', { count: 'exact' });

      if (dateRange) {
        query = query
          .gte('created_at', dateRange.start.toISOString())
          .lte('created_at', dateRange.end.toISOString());
      }

      const { count, error } = await query;
      if (error) throw error;

      return count || 0;
    } catch (error) {
      console.error('Error getting Amazon click stats:', error);
      return 0;
    }
  },

  // 获取独立访客数
  async getUniqueVisitors(dateRange?: { start: Date; end: Date }) {
    try {
      let query = supabase
        .from('user_sessions')
        .select('session_id', { count: 'exact' });

      if (dateRange) {
        query = query
          .gte('started_at', dateRange.start.toISOString())
          .lte('started_at', dateRange.end.toISOString());
      }

      const { count, error } = await query;
      if (error) throw error;

      return count || 0;
    } catch (error) {
      console.error('Error getting unique visitors:', error);
      return 0;
    }
  },

  // 获取热门页面
  async getTopPages(dateRange?: { start: Date; end: Date }, limit: number = 10) {
    try {
      let query = supabase
        .from('page_views')
        .select('page_path, page_title');

      if (dateRange) {
        query = query
          .gte('created_at', dateRange.start.toISOString())
          .lte('created_at', dateRange.end.toISOString());
      }

      const { data, error } = await query;
      if (error) throw error;

      // 统计页面访问次数
      const pageStats: Record<string, { path: string; title?: string; count: number }> = {};
      (data || []).forEach(view => {
        const key = view.page_path;
        if (pageStats[key]) {
          pageStats[key].count++;
        } else {
          pageStats[key] = {
            path: view.page_path,
            title: view.page_title,
            count: 1
          };
        }
      });

      // 转换为数组并排序
      return Object.values(pageStats)
        .sort((a, b) => b.count - a.count)
        .slice(0, limit);
    } catch (error) {
      console.error('Error getting top pages:', error);
      return [];
    }
  },

  // 获取设备类型分布
  async getDeviceBreakdown(dateRange?: { start: Date; end: Date }) {
    try {
      let query = supabase
        .from('page_views')
        .select('device_type');

      if (dateRange) {
        query = query
          .gte('created_at', dateRange.start.toISOString())
          .lte('created_at', dateRange.end.toISOString());
      }

      const { data, error } = await query;
      if (error) throw error;

      // 统计设备类型分布
      const deviceStats: Record<string, number> = {};
      (data || []).forEach(view => {
        const device = view.device_type || 'unknown';
        deviceStats[device] = (deviceStats[device] || 0) + 1;
      });

      return Object.entries(deviceStats).map(([device, count]) => ({
        device,
        count,
        percentage: data ? ((count / data.length) * 100).toFixed(1) : '0'
      }));
    } catch (error) {
      console.error('Error getting device breakdown:', error);
      return [];
    }
  },

  // 记录页面参与度数据
  async trackPageEngagement(engagement: Omit<PageEngagement, 'id' | 'createdAt'>): Promise<void> {
    try {
      const { error } = await supabase
        .from('page_engagement')
        .insert({
          session_id: engagement.sessionId,
          page_path: engagement.pagePath,
          page_title: engagement.pageTitle,
          time_on_page: engagement.timeOnPage,
          scroll_depth: engagement.scrollDepth,
          click_count: engagement.clickCount,
          exit_page: engagement.exitPage,
          bounced: engagement.bounced
        });

      if (error) throw error;
    } catch (error) {
      console.error('Error tracking page engagement:', error);
    }
  },

  // 获取页面参与度统计
  async getEngagementStats(dateRange?: { start: Date; end: Date }) {
    try {
      let query = supabase
        .from('page_engagement')
        .select('*');

      if (dateRange) {
        query = query
          .gte('created_at', dateRange.start.toISOString())
          .lte('created_at', dateRange.end.toISOString());
      }

      const { data, error } = await query;
      if (error) throw error;

      if (!data || data.length === 0) {
        return {
          avgTimeOnPage: 0,
          avgScrollDepth: 0,
          bounceRate: 0,
          totalEngagements: 0
        };
      }

      const totalEngagements = data.length;
      const avgTimeOnPage = data.reduce((sum, item) => sum + (item.time_on_page || 0), 0) / totalEngagements;
      const avgScrollDepth = data.reduce((sum, item) => sum + (item.scroll_depth || 0), 0) / totalEngagements;
      const bounces = data.filter(item => item.bounced).length;
      const bounceRate = (bounces / totalEngagements) * 100;

      return {
        avgTimeOnPage: Math.round(avgTimeOnPage),
        avgScrollDepth: Math.round(avgScrollDepth),
        bounceRate: Math.round(bounceRate * 100) / 100,
        totalEngagements
      };
    } catch (error) {
      console.error('Error getting engagement stats:', error);
      return {
        avgTimeOnPage: 0,
        avgScrollDepth: 0,
        bounceRate: 0,
        totalEngagements: 0
      };
    }
  },

  // 获取热门页面（按参与度排序）
  async getTopEngagingPages(dateRange?: { start: Date; end: Date }, limit: number = 10) {
    try {
      let query = supabase
        .from('page_engagement')
        .select('page_path, page_title, time_on_page, scroll_depth');

      if (dateRange) {
        query = query
          .gte('created_at', dateRange.start.toISOString())
          .lte('created_at', dateRange.end.toISOString());
      }

      const { data, error } = await query;
      if (error) throw error;

      // 按页面分组并计算平均参与度
      const pageStats: Record<string, {
        path: string;
        title?: string;
        avgTimeOnPage: number;
        avgScrollDepth: number;
        visits: number;
        engagementScore: number;
      }> = {};

      (data || []).forEach(item => {
        const key = item.page_path;
        if (pageStats[key]) {
          pageStats[key].avgTimeOnPage += item.time_on_page || 0;
          pageStats[key].avgScrollDepth += item.scroll_depth || 0;
          pageStats[key].visits++;
        } else {
          pageStats[key] = {
            path: item.page_path,
            title: item.page_title,
            avgTimeOnPage: item.time_on_page || 0,
            avgScrollDepth: item.scroll_depth || 0,
            visits: 1,
            engagementScore: 0
          };
        }
      });

      // 计算平均值和参与度分数
      Object.values(pageStats).forEach(stat => {
        stat.avgTimeOnPage = stat.avgTimeOnPage / stat.visits;
        stat.avgScrollDepth = stat.avgScrollDepth / stat.visits;
        // 参与度分数 = (停留时间权重 * 0.6) + (滚动深度权重 * 0.4)
        stat.engagementScore = (stat.avgTimeOnPage / 60 * 0.6) + (stat.avgScrollDepth / 100 * 0.4);
      });

      return Object.values(pageStats)
        .sort((a, b) => b.engagementScore - a.engagementScore)
        .slice(0, limit);
    } catch (error) {
      console.error('Error getting top engaging pages:', error);
      return [];
    }
  }
};

// 浏览器检测工具函数
export const detectBrowser = (userAgent: string): string => {
  if (userAgent.includes('Chrome')) return 'Chrome';
  if (userAgent.includes('Firefox')) return 'Firefox';
  if (userAgent.includes('Safari')) return 'Safari';
  if (userAgent.includes('Edge')) return 'Edge';
  if (userAgent.includes('Opera')) return 'Opera';
  return 'Unknown';
};

// 操作系统检测工具函数
export const detectOS = (userAgent: string): string => {
  if (userAgent.includes('Windows')) return 'Windows';
  if (userAgent.includes('Mac')) return 'macOS';
  if (userAgent.includes('Linux')) return 'Linux';
  if (userAgent.includes('Android')) return 'Android';
  if (userAgent.includes('iOS')) return 'iOS';
  return 'Unknown';
};

// 设备类型检测工具函数
export const detectDeviceType = (userAgent: string): 'desktop' | 'mobile' | 'tablet' => {
  if (/tablet|ipad/i.test(userAgent)) return 'tablet';
  if (/mobile|android|iphone/i.test(userAgent)) return 'mobile';
  return 'desktop';
};

// 生成会话ID
export const generateSessionId = (): string => {
  return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

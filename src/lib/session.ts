'use client';

// 会话管理器 - 管理用户会话ID和相关数据
export class SessionManager {
  private static instance: SessionManager;
  private sessionId: string;
  private sessionData: Record<string, any> = {};

  private constructor() {
    this.sessionId = this.generateSessionId();
    this.loadSessionData();
  }

  static getInstance(): SessionManager {
    if (!SessionManager.instance) {
      SessionManager.instance = new SessionManager();
    }
    return SessionManager.instance;
  }

  // 生成唯一的会话ID
  private generateSessionId(): string {
    const timestamp = Date.now().toString(36);
    const randomStr = Math.random().toString(36).substring(2, 15);
    return `session_${timestamp}_${randomStr}`;
  }

  // 从localStorage加载会话数据
  private loadSessionData(): void {
    if (typeof window === 'undefined') return;

    try {
      const stored = localStorage.getItem('session_data');
      if (stored) {
        const data = JSON.parse(stored);
        this.sessionId = data.sessionId || this.sessionId;
        this.sessionData = data.sessionData || {};
      }
    } catch (error) {
      console.error('Failed to load session data:', error);
    }
  }

  // 保存会话数据到localStorage
  private saveSessionData(): void {
    if (typeof window === 'undefined') return;

    try {
      const data = {
        sessionId: this.sessionId,
        sessionData: this.sessionData,
        lastUpdated: new Date().toISOString()
      };
      localStorage.setItem('session_data', JSON.stringify(data));
    } catch (error) {
      console.error('Failed to save session data:', error);
    }
  }

  // 获取会话ID
  getSessionId(): string {
    return this.sessionId;
  }

  // 设置会话数据
  setSessionData(key: string, value: any): void {
    this.sessionData[key] = value;
    this.saveSessionData();
  }

  // 获取会话数据
  getSessionData(key: string): any {
    return this.sessionData[key];
  }

  // 清除会话数据
  clearSessionData(): void {
    this.sessionData = {};
    this.sessionId = this.generateSessionId();
    this.saveSessionData();
  }

  // 检查会话是否过期（24小时）
  isSessionExpired(): boolean {
    const lastUpdated = this.getSessionData('lastUpdated');
    if (!lastUpdated) return false;

    const lastUpdateTime = new Date(lastUpdated).getTime();
    const now = Date.now();
    const twentyFourHours = 24 * 60 * 60 * 1000;

    return (now - lastUpdateTime) > twentyFourHours;
  }

  // 刷新会话（重置过期时间）
  refreshSession(): void {
    this.setSessionData('lastUpdated', new Date().toISOString());
  }

  // 获取会话统计信息
  getSessionStats(): {
    sessionId: string;
    startTime: string;
    lastActivity: string;
    pageViews: number;
    duration: number;
  } {
    const startTime = this.getSessionData('startTime') || new Date().toISOString();
    const lastActivity = this.getSessionData('lastActivity') || new Date().toISOString();
    const pageViews = this.getSessionData('pageViews') || 0;
    
    const start = new Date(startTime).getTime();
    const last = new Date(lastActivity).getTime();
    const duration = Math.round((last - start) / 1000); // 秒

    return {
      sessionId: this.sessionId,
      startTime,
      lastActivity,
      pageViews,
      duration
    };
  }

  // 记录页面访问
  recordPageView(pagePath: string): void {
    const currentPageViews = this.getSessionData('pageViews') || 0;
    const visitedPages = this.getSessionData('visitedPages') || [];
    
    this.setSessionData('pageViews', currentPageViews + 1);
    this.setSessionData('lastActivity', new Date().toISOString());
    this.setSessionData('currentPage', pagePath);
    
    // 记录访问过的页面
    if (!visitedPages.includes(pagePath)) {
      visitedPages.push(pagePath);
      this.setSessionData('visitedPages', visitedPages);
    }

    // 如果是第一次访问，记录开始时间
    if (!this.getSessionData('startTime')) {
      this.setSessionData('startTime', new Date().toISOString());
    }
  }

  // 获取访问过的页面列表
  getVisitedPages(): string[] {
    return this.getSessionData('visitedPages') || [];
  }

  // 检查是否为新访客
  isNewVisitor(): boolean {
    return !this.getSessionData('startTime');
  }

  // 获取会话持续时间（秒）
  getSessionDuration(): number {
    const startTime = this.getSessionData('startTime');
    if (!startTime) return 0;

    const start = new Date(startTime).getTime();
    const now = Date.now();
    return Math.round((now - start) / 1000);
  }
}

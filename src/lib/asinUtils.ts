/**
 * ASIN (Amazon Standard Identification Number) 工具函数
 * 用于处理Amazon产品标识符和URL生成
 */

/**
 * 验证ASIN格式是否正确
 * ASIN格式：10个字符，包含大写字母和数字
 * @param asin - 要验证的ASIN
 * @returns 是否为有效的ASIN格式
 */
export function isValidAsin(asin: string): boolean {
  if (!asin || typeof asin !== 'string') {
    return false;
  }
  
  // ASIN必须是10个字符，只包含大写字母和数字
  const asinRegex = /^[A-Z0-9]{10}$/;
  return asinRegex.test(asin);
}

/**
 * 从ASIN生成Amazon Mexico产品URL
 * @param asin - Amazon Standard Identification Number
 * @returns Amazon Mexico产品页面URL
 */
export function generateAmazonMexicoUrl(asin: string): string {
  if (!isValidAsin(asin)) {
    throw new Error(`Invalid ASIN format: ${asin}. ASIN must be 10 characters containing only uppercase letters and numbers.`);
  }
  
  return `https://www.amazon.com.mx/dp/${asin}`;
}

/**
 * 从Amazon URL中提取ASIN
 * 支持多种Amazon URL格式
 * @param url - Amazon产品URL
 * @returns 提取的ASIN，如果无法提取则返回null
 */
export function extractAsinFromUrl(url: string): string | null {
  if (!url || typeof url !== 'string') {
    return null;
  }
  
  try {
    // 支持的URL格式：
    // https://amazon.com.mx/dp/B08XXXXXX
    // https://amazon.com.mx/product-name/dp/B08XXXXXX
    // https://amazon.com.mx/gp/product/B08XXXXXX
    // https://www.amazon.com.mx/dp/B08XXXXXX
    
    // 提取 /dp/ 后面的ASIN
    const dpMatch = url.match(/\/dp\/([A-Z0-9]{10})/);
    if (dpMatch) {
      return dpMatch[1];
    }
    
    // 提取 /gp/product/ 后面的ASIN
    const gpMatch = url.match(/\/gp\/product\/([A-Z0-9]{10})/);
    if (gpMatch) {
      return gpMatch[1];
    }
    
    // 提取 /product/ 后面的ASIN
    const productMatch = url.match(/\/product\/([A-Z0-9]{10})/);
    if (productMatch) {
      return productMatch[1];
    }
    
    return null;
  } catch (error) {
    console.error('Error extracting ASIN from URL:', error);
    return null;
  }
}

/**
 * 验证Amazon URL是否有效并包含ASIN
 * @param url - 要验证的Amazon URL
 * @returns 验证结果对象
 */
export function validateAmazonUrl(url: string): {
  isValid: boolean;
  asin: string | null;
  error?: string;
} {
  if (!url || typeof url !== 'string') {
    return {
      isValid: false,
      asin: null,
      error: 'URL不能为空'
    };
  }
  
  try {
    const urlObj = new URL(url);
    
    // 检查是否为Amazon域名
    if (!urlObj.hostname.includes('amazon.com')) {
      return {
        isValid: false,
        asin: null,
        error: '必须是Amazon网站的URL'
      };
    }
    
    // 提取ASIN
    const asin = extractAsinFromUrl(url);
    if (!asin) {
      return {
        isValid: false,
        asin: null,
        error: '无法从URL中提取有效的ASIN'
      };
    }
    
    return {
      isValid: true,
      asin: asin
    };
  } catch (error) {
    return {
      isValid: false,
      asin: null,
      error: 'URL格式无效'
    };
  }
}

/**
 * 生成示例ASIN（用于测试）
 * @param prefix - ASIN前缀，默认为'B08'
 * @returns 生成的示例ASIN
 */
export function generateExampleAsin(prefix: string = 'B08'): string {
  const timestamp = Date.now().toString();
  const randomSuffix = Math.random().toString(36).substring(2, 8).toUpperCase();
  const suffix = (timestamp + randomSuffix).substring(0, 10 - prefix.length);
  
  return (prefix + suffix).substring(0, 10).padEnd(10, '0');
}

/**
 * 格式化ASIN显示
 * @param asin - 要格式化的ASIN
 * @returns 格式化后的ASIN字符串
 */
export function formatAsinDisplay(asin: string): string {
  if (!isValidAsin(asin)) {
    return 'Invalid ASIN';
  }
  
  // 将ASIN格式化为更易读的形式：B08-XXXXXXX
  return `${asin.substring(0, 3)}-${asin.substring(3)}`;
}

/**
 * 批量验证ASIN列表
 * @param asins - ASIN数组
 * @returns 验证结果数组
 */
export function validateAsinBatch(asins: string[]): Array<{
  asin: string;
  isValid: boolean;
  error?: string;
}> {
  return asins.map(asin => ({
    asin,
    isValid: isValidAsin(asin),
    error: isValidAsin(asin) ? undefined : 'Invalid ASIN format'
  }));
}

/**
 * 从产品对象生成Amazon URL
 * 这是一个便利函数，用于在组件中快速生成URL
 * @param product - 包含asin字段的产品对象
 * @returns Amazon Mexico URL
 */
export function getAmazonUrlFromProduct(product: { asin: string }): string {
  return generateAmazonMexicoUrl(product.asin);
}

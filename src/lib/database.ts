import { supabase } from './supabase';
import { Product, Article, Category } from '@/types';
import { PaginatedResult, PaginationParams, createPaginationInfo } from '@/components/Pagination';
import { analyticsService } from './analytics';
import { cleanupOldProductImages } from './imageUtils';

// 产品相关操作
export const productService = {
  // 获取所有产品
  async getAll(): Promise<Product[]> {
    const { data, error } = await supabase
      .from('products')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) throw error;

    // 映射数据库字段到TypeScript类型
    return (data || []).map(item => ({
      ...item,
      asin: item.asin,
      images: item.images || [],
      features: item.features,
      rating: item.rating || 0.0,
      createdAt: new Date(item.created_at),
      updatedAt: new Date(item.updated_at)
    }));
  },

  // 分页获取产品
  async getPaginated(params: PaginationParams, searchTerm?: string, categoryFilter?: string): Promise<PaginatedResult<Product>> {
    let query = supabase
      .from('products')
      .select('*', { count: 'exact' })
      .order('created_at', { ascending: false });

    // 添加搜索条件
    if (searchTerm && searchTerm.trim()) {
      query = query.or(`name.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%`);
    }

    // 添加分类过滤
    if (categoryFilter && categoryFilter !== 'all') {
      query = query.eq('category', categoryFilter);
    }

    // 添加分页
    query = query.range(params.offset, params.offset + params.limit - 1);

    const { data, error, count } = await query;

    if (error) throw error;

    // 映射数据库字段到TypeScript类型
    const products = (data || []).map(item => ({
      ...item,
      asin: item.asin,
      images: item.images || [],
      features: item.features,
      rating: item.rating || 0.0,
      createdAt: new Date(item.created_at),
      updatedAt: new Date(item.updated_at)
    }));

    return {
      data: products,
      pagination: createPaginationInfo(params.page, params.limit, count || 0)
    };
  },

  // 获取产品总数
  async getCount(searchTerm?: string, categoryFilter?: string): Promise<number> {
    let query = supabase
      .from('products')
      .select('*', { count: 'exact', head: true });

    // 添加搜索条件
    if (searchTerm && searchTerm.trim()) {
      query = query.or(`name.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%`);
    }

    // 添加分类过滤
    if (categoryFilter && categoryFilter !== 'all') {
      query = query.eq('category', categoryFilter);
    }

    const { count, error } = await query;

    if (error) throw error;

    return count || 0;
  },

  // 根据分类获取产品
  async getByCategory(category: string): Promise<Product[]> {
    const { data, error } = await supabase
      .from('products')
      .select('*')
      .eq('category', category)
      .order('created_at', { ascending: false });

    if (error) throw error;

    // 映射数据库字段到TypeScript类型
    return (data || []).map(item => ({
      ...item,
      asin: item.asin,
      images: item.images || [],
      features: item.features,
      rating: item.rating || 0.0,
      createdAt: new Date(item.created_at),
      updatedAt: new Date(item.updated_at)
    }));
  },

  // 根据ID获取单个产品
  async getById(id: string): Promise<Product | null> {
    const { data, error } = await supabase
      .from('products')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null; // 未找到记录
      throw error;
    }

    if (!data) return null;

    // 映射数据库字段到TypeScript类型
    return {
      ...data,
      asin: data.asin,
      images: data.images || [],
      features: data.features,
      rating: data.rating || 0.0,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at)
    };
  },

  // 创建产品
  async create(product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>): Promise<Product> {
    const { data, error } = await supabase
      .from('products')
      .insert([{
        name: product.name,
        category: product.category,
        asin: product.asin,
        images: product.images || [],
        features: product.features,
        rating: product.rating || 0.0
      }])
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // 更新产品
  async update(id: string, updates: Partial<Product>): Promise<Product> {
    // 如果更新图片，先获取旧图片用于清理
    let oldImages: string[] = [];
    if (updates.images !== undefined) {
      try {
        const { data: oldProduct } = await supabase
          .from('products')
          .select('images')
          .eq('id', id)
          .single();

        if (oldProduct) {
          oldImages = oldProduct.images || [];
        }
      } catch (error) {
        console.warn('Failed to fetch old images for cleanup:', error);
      }
    }

    const updateData: any = {};
    if (updates.name) updateData.name = updates.name;
    if (updates.category) updateData.category = updates.category;
    if (updates.asin) updateData.asin = updates.asin;
    if (updates.images !== undefined) updateData.images = updates.images;
    if (updates.features !== undefined) updateData.features = updates.features;
    if (updates.rating !== undefined) updateData.rating = updates.rating;

    const { data, error } = await supabase
      .from('products')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;

    // 清理旧图片（异步执行，不阻塞响应）
    if (updates.images !== undefined && oldImages.length > 0) {
      cleanupOldProductImages(oldImages, updates.images).catch(error => {
        console.error('Failed to cleanup old product images:', error);
      });
    }

    return data;
  },

  // 删除产品
  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('products')
      .delete()
      .eq('id', id);
    
    if (error) throw error;
  },

  // 按分类获取产品
  async getByCategory(category: string, limit: number = 10): Promise<Product[]> {
    try {
      const { data, error } = await supabase
        .from('products')
        .select('*')
        .eq('category', category)
        .limit(limit);

      if (error) {
        console.error('Error fetching products by category:', error);
        return [];
      }

      // 映射数据库字段到TypeScript类型
      return (data || []).map(item => ({
        ...item,
        asin: item.asin,
        images: item.images || [],
        features: item.features,
        rating: item.rating || 0.0,
        createdAt: new Date(item.created_at),
        updatedAt: new Date(item.updated_at)
      }));
    } catch (error) {
      console.error('Error in getByCategory:', error);
      return [];
    }
  }
};

// 文章相关操作
export const articleService = {
  // 获取所有文章（包括未发布的）
  async getAll(): Promise<Article[]> {
    const { data, error } = await supabase
      .from('articles')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) throw error;

    // 手动获取关联的产品信息并映射字段
    const articles = data || [];
    const mappedArticles = [];

    for (const item of articles) {
      const mappedArticle = {
        ...item,
        productId: item.product_id,
        coverImage: item.cover_image,
        metaTitle: item.meta_title,
        metaDescription: item.meta_description,
        publishedAt: item.published_at ? new Date(item.published_at) : undefined,
        createdAt: new Date(item.created_at),
        updatedAt: new Date(item.updated_at)
      };

      if (item.product_id) {
        const product = await productService.getById(item.product_id);
        mappedArticle.product = product;
      }

      mappedArticles.push(mappedArticle);
    }

    return mappedArticles;
  },

  // 分页获取文章（包括未发布的）
  async getPaginated(params: PaginationParams, searchTerm?: string, publishedOnly?: boolean): Promise<PaginatedResult<Article>> {
    let query = supabase
      .from('articles')
      .select('*', { count: 'exact' })
      .order('created_at', { ascending: false });

    // 添加发布状态过滤
    if (publishedOnly) {
      query = query.eq('published', true);
    }

    // 添加搜索条件
    if (searchTerm && searchTerm.trim()) {
      query = query.or(`title.ilike.%${searchTerm}%,content.ilike.%${searchTerm}%,meta_title.ilike.%${searchTerm}%`);
    }

    // 添加分页
    query = query.range(params.offset, params.offset + params.limit - 1);

    const { data, error, count } = await query;

    if (error) throw error;

    // 手动获取关联的产品信息并映射字段
    const articles = data || [];
    const mappedArticles = [];

    for (const item of articles) {
      const mappedArticle = {
        ...item,
        productId: item.product_id,
        coverImage: item.cover_image,
        videoUrl: item.video_url,
        metaTitle: item.meta_title,
        metaDescription: item.meta_description,
        publishedAt: item.published_at ? new Date(item.published_at) : undefined,
        createdAt: new Date(item.created_at),
        updatedAt: new Date(item.updated_at)
      };

      if (item.product_id) {
        const product = await productService.getById(item.product_id);
        mappedArticle.product = product;
      }

      mappedArticles.push(mappedArticle);
    }

    return {
      data: mappedArticles,
      pagination: createPaginationInfo(params.page, params.limit, count || 0)
    };
  },

  // 获取文章总数
  async getCount(searchTerm?: string, publishedOnly?: boolean): Promise<number> {
    let query = supabase
      .from('articles')
      .select('*', { count: 'exact', head: true });

    // 添加发布状态过滤
    if (publishedOnly) {
      query = query.eq('published', true);
    }

    // 添加搜索条件
    if (searchTerm && searchTerm.trim()) {
      query = query.or(`title.ilike.%${searchTerm}%,content.ilike.%${searchTerm}%,meta_title.ilike.%${searchTerm}%`);
    }

    const { count, error } = await query;

    if (error) throw error;

    return count || 0;
  },

  // 获取所有已发布的文章
  async getPublished(): Promise<Article[]> {
    const { data, error } = await supabase
      .from('articles')
      .select('*')
      .eq('published', true)
      .order('published_at', { ascending: false });

    if (error) throw error;

    // 手动获取关联的产品信息并映射字段
    const articles = data || [];
    const mappedArticles = [];

    for (const item of articles) {
      const mappedArticle = {
        ...item,
        productId: item.product_id,
        coverImage: item.cover_image,
        metaTitle: item.meta_title,
        metaDescription: item.meta_description,
        publishedAt: item.published_at ? new Date(item.published_at) : undefined,
        createdAt: new Date(item.created_at),
        updatedAt: new Date(item.updated_at)
      };

      if (item.product_id) {
        const product = await productService.getById(item.product_id);
        mappedArticle.product = product;
      }

      mappedArticles.push(mappedArticle);
    }

    return mappedArticles;
  },

  // 根据slug获取文章
  async getBySlug(slug: string): Promise<Article | null> {
    const { data, error } = await supabase
      .from('articles')
      .select('*')
      .eq('slug', slug)
      .eq('published', true)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null;
      throw error;
    }

    if (!data) return null;

    // 映射字段并获取关联的产品信息
    const mappedArticle = {
      ...data,
      productId: data.product_id,
      coverImage: data.cover_image,
      videoUrl: data.video_url,
      metaTitle: data.meta_title,
      metaDescription: data.meta_description,
      publishedAt: data.published_at ? new Date(data.published_at) : undefined,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at)
    };

    if (data.product_id) {
      const product = await productService.getById(data.product_id);
      mappedArticle.product = product;
    }

    return mappedArticle;
  },

  // 根据产品ID获取文章
  async getByProductId(productId: string): Promise<Article | null> {
    const { data, error } = await supabase
      .from('articles')
      .select('*')
      .eq('product_id', productId)
      .maybeSingle(); // 使用 maybeSingle 而不是 single 来避免406错误

    if (error) {
      console.error('Error fetching article by product ID:', error);
      return null;
    }

    if (!data) return null;

    // 映射字段并获取关联的产品信息
    const mappedArticle = {
      ...data,
      productId: data.product_id,
      coverImage: data.cover_image,
      videoUrl: data.video_url,
      metaTitle: data.meta_title,
      metaDescription: data.meta_description,
      publishedAt: data.published_at ? new Date(data.published_at) : undefined,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at)
    };

    if (data.product_id) {
      try {
        const product = await productService.getById(data.product_id);
        mappedArticle.product = product;
      } catch (error) {
        console.error('Error loading product for article:', error);
      }
    }

    return mappedArticle;
  },

  // 根据ID获取文章
  async getById(id: string): Promise<Article | null> {
    const { data, error } = await supabase
      .from('articles')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null;
      throw error;
    }

    if (!data) return null;

    // 映射字段并获取关联的产品信息
    const mappedArticle = {
      ...data,
      productId: data.product_id,
      coverImage: data.cover_image,
      videoUrl: data.video_url,
      metaTitle: data.meta_title,
      metaDescription: data.meta_description,
      publishedAt: data.published_at ? new Date(data.published_at) : undefined,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at)
    };

    if (data.product_id) {
      try {
        const product = await productService.getById(data.product_id);
        mappedArticle.product = product;
      } catch (error) {
        console.error('Error loading product for article:', error);
      }
    }

    return mappedArticle;
  },

  // 创建文章
  async create(article: Omit<Article, 'id' | 'createdAt' | 'updatedAt'>): Promise<Article> {
    const { data, error } = await supabase
      .from('articles')
      .insert([{
        title: article.title,
        content: article.content,
        excerpt: article.excerpt,
        slug: article.slug,
        product_id: article.productId,
        cover_image: article.coverImage,
        video_url: article.videoUrl,
        meta_title: article.metaTitle,
        meta_description: article.metaDescription,
        keywords: article.keywords,
        published: article.published,
        published_at: article.published ? new Date().toISOString() : null
      }])
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // 更新文章
  async update(id: string, updates: Partial<Article>): Promise<Article> {
    const updateData: any = {};
    
    if (updates.title) updateData.title = updates.title;
    if (updates.content) updateData.content = updates.content;
    if (updates.excerpt) updateData.excerpt = updates.excerpt;
    if (updates.slug) updateData.slug = updates.slug;
    if (updates.productId) updateData.product_id = updates.productId;
    if (updates.coverImage !== undefined) updateData.cover_image = updates.coverImage;
    if (updates.videoUrl !== undefined) updateData.video_url = updates.videoUrl;
    if (updates.metaTitle) updateData.meta_title = updates.metaTitle;
    if (updates.metaDescription) updateData.meta_description = updates.metaDescription;
    if (updates.keywords) updateData.keywords = updates.keywords;
    if (typeof updates.published === 'boolean') {
      updateData.published = updates.published;
      if (updates.published) {
        updateData.published_at = new Date().toISOString();
      }
    }

    const { data, error } = await supabase
      .from('articles')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },

  // 删除文章
  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('articles')
      .delete()
      .eq('id', id);

    if (error) throw error;
  },

  // 获取相关文章
  async getRelatedArticles(articleId: string, limit: number = 3): Promise<Article[]> {
    // 首先获取当前文章信息
    const currentArticle = await this.getById(articleId);
    if (!currentArticle) return [];

    const relatedArticles: Article[] = [];

    // 策略1: 获取同类别产品的文章
    if (currentArticle.product?.category) {
      const { data: categoryArticles, error } = await supabase
        .from('articles')
        .select(`
          *,
          products!inner(category)
        `)
        .eq('published', true)
        .eq('products.category', currentArticle.product.category)
        .neq('id', articleId)
        .order('published_at', { ascending: false })
        .limit(limit);

      if (!error && categoryArticles) {
        for (const item of categoryArticles) {
          const mappedArticle = {
            ...item,
            productId: item.product_id,
            coverImage: item.cover_image,
            metaTitle: item.meta_title,
            metaDescription: item.meta_description,
            publishedAt: item.published_at ? new Date(item.published_at) : undefined,
            createdAt: new Date(item.created_at),
            updatedAt: new Date(item.updated_at)
          };

          if (item.product_id) {
            const product = await productService.getById(item.product_id);
            mappedArticle.product = product;
          }

          relatedArticles.push(mappedArticle);
        }
      }
    }

    // 策略2: 如果同类别文章不足，通过关键词匹配获取更多文章
    if (relatedArticles.length < limit && currentArticle.keywords.length > 0) {
      const remainingLimit = limit - relatedArticles.length;
      const existingIds = [articleId, ...relatedArticles.map(a => a.id)];

      const { data: keywordArticles, error } = await supabase
        .from('articles')
        .select('*')
        .eq('published', true)
        .not('id', 'in', `(${existingIds.join(',')})`)
        .overlaps('keywords', currentArticle.keywords)
        .order('published_at', { ascending: false })
        .limit(remainingLimit);

      if (!error && keywordArticles) {
        for (const item of keywordArticles) {
          const mappedArticle = {
            ...item,
            productId: item.product_id,
            coverImage: item.cover_image,
            metaTitle: item.meta_title,
            metaDescription: item.meta_description,
            publishedAt: item.published_at ? new Date(item.published_at) : undefined,
            createdAt: new Date(item.created_at),
            updatedAt: new Date(item.updated_at)
          };

          if (item.product_id) {
            const product = await productService.getById(item.product_id);
            mappedArticle.product = product;
          }

          relatedArticles.push(mappedArticle);
        }
      }
    }

    // 策略3: 如果仍然不足，获取最新发布的文章作为补充
    if (relatedArticles.length < limit) {
      const remainingLimit = limit - relatedArticles.length;
      const existingIds = [articleId, ...relatedArticles.map(a => a.id)];

      const { data: latestArticles, error } = await supabase
        .from('articles')
        .select('*')
        .eq('published', true)
        .not('id', 'in', `(${existingIds.join(',')})`)
        .order('published_at', { ascending: false })
        .limit(remainingLimit);

      if (!error && latestArticles) {
        for (const item of latestArticles) {
          const mappedArticle = {
            ...item,
            productId: item.product_id,
            coverImage: item.cover_image,
            metaTitle: item.meta_title,
            metaDescription: item.meta_description,
            publishedAt: item.published_at ? new Date(item.published_at) : undefined,
            createdAt: new Date(item.created_at),
            updatedAt: new Date(item.updated_at)
          };

          if (item.product_id) {
            const product = await productService.getById(item.product_id);
            mappedArticle.product = product;
          }

          relatedArticles.push(mappedArticle);
        }
      }
    }

    return relatedArticles.slice(0, limit);
  }
};

// 分类相关操作
export const categoryService = {
  // 获取所有分类
  async getAll(): Promise<Category[]> {
    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .order('name');

    if (error) throw error;

    // 映射数据库字段到TypeScript类型
    return (data || []).map(item => ({
      ...item,
      createdAt: new Date(item.created_at)
    }));
  },

  // 分页获取分类
  async getPaginated(params: PaginationParams, searchTerm?: string): Promise<PaginatedResult<Category>> {
    let query = supabase
      .from('categories')
      .select('*', { count: 'exact' })
      .order('name');

    // 添加搜索条件
    if (searchTerm && searchTerm.trim()) {
      query = query.or(`name.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%,slug.ilike.%${searchTerm}%`);
    }

    // 添加分页
    query = query.range(params.offset, params.offset + params.limit - 1);

    const { data, error, count } = await query;

    if (error) throw error;

    // 映射数据库字段到TypeScript类型
    const categories = (data || []).map(item => ({
      ...item,
      createdAt: new Date(item.created_at)
    }));

    return {
      data: categories,
      pagination: createPaginationInfo(params.page, params.limit, count || 0)
    };
  },

  // 获取分类总数
  async getCount(searchTerm?: string): Promise<number> {
    let query = supabase
      .from('categories')
      .select('*', { count: 'exact', head: true });

    // 添加搜索条件
    if (searchTerm && searchTerm.trim()) {
      query = query.or(`name.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%,slug.ilike.%${searchTerm}%`);
    }

    const { count, error } = await query;

    if (error) throw error;

    return count || 0;
  },

  // 根据slug获取分类
  async getBySlug(slug: string): Promise<Category | null> {
    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .eq('slug', slug)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null;
      throw error;
    }

    if (!data) return null;

    // 映射数据库字段到TypeScript类型
    return {
      ...data,
      createdAt: new Date(data.created_at)
    };
  },

  // 根据ID获取分类
  async getById(id: string): Promise<Category | null> {
    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null;
      throw error;
    }

    if (!data) return null;

    // 映射数据库字段到TypeScript类型
    return {
      ...data,
      createdAt: new Date(data.created_at)
    };
  },

  // 创建分类
  async create(category: Omit<Category, 'id' | 'createdAt'>): Promise<Category> {
    const { data, error } = await supabase
      .from('categories')
      .insert([{
        name: category.name,
        slug: category.slug,
        description: category.description
      }])
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // 更新分类
  async update(id: string, category: Partial<Omit<Category, 'id' | 'createdAt'>>): Promise<Category> {
    const updateData: any = {};

    if (category.name !== undefined) updateData.name = category.name;
    if (category.slug !== undefined) updateData.slug = category.slug;
    if (category.description !== undefined) updateData.description = category.description;

    const { data, error } = await supabase
      .from('categories')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // 删除分类
  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('categories')
      .delete()
      .eq('id', id);

    if (error) throw error;
  },

  // 检查分类是否被产品使用
  async isUsedByProducts(slug: string): Promise<boolean> {
    const { data, error } = await supabase
      .from('products')
      .select('id')
      .eq('category', slug)
      .limit(1);

    if (error) throw error;
    return (data && data.length > 0) || false;
  }
};

// 统计数据服务
export const statsService = {
  // 获取基础统计数据
  async getBasicStats(dateRange?: { start: Date; end: Date }) {
    try {
      // 并行获取所有基础统计数据
      const [
        totalProducts,
        totalArticles,
        publishedArticles,
        totalCategories,
        productsByCategory,
        recentProducts,
        recentArticles,
        totalPageViews,
        totalAmazonClicks,
        uniqueVisitors
      ] = await Promise.all([
        this.getProductCount(dateRange),
        this.getArticleCount(dateRange),
        this.getPublishedArticleCount(dateRange),
        this.getCategoryCount(dateRange),
        this.getProductsByCategory(dateRange),
        this.getRecentProducts(7), // 最近7天
        this.getRecentArticles(7), // 最近7天
        analyticsService.getPageViewStats(dateRange),
        analyticsService.getAmazonClickStats(dateRange),
        analyticsService.getUniqueVisitors(dateRange)
      ]);

      return {
        totalProducts,
        totalArticles,
        publishedArticles,
        unpublishedArticles: totalArticles - publishedArticles,
        totalCategories,
        productsByCategory,
        recentProducts,
        recentArticles,
        totalPageViews,
        totalAmazonClicks,
        uniqueVisitors,
        // 计算一些派生统计
        publishRate: totalArticles > 0 ? (publishedArticles / totalArticles * 100).toFixed(1) : '0',
        avgProductsPerCategory: totalCategories > 0 ? (totalProducts / totalCategories).toFixed(1) : '0',
        conversionRate: totalPageViews > 0 ? (totalAmazonClicks / totalPageViews * 100).toFixed(2) : '0'
      };
    } catch (error) {
      console.error('Error getting basic stats:', error);
      throw error;
    }
  },

  // 获取产品总数
  async getProductCount(dateRange?: { start: Date; end: Date }): Promise<number> {
    let query = supabase
      .from('products')
      .select('*', { count: 'exact', head: true });

    if (dateRange) {
      query = query
        .gte('created_at', dateRange.start.toISOString())
        .lte('created_at', dateRange.end.toISOString());
    }

    const { count, error } = await query;
    if (error) throw error;
    return count || 0;
  },

  // 获取文章总数
  async getArticleCount(dateRange?: { start: Date; end: Date }): Promise<number> {
    let query = supabase
      .from('articles')
      .select('*', { count: 'exact', head: true });

    if (dateRange) {
      query = query
        .gte('created_at', dateRange.start.toISOString())
        .lte('created_at', dateRange.end.toISOString());
    }

    const { count, error } = await query;
    if (error) throw error;
    return count || 0;
  },

  // 获取已发布文章数
  async getPublishedArticleCount(dateRange?: { start: Date; end: Date }): Promise<number> {
    let query = supabase
      .from('articles')
      .select('*', { count: 'exact', head: true })
      .eq('published', true);

    if (dateRange) {
      query = query
        .gte('created_at', dateRange.start.toISOString())
        .lte('created_at', dateRange.end.toISOString());
    }

    const { count, error } = await query;
    if (error) throw error;
    return count || 0;
  },

  // 获取分类总数
  async getCategoryCount(dateRange?: { start: Date; end: Date }): Promise<number> {
    let query = supabase
      .from('categories')
      .select('*', { count: 'exact', head: true });

    if (dateRange) {
      query = query
        .gte('created_at', dateRange.start.toISOString())
        .lte('created_at', dateRange.end.toISOString());
    }

    const { count, error } = await query;
    if (error) throw error;
    return count || 0;
  },

  // 按分类统计产品数量
  async getProductsByCategory(dateRange?: { start: Date; end: Date }) {
    let query = supabase
      .from('products')
      .select('category');

    if (dateRange) {
      query = query
        .gte('created_at', dateRange.start.toISOString())
        .lte('created_at', dateRange.end.toISOString());
    }

    const { data, error } = await query;
    if (error) throw error;

    // 统计每个分类的产品数量
    const categoryStats: Record<string, number> = {};
    (data || []).forEach(item => {
      const category = item.category || 'uncategorized';
      categoryStats[category] = (categoryStats[category] || 0) + 1;
    });

    // 转换为数组格式，按数量排序
    return Object.entries(categoryStats)
      .map(([category, count]) => ({
        category: category.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase()),
        count,
        slug: category
      }))
      .sort((a, b) => b.count - a.count);
  },

  // 获取最近创建的产品
  async getRecentProducts(days: number = 7) {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const { data, error } = await supabase
      .from('products')
      .select('*', { count: 'exact', head: true })
      .gte('created_at', startDate.toISOString());

    if (error) throw error;
    return data?.length || 0;
  },

  // 获取最近创建的文章
  async getRecentArticles(days: number = 7) {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const { data, error } = await supabase
      .from('articles')
      .select('*', { count: 'exact', head: true })
      .gte('created_at', startDate.toISOString());

    if (error) throw error;
    return data?.length || 0;
  },

  // 获取时间趋势数据
  async getTimeTrends(days: number = 30) {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    // 获取产品创建趋势
    const { data: productData, error: productError } = await supabase
      .from('products')
      .select('created_at')
      .gte('created_at', startDate.toISOString())
      .lte('created_at', endDate.toISOString())
      .order('created_at', { ascending: true });

    if (productError) throw productError;

    // 获取文章创建趋势
    const { data: articleData, error: articleError } = await supabase
      .from('articles')
      .select('created_at')
      .gte('created_at', startDate.toISOString())
      .lte('created_at', endDate.toISOString())
      .order('created_at', { ascending: true });

    if (articleError) throw articleError;

    // 按日期分组统计
    const productTrends = this.groupByDate(productData || []);
    const articleTrends = this.groupByDate(articleData || []);

    return {
      products: productTrends,
      articles: articleTrends
    };
  },

  // 辅助方法：按日期分组
  private groupByDate(data: Array<{ created_at: string }>) {
    const grouped: Record<string, number> = {};

    data.forEach(item => {
      const date = new Date(item.created_at).toISOString().split('T')[0];
      grouped[date] = (grouped[date] || 0) + 1;
    });

    return Object.entries(grouped)
      .map(([date, count]) => ({ date, count }))
      .sort((a, b) => a.date.localeCompare(b.date));
  },

  // 获取图表数据
  async getChartData(dateRange?: { start: Date; end: Date }) {
    try {
      const [
        timeTrends,
        categoryStats,
        topPages,
        deviceBreakdown
      ] = await Promise.all([
        this.getTimeTrends(30), // 最近30天
        this.getProductsByCategory(dateRange),
        analyticsService.getTopPages(dateRange, 10),
        analyticsService.getDeviceBreakdown(dateRange)
      ]);

      // 转换时间趋势数据格式
      const timeTrendData = timeTrends.products.map(item => ({
        date: item.date,
        products: item.count,
        articles: timeTrends.articles.find(a => a.date === item.date)?.count || 0
      }));

      // 转换分类数据格式
      const categoryData = categoryStats.map(item => ({
        category: item.category,
        count: item.count,
        slug: item.slug
      }));

      // 转换热门页面数据格式
      const topPagesData = topPages.map(item => ({
        page: item.path,
        views: item.count,
        title: item.title
      }));

      // 转换设备数据格式
      const deviceData = deviceBreakdown.map(item => ({
        device: item.device === 'desktop' ? '桌面端' :
               item.device === 'mobile' ? '移动端' :
               item.device === 'tablet' ? '平板端' : '未知',
        count: item.count,
        percentage: item.percentage
      }));

      // 生成模拟转化率数据（实际应该从数据库获取）
      const conversionData = timeTrendData.map(item => ({
        date: item.date,
        pageViews: Math.floor(Math.random() * 100) + 50,
        amazonClicks: Math.floor(Math.random() * 20) + 5,
        conversionRate: Math.floor(Math.random() * 10) + 2
      }));

      return {
        timeTrend: timeTrendData,
        categoryDistribution: categoryData,
        topPages: topPagesData,
        deviceBreakdown: deviceData,
        conversionTrend: conversionData
      };
    } catch (error) {
      console.error('Error getting chart data:', error);
      throw error;
    }
  }
};

export { productService, articleService, categoryService, statsService, analyticsService };

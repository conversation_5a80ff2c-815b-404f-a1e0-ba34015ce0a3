import { QuizAnswer, QuizResult, QuizSession } from '@/types';

/**
 * 本地存储管理类 - 处理测验数据的本地缓存
 */
export class QuizStorage {
  private static readonly STORAGE_PREFIX = 'quiz_';
  private static readonly ANSWERS_KEY = 'answers';
  private static readonly RESULT_KEY = 'result';
  private static readonly SESSION_KEY = 'session';
  private static readonly CONFIG_KEY = 'config';

  /**
   * 检查是否支持本地存储
   */
  private static isStorageAvailable(): boolean {
    try {
      const test = '__storage_test__';
      localStorage.setItem(test, test);
      localStorage.removeItem(test);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 生成存储键名
   */
  private static getKey(quizId: string, type: string): string {
    return `${this.STORAGE_PREFIX}${quizId}_${type}`;
  }

  /**
   * 保存测验答案到本地存储
   */
  static saveAnswers(quizId: string, answers: QuizAnswer[]): boolean {
    if (!this.isStorageAvailable()) return false;

    try {
      const key = this.getKey(quizId, this.ANSWERS_KEY);
      const data = {
        answers,
        timestamp: Date.now(),
        version: '1.0'
      };
      localStorage.setItem(key, JSON.stringify(data));
      return true;
    } catch (error) {
      console.error('Error saving answers to localStorage:', error);
      return false;
    }
  }

  /**
   * 从本地存储加载测验答案
   */
  static loadAnswers(quizId: string): QuizAnswer[] {
    if (!this.isStorageAvailable()) return [];

    try {
      const key = this.getKey(quizId, this.ANSWERS_KEY);
      const stored = localStorage.getItem(key);
      
      if (!stored) return [];

      const data = JSON.parse(stored);
      
      // 检查数据是否过期（24小时）
      const maxAge = 24 * 60 * 60 * 1000; // 24小时
      if (Date.now() - data.timestamp > maxAge) {
        this.clearAnswers(quizId);
        return [];
      }

      return data.answers || [];
    } catch (error) {
      console.error('Error loading answers from localStorage:', error);
      return [];
    }
  }

  /**
   * 清除测验答案
   */
  static clearAnswers(quizId: string): boolean {
    if (!this.isStorageAvailable()) return false;

    try {
      const key = this.getKey(quizId, this.ANSWERS_KEY);
      localStorage.removeItem(key);
      return true;
    } catch (error) {
      console.error('Error clearing answers from localStorage:', error);
      return false;
    }
  }

  /**
   * 保存测验结果
   */
  static saveResult(quizId: string, result: QuizResult): boolean {
    if (!this.isStorageAvailable()) return false;

    try {
      const key = this.getKey(quizId, this.RESULT_KEY);
      const data = {
        result,
        timestamp: Date.now(),
        version: '1.0'
      };
      localStorage.setItem(key, JSON.stringify(data));
      return true;
    } catch (error) {
      console.error('Error saving result to localStorage:', error);
      return false;
    }
  }

  /**
   * 从本地存储加载测验结果
   */
  static loadResult(quizId: string): QuizResult | null {
    if (!this.isStorageAvailable()) return null;

    try {
      const key = this.getKey(quizId, this.RESULT_KEY);
      const stored = localStorage.getItem(key);
      
      if (!stored) return null;

      const data = JSON.parse(stored);
      
      // 检查数据是否过期（7天）
      const maxAge = 7 * 24 * 60 * 60 * 1000; // 7天
      if (Date.now() - data.timestamp > maxAge) {
        this.clearResult(quizId);
        return null;
      }

      return data.result || null;
    } catch (error) {
      console.error('Error loading result from localStorage:', error);
      return null;
    }
  }

  /**
   * 清除测验结果
   */
  static clearResult(quizId: string): boolean {
    if (!this.isStorageAvailable()) return false;

    try {
      const key = this.getKey(quizId, this.RESULT_KEY);
      localStorage.removeItem(key);
      return true;
    } catch (error) {
      console.error('Error clearing result from localStorage:', error);
      return false;
    }
  }

  /**
   * 保存会话信息
   */
  static saveSession(quizId: string, sessionData: Partial<QuizSession>): boolean {
    if (!this.isStorageAvailable()) return false;

    try {
      const key = this.getKey(quizId, this.SESSION_KEY);
      const data = {
        session: sessionData,
        timestamp: Date.now(),
        version: '1.0'
      };
      localStorage.setItem(key, JSON.stringify(data));
      return true;
    } catch (error) {
      console.error('Error saving session to localStorage:', error);
      return false;
    }
  }

  /**
   * 加载会话信息
   */
  static loadSession(quizId: string): Partial<QuizSession> | null {
    if (!this.isStorageAvailable()) return null;

    try {
      const key = this.getKey(quizId, this.SESSION_KEY);
      const stored = localStorage.getItem(key);
      
      if (!stored) return null;

      const data = JSON.parse(stored);
      return data.session || null;
    } catch (error) {
      console.error('Error loading session from localStorage:', error);
      return null;
    }
  }

  /**
   * 清除所有测验数据
   */
  static clearAllQuizData(quizId: string): boolean {
    if (!this.isStorageAvailable()) return false;

    try {
      this.clearAnswers(quizId);
      this.clearResult(quizId);
      
      const sessionKey = this.getKey(quizId, this.SESSION_KEY);
      localStorage.removeItem(sessionKey);
      
      return true;
    } catch (error) {
      console.error('Error clearing all quiz data:', error);
      return false;
    }
  }

  /**
   * 获取存储使用情况
   */
  static getStorageInfo(): {
    isAvailable: boolean;
    usedSpace: number;
    totalSpace: number;
    quizDataKeys: string[];
  } {
    const info = {
      isAvailable: this.isStorageAvailable(),
      usedSpace: 0,
      totalSpace: 0,
      quizDataKeys: [] as string[]
    };

    if (!info.isAvailable) return info;

    try {
      // 计算已使用空间
      let totalSize = 0;
      const quizKeys: string[] = [];

      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key) {
          const value = localStorage.getItem(key);
          if (value) {
            totalSize += key.length + value.length;
          }
          
          if (key.startsWith(this.STORAGE_PREFIX)) {
            quizKeys.push(key);
          }
        }
      }

      info.usedSpace = totalSize;
      info.totalSpace = 5 * 1024 * 1024; // 假设5MB限制
      info.quizDataKeys = quizKeys;

    } catch (error) {
      console.error('Error getting storage info:', error);
    }

    return info;
  }

  /**
   * 清理过期数据
   */
  static cleanupExpiredData(): number {
    if (!this.isStorageAvailable()) return 0;

    let cleanedCount = 0;

    try {
      const keysToRemove: string[] = [];
      
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith(this.STORAGE_PREFIX)) {
          try {
            const stored = localStorage.getItem(key);
            if (stored) {
              const data = JSON.parse(stored);
              const maxAge = key.includes(this.RESULT_KEY) 
                ? 7 * 24 * 60 * 60 * 1000  // 结果保存7天
                : 24 * 60 * 60 * 1000;     // 答案保存1天
              
              if (Date.now() - data.timestamp > maxAge) {
                keysToRemove.push(key);
              }
            }
          } catch {
            // 如果数据格式错误，也删除
            keysToRemove.push(key);
          }
        }
      }

      keysToRemove.forEach(key => {
        localStorage.removeItem(key);
        cleanedCount++;
      });

    } catch (error) {
      console.error('Error cleaning up expired data:', error);
    }

    return cleanedCount;
  }

  /**
   * 导出测验数据（用于备份）
   */
  static exportQuizData(quizId: string): string | null {
    if (!this.isStorageAvailable()) return null;

    try {
      const data = {
        quizId,
        answers: this.loadAnswers(quizId),
        result: this.loadResult(quizId),
        session: this.loadSession(quizId),
        exportedAt: new Date().toISOString(),
        version: '1.0'
      };

      return JSON.stringify(data, null, 2);
    } catch (error) {
      console.error('Error exporting quiz data:', error);
      return null;
    }
  }

  /**
   * 导入测验数据（用于恢复）
   */
  static importQuizData(jsonData: string): boolean {
    if (!this.isStorageAvailable()) return false;

    try {
      const data = JSON.parse(jsonData);
      
      if (!data.quizId || !data.version) {
        throw new Error('Invalid data format');
      }

      const { quizId, answers, result, session } = data;

      if (answers) {
        this.saveAnswers(quizId, answers);
      }

      if (result) {
        this.saveResult(quizId, result);
      }

      if (session) {
        this.saveSession(quizId, session);
      }

      return true;
    } catch (error) {
      console.error('Error importing quiz data:', error);
      return false;
    }
  }
}

// 表单验证工具函数

export interface ValidationResult {
  isValid: boolean;
  errors: Record<string, string>;
}

// 产品验证
export interface ProductFormData {
  name: string;
  category: string;
  amazonUrl: string;
  images: string[];
  features: string;
  rating: number;
}

export const validateProduct = (data: ProductFormData): ValidationResult => {
  const errors: Record<string, string> = {};

  // 产品名称验证
  if (!data.name.trim()) {
    errors.name = '产品名称不能为空';
  } else if (data.name.trim().length < 2) {
    errors.name = '产品名称至少需要2个字符';
  } else if (data.name.trim().length > 255) {
    errors.name = '产品名称不能超过255个字符';
  }

  // 分类验证
  if (!data.category) {
    errors.category = '请选择产品分类';
  }

  // Amazon链接验证
  if (!data.amazonUrl.trim()) {
    errors.amazonUrl = 'Amazon链接不能为空';
  } else if (!isValidUrl(data.amazonUrl)) {
    errors.amazonUrl = '请输入有效的URL格式';
  } else if (!data.amazonUrl.includes('amazon.com')) {
    errors.amazonUrl = '请输入有效的Amazon链接';
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
};

// 文章验证
export interface ArticleFormData {
  title: string;
  content: string;
  excerpt: string;
  slug: string;
  productId: string;
  coverImage?: string;
  videoUrl?: string;
  metaTitle: string;
  metaDescription: string;
  keywords: string[];
}

export const validateArticle = (data: ArticleFormData): ValidationResult => {
  const errors: Record<string, string> = {};

  // 标题验证
  if (!data.title.trim()) {
    errors.title = '文章标题不能为空';
  } else if (data.title.trim().length < 5) {
    errors.title = '文章标题至少需要5个字符';
  } else if (data.title.trim().length > 255) {
    errors.title = '文章标题不能超过255个字符';
  }

  // 内容验证
  if (!data.content.trim()) {
    errors.content = '文章内容不能为空';
  } else if (data.content.trim().length < 100) {
    errors.content = '文章内容至少需要100个字符';
  } else if (data.content.trim().length > 50000) {
    errors.content = '文章内容不能超过50,000个字符';
  }

  // 摘要验证
  if (!data.excerpt.trim()) {
    errors.excerpt = '文章摘要不能为空';
  } else if (data.excerpt.trim().length < 20) {
    errors.excerpt = '文章摘要至少需要20个字符';
  } else if (data.excerpt.trim().length > 500) {
    errors.excerpt = '文章摘要不能超过500个字符';
  }

  // Slug验证
  if (!data.slug.trim()) {
    errors.slug = '文章链接不能为空';
  } else if (!isValidSlug(data.slug)) {
    errors.slug = '文章链接只能包含字母、数字和连字符，且不能以连字符开头或结尾';
  } else if (data.slug.length > 255) {
    errors.slug = '文章链接不能超过255个字符';
  }

  // 产品ID验证
  if (!data.productId) {
    errors.productId = '请选择关联产品';
  }

  // 封面图片验证（可选）
  if (data.coverImage && data.coverImage.trim() && !isValidUrl(data.coverImage.trim())) {
    errors.coverImage = '请输入有效的图片URL';
  }

  // 视频URL验证（可选）
  if (data.videoUrl && data.videoUrl.trim() && !isValidUrl(data.videoUrl.trim())) {
    errors.videoUrl = '请输入有效的视频URL';
  }

  // SEO标题验证
  if (!data.metaTitle.trim()) {
    errors.metaTitle = 'SEO标题不能为空';
  } else if (data.metaTitle.trim().length < 10) {
    errors.metaTitle = 'SEO标题至少需要10个字符';
  } else if (data.metaTitle.trim().length > 60) {
    errors.metaTitle = 'SEO标题建议不超过60个字符以获得更好的搜索效果';
  }

  // SEO描述验证
  if (!data.metaDescription.trim()) {
    errors.metaDescription = 'SEO描述不能为空';
  } else if (data.metaDescription.trim().length < 50) {
    errors.metaDescription = 'SEO描述至少需要50个字符';
  } else if (data.metaDescription.trim().length > 160) {
    errors.metaDescription = 'SEO描述建议不超过160个字符以获得更好的搜索效果';
  }

  // 关键词验证
  if (data.keywords.length === 0) {
    errors.keywords = '至少需要添加一个关键词';
  } else if (data.keywords.length > 10) {
    errors.keywords = '关键词数量不建议超过10个';
  } else if (data.keywords.some(k => k.length > 50)) {
    errors.keywords = '每个关键词不能超过50个字符';
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
};

// 分类验证
export interface CategoryFormData {
  name: string;
  slug: string;
  description?: string;
}

export const validateCategory = (data: CategoryFormData): ValidationResult => {
  const errors: Record<string, string> = {};

  // 分类名称验证
  if (!data.name.trim()) {
    errors.name = '分类名称不能为空';
  } else if (data.name.trim().length < 2) {
    errors.name = '分类名称至少需要2个字符';
  } else if (data.name.trim().length > 100) {
    errors.name = '分类名称不能超过100个字符';
  }

  // Slug验证
  if (!data.slug.trim()) {
    errors.slug = 'URL别名不能为空';
  } else if (!isValidSlug(data.slug)) {
    errors.slug = 'URL别名只能包含小写字母、数字和连字符，且不能以连字符开头或结尾';
  } else if (data.slug.length < 2) {
    errors.slug = 'URL别名至少需要2个字符';
  } else if (data.slug.length > 100) {
    errors.slug = 'URL别名不能超过100个字符';
  }

  // 描述验证（可选）
  if (data.description && data.description.trim().length > 500) {
    errors.description = '描述不能超过500个字符';
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
};

// 工具函数
export const isValidUrl = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

export const isValidSlug = (slug: string): boolean => {
  // Slug应该只包含小写字母、数字和连字符，不能以连字符开头或结尾
  const slugRegex = /^[a-z0-9]+(?:-[a-z0-9]+)*$/;
  return slugRegex.test(slug);
};

// 生成slug的函数
export const generateSlug = (title: string): string => {
  return title
    .toLowerCase()
    .replace(/[áàäâ]/g, 'a')
    .replace(/[éèëê]/g, 'e')
    .replace(/[íìïî]/g, 'i')
    .replace(/[óòöô]/g, 'o')
    .replace(/[úùüû]/g, 'u')
    .replace(/ñ/g, 'n')
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .replace(/^-+|-+$/g, '') // 移除开头和结尾的连字符
    .trim();
};

// 错误消息本地化
export const getErrorMessage = (field: string, error: string): string => {
  const errorMessages: Record<string, Record<string, string>> = {
    product: {
      name_required: '产品名称不能为空',
      name_too_short: '产品名称至少需要2个字符',
      name_too_long: '产品名称不能超过255个字符',
      description_required: '产品描述不能为空',
      description_too_short: '产品描述至少需要10个字符',
      description_too_long: '产品描述不能超过2000个字符',
      category_required: '请选择产品分类',
      amazon_url_required: 'Amazon链接不能为空',
      amazon_url_invalid: '请输入有效的Amazon链接',
      price_invalid: '价格必须是有效数字',
      rating_invalid: '评分必须在0-5之间',
      features_required: '至少需要添加一个产品特性'
    },
    article: {
      title_required: '文章标题不能为空',
      title_too_short: '文章标题至少需要5个字符',
      title_too_long: '文章标题不能超过255个字符',
      content_required: '文章内容不能为空',
      content_too_short: '文章内容至少需要100个字符',
      excerpt_required: '文章摘要不能为空',
      slug_required: '文章链接不能为空',
      slug_invalid: '文章链接格式不正确',
      product_id_required: '请选择关联产品',
      meta_title_required: 'SEO标题不能为空',
      meta_description_required: 'SEO描述不能为空',
      keywords_required: '至少需要添加一个关键词'
    }
  };

  return errorMessages[field]?.[error] || error;
};

// 表单数据清理函数
export const sanitizeProductData = (data: ProductFormData) => {
  return {
    name: data.name.trim(),
    category: data.category,
    amazonUrl: data.amazonUrl.trim(),
    images: data.images.filter(img => img.trim()).map(img => img.trim()), // 过滤空字符串并清理
    features: data.features?.trim() || '',
    rating: data.rating || 0.0
  };
};

export const sanitizeArticleData = (data: ArticleFormData) => {
  return {
    title: data.title.trim(),
    content: data.content.trim(),
    excerpt: data.excerpt.trim(),
    slug: data.slug.trim().toLowerCase(),
    productId: data.productId,
    coverImage: data.coverImage?.trim() || undefined,
    videoUrl: data.videoUrl?.trim() || undefined,
    metaTitle: data.metaTitle.trim(),
    metaDescription: data.metaDescription.trim(),
    keywords: data.keywords.filter(k => k.trim()).map(k => k.trim())
  };
};

export const sanitizeCategoryData = (data: CategoryFormData) => {
  return {
    name: data.name.trim(),
    slug: data.slug.trim().toLowerCase(),
    description: data.description?.trim() || undefined
  };
};

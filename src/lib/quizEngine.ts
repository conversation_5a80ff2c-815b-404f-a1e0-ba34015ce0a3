import { 
  QuizConfig, 
  Q<PERSON><PERSON>uestion, 
  QuizAnswer, 
  QuizResult, 
  QuizResultGroup,
  QuizSession,
  Product,
  ProductGroupRule
} from '@/types';
import { supabase } from './supabase';
import { productService } from './database';

/**
 * 测验引擎类 - 处理测验逻辑和结果计算
 */
export class QuizEngine {
  private config: QuizConfig;

  constructor(config: QuizConfig) {
    this.config = config;
  }

  /**
   * 计算用户答案的维度得分
   */
  calculateDimensionScores(answers: QuizAnswer[]): Record<string, number> {
    const dimensionScores: Record<string, number> = {};
    const dimensionWeights: Record<string, number> = {};

    // 初始化维度得分
    this.config.dimensions.forEach(dimension => {
      dimensionScores[dimension] = 0;
      dimensionWeights[dimension] = 0;
    });

    // 遍历所有答案
    answers.forEach(answer => {
      const question = this.config.questions.find(q => q.id === answer.questionId);
      if (!question) return;

      const weight = question.weight || 1;

      if (question.type === 'single_choice') {
        const option = question.options?.find(opt => opt.value === answer.value);
        if (option?.score) {
          Object.entries(option.score).forEach(([dimension, score]) => {
            if (dimensionScores.hasOwnProperty(dimension)) {
              dimensionScores[dimension] += score * weight;
              dimensionWeights[dimension] += weight;
            }
          });
        }
      } else if (question.type === 'multiple_choice') {
        const selectedValues = Array.isArray(answer.value) ? answer.value : [answer.value];
        selectedValues.forEach(value => {
          const option = question.options?.find(opt => opt.value === value);
          if (option?.score) {
            Object.entries(option.score).forEach(([dimension, score]) => {
              if (dimensionScores.hasOwnProperty(dimension)) {
                dimensionScores[dimension] += score * weight;
                dimensionWeights[dimension] += weight;
              }
            });
          }
        });
      } else if (question.type === 'slider' || question.type === 'rating') {
        const numericValue = typeof answer.value === 'number' ? answer.value : parseFloat(answer.value as string);
        if (!isNaN(numericValue)) {
          // 对于滑块和评分问题，假设它们直接映射到特定维度
          // 这里需要根据具体问题配置来确定映射关系
          const primaryDimension = this.getPrimaryDimensionForQuestion(question);
          if (primaryDimension && dimensionScores.hasOwnProperty(primaryDimension)) {
            dimensionScores[primaryDimension] += numericValue * weight;
            dimensionWeights[primaryDimension] += weight;
          }
        }
      }
    });

    // 计算加权平均分
    Object.keys(dimensionScores).forEach(dimension => {
      if (dimensionWeights[dimension] > 0) {
        dimensionScores[dimension] = dimensionScores[dimension] / dimensionWeights[dimension];
      }
    });

    return dimensionScores;
  }

  /**
   * 获取问题的主要维度（用于滑块和评分问题）
   */
  private getPrimaryDimensionForQuestion(question: QuizQuestion): string | null {
    // 这里可以根据问题ID或其他属性来确定主要维度
    // 简单实现：根据问题ID的前缀来判断
    if (question.id.includes('experience')) return 'experience';
    if (question.id.includes('intensity')) return 'intensity';
    if (question.id.includes('social')) return 'social';
    if (question.id.includes('exploration')) return 'exploration';
    
    // 默认返回第一个维度
    return this.config.dimensions[0] || null;
  }

  /**
   * 根据维度得分匹配最佳结果组
   */
  findBestResultGroup(dimensionScores: Record<string, number>): QuizResultGroup | null {
    let bestMatch: QuizResultGroup | null = null;
    let bestScore = -1;

    this.config.resultGroups.forEach(group => {
      let matchScore = 0;
      let totalDimensions = 0;

      Object.entries(group.dimensionScores).forEach(([dimension, range]) => {
        if (dimensionScores.hasOwnProperty(dimension)) {
          const score = dimensionScores[dimension];
          const [min, max] = range;
          
          if (score >= min && score <= max) {
            // 完全匹配得满分
            matchScore += 1;
          } else {
            // 部分匹配根据距离给分
            const distance = Math.min(Math.abs(score - min), Math.abs(score - max));
            const maxDistance = Math.max(max - min, 1); // 避免除零
            matchScore += Math.max(0, 1 - distance / maxDistance);
          }
          totalDimensions++;
        }
      });

      if (totalDimensions > 0) {
        const normalizedScore = matchScore / totalDimensions;
        if (normalizedScore > bestScore) {
          bestScore = normalizedScore;
          bestMatch = group;
        }
      }
    });

    return bestMatch;
  }

  /**
   * 根据结果组获取推荐产品
   */
  async getRecommendedProducts(resultGroup: QuizResultGroup): Promise<Product[]> {
    try {
      // 如果有指定的推荐产品ID，优先使用
      if (resultGroup.recommendedProducts && resultGroup.recommendedProducts.length > 0) {
        const products = await Promise.all(
          resultGroup.recommendedProducts.map(id => productService.getById(id))
        );
        return products.filter(p => p !== null) as Product[];
      }

      // 否则根据产品分类获取产品
      const allProducts: Product[] = [];
      for (const category of resultGroup.productCategories) {
        const categoryProducts = await productService.getByCategory(category, 5);
        allProducts.push(...categoryProducts);
      }

      // 去重并限制数量
      const uniqueProducts = allProducts.filter((product, index, self) => 
        index === self.findIndex(p => p.id === product.id)
      );

      return uniqueProducts.slice(0, 6); // 最多返回6个产品
    } catch (error) {
      console.error('Error getting recommended products:', error);
      return [];
    }
  }

  /**
   * 计算推荐置信度
   */
  calculateConfidence(dimensionScores: Record<string, number>, resultGroup: QuizResultGroup): number {
    let totalMatch = 0;
    let totalDimensions = 0;

    Object.entries(resultGroup.dimensionScores).forEach(([dimension, range]) => {
      if (dimensionScores.hasOwnProperty(dimension)) {
        const score = dimensionScores[dimension];
        const [min, max] = range;
        
        if (score >= min && score <= max) {
          totalMatch += 1;
        } else {
          const distance = Math.min(Math.abs(score - min), Math.abs(score - max));
          const maxDistance = Math.max(max - min, 1);
          totalMatch += Math.max(0, 1 - distance / maxDistance);
        }
        totalDimensions++;
      }
    });

    return totalDimensions > 0 ? totalMatch / totalDimensions : 0;
  }

  /**
   * 处理测验完成，计算结果
   */
  async processQuizCompletion(answers: QuizAnswer[]): Promise<QuizResult | null> {
    try {
      // 1. 计算维度得分
      const dimensionScores = this.calculateDimensionScores(answers);

      // 2. 找到最佳匹配的结果组
      const resultGroup = this.findBestResultGroup(dimensionScores);
      if (!resultGroup) {
        throw new Error('No matching result group found');
      }

      // 3. 获取推荐产品
      const recommendedProducts = await this.getRecommendedProducts(resultGroup);

      // 4. 计算置信度
      const confidence = this.calculateConfidence(dimensionScores, resultGroup);

      return {
        resultGroupId: resultGroup.id,
        resultGroup,
        dimensionScores,
        recommendedProducts,
        confidence
      };
    } catch (error) {
      console.error('Error processing quiz completion:', error);
      return null;
    }
  }

  /**
   * 验证答案的完整性
   */
  validateAnswers(answers: QuizAnswer[]): { valid: boolean; missingQuestions: string[] } {
    const answeredQuestionIds = new Set(answers.map(a => a.questionId));
    const missingQuestions: string[] = [];

    this.config.questions.forEach(question => {
      if (question.required !== false && !answeredQuestionIds.has(question.id)) {
        missingQuestions.push(question.id);
      }
    });

    return {
      valid: missingQuestions.length === 0,
      missingQuestions
    };
  }
}

/**
 * 测验服务类 - 处理数据库操作
 */
export class QuizService {
  /**
   * 获取活跃的测验配置
   */
  static async getActiveQuizConfig(): Promise<QuizConfig | null> {
    try {
      const { data, error } = await supabase
        .from('quiz_configs')
        .select('*')
        .eq('active', true)
        .order('created_at', { ascending: false })
        .limit(1)
        .single();

      if (error) {
        console.error('Error fetching quiz config:', error);
        return null;
      }

      return this.transformQuizConfig(data);
    } catch (error) {
      console.error('Error in getActiveQuizConfig:', error);
      return null;
    }
  }

  /**
   * 获取所有测验配置
   */
  static async getAllQuizConfigs(): Promise<QuizConfig[]> {
    try {
      const { data, error } = await supabase
        .from('quiz_configs')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching all quiz configs:', error);
        return [];
      }

      return (data || []).map(this.transformQuizConfig);
    } catch (error) {
      console.error('Error in getAllQuizConfigs:', error);
      return [];
    }
  }

  /**
   * 根据ID获取测验配置
   */
  static async getQuizConfigById(id: string): Promise<QuizConfig | null> {
    try {
      const { data, error } = await supabase
        .from('quiz_configs')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        console.error('Error fetching quiz config by id:', error);
        return null;
      }

      return this.transformQuizConfig(data);
    } catch (error) {
      console.error('Error in getQuizConfigById:', error);
      return null;
    }
  }

  /**
   * 创建新的测验配置
   */
  static async createQuizConfig(config: Omit<QuizConfig, 'id' | 'createdAt' | 'updatedAt'>): Promise<QuizConfig | null> {
    try {
      const { data, error } = await supabase
        .from('quiz_configs')
        .insert({
          title: config.title,
          description: config.description,
          questions: config.questions,
          result_groups: config.resultGroups,
          dimensions: config.dimensions,
          active: config.active
        })
        .select('*')
        .single();

      if (error) {
        console.error('Error creating quiz config:', error);
        return null;
      }

      return this.transformQuizConfig(data);
    } catch (error) {
      console.error('Error in createQuizConfig:', error);
      return null;
    }
  }

  /**
   * 更新测验配置
   */
  static async updateQuizConfig(id: string, updates: Partial<QuizConfig>): Promise<QuizConfig | null> {
    try {
      const updateData: any = {};

      if (updates.title !== undefined) updateData.title = updates.title;
      if (updates.description !== undefined) updateData.description = updates.description;
      if (updates.questions !== undefined) updateData.questions = updates.questions;
      if (updates.resultGroups !== undefined) updateData.result_groups = updates.resultGroups;
      if (updates.dimensions !== undefined) updateData.dimensions = updates.dimensions;
      if (updates.active !== undefined) updateData.active = updates.active;

      const { data, error } = await supabase
        .from('quiz_configs')
        .update(updateData)
        .eq('id', id)
        .select('*')
        .single();

      if (error) {
        console.error('Error updating quiz config:', error);
        return null;
      }

      return this.transformQuizConfig(data);
    } catch (error) {
      console.error('Error in updateQuizConfig:', error);
      return null;
    }
  }

  /**
   * 删除测验配置
   */
  static async deleteQuizConfig(id: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('quiz_configs')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Error deleting quiz config:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error in deleteQuizConfig:', error);
      return false;
    }
  }

  /**
   * 切换测验配置的激活状态
   */
  static async toggleQuizConfigActive(id: string): Promise<boolean> {
    try {
      // 首先获取当前状态
      const currentConfig = await this.getQuizConfigById(id);
      if (!currentConfig) return false;

      // 如果要激活此配置，先停用所有其他配置
      if (!currentConfig.active) {
        await supabase
          .from('quiz_configs')
          .update({ active: false })
          .neq('id', id);
      }

      // 切换当前配置的状态
      const { error } = await supabase
        .from('quiz_configs')
        .update({ active: !currentConfig.active })
        .eq('id', id);

      if (error) {
        console.error('Error toggling quiz config active status:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error in toggleQuizConfigActive:', error);
      return false;
    }
  }

  /**
   * 转换数据库记录为QuizConfig对象
   */
  private static transformQuizConfig(data: any): QuizConfig {
    return {
      id: data.id,
      title: data.title,
      description: data.description,
      questions: data.questions,
      resultGroups: data.result_groups,
      dimensions: data.dimensions,
      active: data.active,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at)
    };
  }

  /**
   * 创建测验会话
   */
  static async createQuizSession(quizConfigId: string, sessionId: string): Promise<string | null> {
    try {
      const { data, error } = await supabase
        .from('quiz_sessions')
        .insert({
          quiz_config_id: quizConfigId,
          session_id: sessionId,
          answers: null,
          result: null
        })
        .select('id')
        .single();

      if (error) {
        console.error('Error creating quiz session:', error);
        return null;
      }

      return data.id;
    } catch (error) {
      console.error('Error in createQuizSession:', error);
      return null;
    }
  }

  /**
   * 更新测验会话
   */
  static async updateQuizSession(
    sessionId: string, 
    answers: QuizAnswer[], 
    result?: QuizResult
  ): Promise<boolean> {
    try {
      const updateData: any = {
        answers: answers,
        updated_at: new Date().toISOString()
      };

      if (result) {
        updateData.result = result;
        updateData.completed_at = new Date().toISOString();
      }

      const { error } = await supabase
        .from('quiz_sessions')
        .update(updateData)
        .eq('id', sessionId);

      if (error) {
        console.error('Error updating quiz session:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error in updateQuizSession:', error);
      return false;
    }
  }

  /**
   * 获取测验会话
   */
  static async getQuizSession(sessionId: string): Promise<QuizSession | null> {
    try {
      const { data, error } = await supabase
        .from('quiz_sessions')
        .select('*')
        .eq('id', sessionId)
        .single();

      if (error) {
        console.error('Error fetching quiz session:', error);
        return null;
      }

      return this.transformQuizSession(data);
    } catch (error) {
      console.error('Error in getQuizSession:', error);
      return null;
    }
  }

  /**
   * 获取所有测验会话（分页）
   */
  static async getQuizSessions(
    page: number = 1,
    limit: number = 20,
    quizConfigId?: string
  ): Promise<{ sessions: QuizSession[], total: number }> {
    try {
      let query = supabase
        .from('quiz_sessions')
        .select('*', { count: 'exact' })
        .order('started_at', { ascending: false })
        .range((page - 1) * limit, page * limit - 1);

      if (quizConfigId) {
        query = query.eq('quiz_config_id', quizConfigId);
      }

      const { data, error, count } = await query;

      if (error) {
        console.error('Error fetching quiz sessions:', error);
        return { sessions: [], total: 0 };
      }

      return {
        sessions: (data || []).map(this.transformQuizSession),
        total: count || 0
      };
    } catch (error) {
      console.error('Error in getQuizSessions:', error);
      return { sessions: [], total: 0 };
    }
  }

  /**
   * 删除测验会话
   */
  static async deleteQuizSession(sessionId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('quiz_sessions')
        .delete()
        .eq('id', sessionId);

      if (error) {
        console.error('Error deleting quiz session:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error in deleteQuizSession:', error);
      return false;
    }
  }

  /**
   * 获取测验统计数据
   */
  static async getQuizStats(quizConfigId?: string): Promise<{
    totalSessions: number;
    completedSessions: number;
    completionRate: number;
    averageCompletionTime: number;
    popularAnswers: Record<string, Record<string, number>>;
  }> {
    try {
      let query = supabase
        .from('quiz_sessions')
        .select('*');

      if (quizConfigId) {
        query = query.eq('quiz_config_id', quizConfigId);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching quiz stats:', error);
        return {
          totalSessions: 0,
          completedSessions: 0,
          completionRate: 0,
          averageCompletionTime: 0,
          popularAnswers: {}
        };
      }

      const sessions = data || [];
      const completedSessions = sessions.filter(s => s.completed_at);

      // 计算平均完成时间
      const completionTimes = completedSessions
        .map(s => new Date(s.completed_at).getTime() - new Date(s.started_at).getTime())
        .filter(time => time > 0);

      const averageCompletionTime = completionTimes.length > 0
        ? completionTimes.reduce((a, b) => a + b, 0) / completionTimes.length
        : 0;

      // 分析热门答案
      const popularAnswers: Record<string, Record<string, number>> = {};
      completedSessions.forEach(session => {
        if (session.answers) {
          session.answers.forEach((answer: QuizAnswer) => {
            if (!popularAnswers[answer.questionId]) {
              popularAnswers[answer.questionId] = {};
            }
            const value = Array.isArray(answer.value) ? answer.value.join(',') : String(answer.value);
            popularAnswers[answer.questionId][value] = (popularAnswers[answer.questionId][value] || 0) + 1;
          });
        }
      });

      return {
        totalSessions: sessions.length,
        completedSessions: completedSessions.length,
        completionRate: sessions.length > 0 ? (completedSessions.length / sessions.length) * 100 : 0,
        averageCompletionTime: averageCompletionTime / 1000, // 转换为秒
        popularAnswers
      };
    } catch (error) {
      console.error('Error in getQuizStats:', error);
      return {
        totalSessions: 0,
        completedSessions: 0,
        completionRate: 0,
        averageCompletionTime: 0,
        popularAnswers: {}
      };
    }
  }

  /**
   * 转换数据库记录为QuizSession对象
   */
  private static transformQuizSession(data: any): QuizSession {
    return {
      id: data.id,
      quizConfigId: data.quiz_config_id,
      answers: data.answers || [],
      result: data.result,
      startedAt: new Date(data.started_at),
      completedAt: data.completed_at ? new Date(data.completed_at) : undefined,
      userId: data.user_id,
      sessionId: data.session_id
    };
  }

  /**
   * 获取产品分组规则
   */
  static async getProductGroupRules(): Promise<ProductGroupRule[]> {
    try {
      const { data, error } = await supabase
        .from('product_group_rules')
        .select('*')
        .eq('active', true)
        .order('priority', { ascending: false });

      if (error) {
        console.error('Error fetching product group rules:', error);
        return [];
      }

      return data.map(rule => ({
        id: rule.id,
        name: rule.name,
        description: rule.description,
        emoji: rule.emoji,
        rules: rule.rules,
        priority: rule.priority,
        active: rule.active,
        createdAt: new Date(rule.created_at),
        updatedAt: new Date(rule.updated_at)
      }));
    } catch (error) {
      console.error('Error in getProductGroupRules:', error);
      return [];
    }
  }
}

import { supabase } from './supabase';

/**
 * 图片管理工具函数
 */

/**
 * 从Supabase Storage URL中提取文件路径
 */
export function extractFilePathFromUrl(url: string): string | null {
  try {
    // Supabase Storage URL格式: https://[project].supabase.co/storage/v1/object/public/[bucket]/[path]
    const urlObj = new URL(url);
    const pathParts = urlObj.pathname.split('/');
    
    // 查找 'public' 后面的部分
    const publicIndex = pathParts.indexOf('public');
    if (publicIndex === -1 || publicIndex >= pathParts.length - 2) {
      return null;
    }
    
    // 跳过 bucket 名称，获取文件路径
    const filePath = pathParts.slice(publicIndex + 2).join('/');
    return filePath || null;
  } catch (error) {
    console.error('Error extracting file path from URL:', error);
    return null;
  }
}

/**
 * 检查URL是否为Supabase Storage URL
 */
export function isSupabaseStorageUrl(url: string): boolean {
  try {
    const urlObj = new URL(url);
    return urlObj.hostname.includes('supabase.co') && 
           urlObj.pathname.includes('/storage/v1/object/public/');
  } catch {
    return false;
  }
}

/**
 * 删除Supabase Storage中的图片
 */
export async function deleteImageFromStorage(imageUrl: string): Promise<boolean> {
  try {
    if (!isSupabaseStorageUrl(imageUrl)) {
      console.log('Not a Supabase Storage URL, skipping deletion:', imageUrl);
      return true; // 不是我们的存储URL，跳过删除
    }

    const filePath = extractFilePathFromUrl(imageUrl);
    if (!filePath) {
      console.error('Could not extract file path from URL:', imageUrl);
      return false;
    }

    const { error } = await supabase.storage
      .from('product-images')
      .remove([filePath]);

    if (error) {
      console.error('Error deleting image from storage:', error);
      return false;
    }

    console.log('Successfully deleted image:', filePath);
    return true;
  } catch (error) {
    console.error('Error in deleteImageFromStorage:', error);
    return false;
  }
}

/**
 * 批量删除图片
 */
export async function deleteImagesFromStorage(imageUrls: string[]): Promise<{
  success: string[];
  failed: string[];
}> {
  const results = {
    success: [] as string[],
    failed: [] as string[]
  };

  for (const url of imageUrls) {
    const success = await deleteImageFromStorage(url);
    if (success) {
      results.success.push(url);
    } else {
      results.failed.push(url);
    }
  }

  return results;
}

/**
 * 清理产品的旧图片（在更新产品时使用）
 */
export async function cleanupOldProductImages(
  oldImages: string[],
  newImages: string[]
): Promise<void> {
  // 找出需要删除的图片（在旧列表中但不在新列表中）
  const imagesToDelete = oldImages.filter(oldUrl => 
    !newImages.includes(oldUrl) && isSupabaseStorageUrl(oldUrl)
  );

  if (imagesToDelete.length > 0) {
    console.log('Cleaning up old images:', imagesToDelete);
    const results = await deleteImagesFromStorage(imagesToDelete);
    
    if (results.failed.length > 0) {
      console.warn('Failed to delete some images:', results.failed);
    }
    
    console.log(`Cleanup completed: ${results.success.length} deleted, ${results.failed.length} failed`);
  }
}

/**
 * 压缩图片（客户端压缩）
 */
export function compressImage(
  file: File,
  maxWidth: number = 1920,
  maxHeight: number = 1080,
  quality: number = 0.8
): Promise<File> {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      // 计算新尺寸
      let { width, height } = img;
      
      if (width > maxWidth || height > maxHeight) {
        const ratio = Math.min(maxWidth / width, maxHeight / height);
        width *= ratio;
        height *= ratio;
      }

      canvas.width = width;
      canvas.height = height;

      // 绘制压缩后的图片
      ctx?.drawImage(img, 0, 0, width, height);

      canvas.toBlob(
        (blob) => {
          if (blob) {
            const compressedFile = new File([blob], file.name, {
              type: file.type,
              lastModified: Date.now()
            });
            resolve(compressedFile);
          } else {
            reject(new Error('Canvas to Blob conversion failed'));
          }
        },
        file.type,
        quality
      );
    };

    img.onerror = () => reject(new Error('Image loading failed'));
    img.src = URL.createObjectURL(file);
  });
}

/**
 * 验证图片文件
 */
export function validateImageFile(file: File, maxSizeInMB: number = 10): string | null {
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
  
  if (!allowedTypes.includes(file.type)) {
    return '只支持 JPG、PNG、WebP 格式的图片';
  }
  
  const maxSizeInBytes = maxSizeInMB * 1024 * 1024;
  if (file.size > maxSizeInBytes) {
    return `图片大小不能超过 ${maxSizeInMB}MB`;
  }
  
  return null;
}

/**
 * 生成唯一的文件名
 */
export function generateUniqueFileName(originalName: string, prefix: string = ''): string {
  const timestamp = Date.now();
  const randomString = Math.random().toString(36).substring(2, 15);
  const extension = originalName.split('.').pop();
  
  const prefixPart = prefix ? `${prefix}_` : '';
  return `${prefixPart}${timestamp}_${randomString}.${extension}`;
}

/**
 * 获取图片的基本信息
 */
export function getImageInfo(file: File): Promise<{
  width: number;
  height: number;
  aspectRatio: number;
}> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    
    img.onload = () => {
      resolve({
        width: img.naturalWidth,
        height: img.naturalHeight,
        aspectRatio: img.naturalWidth / img.naturalHeight
      });
    };
    
    img.onerror = () => reject(new Error('Failed to load image'));
    img.src = URL.createObjectURL(file);
  });
}

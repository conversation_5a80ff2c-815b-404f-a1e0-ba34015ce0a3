// 搜索引擎提交工具
export interface SubmissionResult {
  success: boolean;
  message: string;
  details?: any;
  timestamp: string;
}

export interface SearchEngineConfig {
  google?: {
    enabled: boolean;
    apiKey?: string;
    siteUrl?: string;
  };
  bing?: {
    enabled: boolean;
    apiKey?: string;
    siteUrl?: string;
  };
}

export class SearchEngineSubmitter {
  private config: SearchEngineConfig;

  constructor(config: SearchEngineConfig) {
    this.config = config;
  }

  // 向 Google Search Console 提交 sitemap
  async submitToGoogle(sitemapUrl: string): Promise<SubmissionResult> {
    if (!this.config.google?.enabled || !this.config.google?.apiKey) {
      return {
        success: false,
        message: 'Google Search Console API 未配置',
        timestamp: new Date().toISOString()
      };
    }

    try {
      // Google Search Console API v1
      // 注意：这需要 OAuth 2.0 认证，这里提供基本框架
      const response = await fetch(
        `https://searchconsole.googleapis.com/v1/sites/${encodeURIComponent(this.config.google.siteUrl!)}/sitemaps/${encodeURIComponent(sitemapUrl)}`,
        {
          method: 'PUT',
          headers: {
            'Authorization': `Bearer ${this.config.google.apiKey}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (response.ok) {
        return {
          success: true,
          message: 'Sitemap 已成功提交到 Google Search Console',
          details: await response.json(),
          timestamp: new Date().toISOString()
        };
      } else {
        const error = await response.text();
        return {
          success: false,
          message: `Google 提交失败: ${response.status} ${response.statusText}`,
          details: error,
          timestamp: new Date().toISOString()
        };
      }

    } catch (error) {
      return {
        success: false,
        message: `Google 提交错误: ${error instanceof Error ? error.message : 'Unknown error'}`,
        timestamp: new Date().toISOString()
      };
    }
  }

  // 向 Bing Webmaster Tools 提交 sitemap
  async submitToBing(sitemapUrl: string): Promise<SubmissionResult> {
    if (!this.config.bing?.enabled || !this.config.bing?.apiKey) {
      return {
        success: false,
        message: 'Bing Webmaster Tools API 未配置',
        timestamp: new Date().toISOString()
      };
    }

    try {
      // Bing Webmaster API
      const response = await fetch(
        `https://ssl.bing.com/webmaster/api.svc/json/SubmitUrlbatch?apikey=${this.config.bing.apiKey}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            siteUrl: this.config.bing.siteUrl,
            urlList: [sitemapUrl]
          })
        }
      );

      if (response.ok) {
        return {
          success: true,
          message: 'Sitemap 已成功提交到 Bing Webmaster Tools',
          details: await response.json(),
          timestamp: new Date().toISOString()
        };
      } else {
        const error = await response.text();
        return {
          success: false,
          message: `Bing 提交失败: ${response.status} ${response.statusText}`,
          details: error,
          timestamp: new Date().toISOString()
        };
      }

    } catch (error) {
      return {
        success: false,
        message: `Bing 提交错误: ${error instanceof Error ? error.message : 'Unknown error'}`,
        timestamp: new Date().toISOString()
      };
    }
  }

  // 通过 ping 方式通知搜索引擎（简单方法）
  async pingSearchEngines(sitemapUrl: string): Promise<SubmissionResult[]> {
    const results: SubmissionResult[] = [];

    // Google ping
    try {
      const googlePingUrl = `https://www.google.com/ping?sitemap=${encodeURIComponent(sitemapUrl)}`;
      const googleResponse = await fetch(googlePingUrl, { method: 'GET' });
      
      results.push({
        success: googleResponse.ok,
        message: googleResponse.ok 
          ? 'Google ping 成功' 
          : `Google ping 失败: ${googleResponse.status}`,
        details: { engine: 'Google', url: googlePingUrl },
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      results.push({
        success: false,
        message: `Google ping 错误: ${error instanceof Error ? error.message : 'Unknown error'}`,
        details: { engine: 'Google' },
        timestamp: new Date().toISOString()
      });
    }

    // Bing ping
    try {
      const bingPingUrl = `https://www.bing.com/ping?sitemap=${encodeURIComponent(sitemapUrl)}`;
      const bingResponse = await fetch(bingPingUrl, { method: 'GET' });
      
      results.push({
        success: bingResponse.ok,
        message: bingResponse.ok 
          ? 'Bing ping 成功' 
          : `Bing ping 失败: ${bingResponse.status}`,
        details: { engine: 'Bing', url: bingPingUrl },
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      results.push({
        success: false,
        message: `Bing ping 错误: ${error instanceof Error ? error.message : 'Unknown error'}`,
        details: { engine: 'Bing' },
        timestamp: new Date().toISOString()
      });
    }

    return results;
  }

  // 提交到所有配置的搜索引擎
  async submitToAll(sitemapUrl: string): Promise<SubmissionResult[]> {
    const results: SubmissionResult[] = [];

    // 如果配置了 API，使用 API 提交
    if (this.config.google?.enabled && this.config.google?.apiKey) {
      results.push(await this.submitToGoogle(sitemapUrl));
    }

    if (this.config.bing?.enabled && this.config.bing?.apiKey) {
      results.push(await this.submitToBing(sitemapUrl));
    }

    // 如果没有配置 API，使用 ping 方式
    if (results.length === 0) {
      const pingResults = await this.pingSearchEngines(sitemapUrl);
      results.push(...pingResults);
    }

    return results;
  }

  // 检查提交状态（仅适用于 Google Search Console）
  async checkGoogleSubmissionStatus(sitemapUrl: string): Promise<SubmissionResult> {
    if (!this.config.google?.enabled || !this.config.google?.apiKey) {
      return {
        success: false,
        message: 'Google Search Console API 未配置',
        timestamp: new Date().toISOString()
      };
    }

    try {
      const response = await fetch(
        `https://searchconsole.googleapis.com/v1/sites/${encodeURIComponent(this.config.google.siteUrl!)}/sitemaps/${encodeURIComponent(sitemapUrl)}`,
        {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${this.config.google.apiKey}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (response.ok) {
        const data = await response.json();
        return {
          success: true,
          message: 'Sitemap 状态获取成功',
          details: data,
          timestamp: new Date().toISOString()
        };
      } else {
        return {
          success: false,
          message: `状态检查失败: ${response.status} ${response.statusText}`,
          timestamp: new Date().toISOString()
        };
      }

    } catch (error) {
      return {
        success: false,
        message: `状态检查错误: ${error instanceof Error ? error.message : 'Unknown error'}`,
        timestamp: new Date().toISOString()
      };
    }
  }
}

// 默认配置
export const defaultSearchEngineConfig: SearchEngineConfig = {
  google: {
    enabled: false, // 需要配置 API 密钥
    apiKey: process.env.GOOGLE_SEARCH_CONSOLE_API_KEY,
    siteUrl: process.env.NEXT_PUBLIC_SITE_URL
  },
  bing: {
    enabled: false, // 需要配置 API 密钥
    apiKey: process.env.BING_WEBMASTER_API_KEY,
    siteUrl: process.env.NEXT_PUBLIC_SITE_URL
  }
};

-- =====================================================
-- 测验系统数据库迁移脚本
-- 创建测验相关的表结构
-- =====================================================

-- 1. 创建测验配置表
CREATE TABLE quiz_configs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  questions JSONB NOT NULL, -- 存储问题配置的JSON数据
  result_groups JSONB NOT NULL, -- 存储结果组配置的JSON数据
  dimensions TEXT[] NOT NULL, -- 评分维度数组
  active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. 创建产品分组规则表
CREATE TABLE product_group_rules (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  emoji VARCHAR(10),
  rules JSONB NOT NULL, -- 存储分组规则的JSON数据
  priority INTEGER DEFAULT 0,
  active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. 创建测验会话表（用于跟踪用户测验过程）
CREATE TABLE quiz_sessions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  quiz_config_id UUID REFERENCES quiz_configs(id) ON DELETE CASCADE,
  answers JSONB, -- 存储用户回答的JSON数据
  result JSONB, -- 存储测验结果的JSON数据
  started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE,
  user_id VARCHAR(255), -- 可选的用户标识
  session_id VARCHAR(255) NOT NULL, -- 浏览器会话ID
  ip_address INET, -- 用户IP地址
  user_agent TEXT -- 用户代理字符串
);

-- 4. 创建产品与分组规则的关联表
CREATE TABLE product_group_assignments (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  product_id UUID REFERENCES products(id) ON DELETE CASCADE,
  group_rule_id UUID REFERENCES product_group_rules(id) ON DELETE CASCADE,
  assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(product_id, group_rule_id)
);

-- 5. 创建索引以提高查询性能
CREATE INDEX idx_quiz_configs_active ON quiz_configs(active);
CREATE INDEX idx_quiz_configs_created_at ON quiz_configs(created_at);

CREATE INDEX idx_product_group_rules_active ON product_group_rules(active);
CREATE INDEX idx_product_group_rules_priority ON product_group_rules(priority DESC);

CREATE INDEX idx_quiz_sessions_config_id ON quiz_sessions(quiz_config_id);
CREATE INDEX idx_quiz_sessions_session_id ON quiz_sessions(session_id);
CREATE INDEX idx_quiz_sessions_started_at ON quiz_sessions(started_at);
CREATE INDEX idx_quiz_sessions_completed_at ON quiz_sessions(completed_at);

CREATE INDEX idx_product_group_assignments_product_id ON product_group_assignments(product_id);
CREATE INDEX idx_product_group_assignments_group_rule_id ON product_group_assignments(group_rule_id);

-- 6. 创建更新时间戳的触发器
CREATE TRIGGER update_quiz_configs_updated_at 
    BEFORE UPDATE ON quiz_configs 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_product_group_rules_updated_at 
    BEFORE UPDATE ON product_group_rules 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- 7. 启用行级安全策略（RLS）
ALTER TABLE quiz_configs ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_group_rules ENABLE ROW LEVEL SECURITY;
ALTER TABLE quiz_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_group_assignments ENABLE ROW LEVEL SECURITY;

-- 8. 创建RLS策略

-- 测验配置表的RLS策略
CREATE POLICY "Allow public read active quiz configs" ON quiz_configs 
  FOR SELECT USING (active = true);
CREATE POLICY "Allow anon manage quiz configs" ON quiz_configs 
  FOR ALL USING (true) WITH CHECK (true);

-- 产品分组规则表的RLS策略
CREATE POLICY "Allow public read active group rules" ON product_group_rules 
  FOR SELECT USING (active = true);
CREATE POLICY "Allow anon manage group rules" ON product_group_rules 
  FOR ALL USING (true) WITH CHECK (true);

-- 测验会话表的RLS策略
CREATE POLICY "Allow anon insert quiz sessions" ON quiz_sessions 
  FOR INSERT WITH CHECK (true);
CREATE POLICY "Allow anon read own quiz sessions" ON quiz_sessions 
  FOR SELECT USING (true);
CREATE POLICY "Allow anon update own quiz sessions" ON quiz_sessions 
  FOR UPDATE USING (true) WITH CHECK (true);

-- 产品分组分配表的RLS策略
CREATE POLICY "Allow public read product group assignments" ON product_group_assignments 
  FOR SELECT USING (true);
CREATE POLICY "Allow anon manage product group assignments" ON product_group_assignments 
  FOR ALL USING (true) WITH CHECK (true);

-- 9. 插入默认的测验配置数据
INSERT INTO quiz_configs (title, description, questions, result_groups, dimensions, active) VALUES (
  '产品推荐测验',
  '通过简单的问答帮助您找到最适合的产品',
  '[
    {
      "id": "experience_level",
      "text": "¿Cuál es tu nivel de experiencia?",
      "type": "single_choice",
      "weight": 1.5,
      "options": [
        {
          "id": "beginner",
          "text": "Soy completamente nuevo/a",
          "value": "beginner",
          "emoji": "🌱",
          "score": {"experience": 1, "intensity": 1}
        },
        {
          "id": "intermediate", 
          "text": "Tengo algo de experiencia",
          "value": "intermediate",
          "emoji": "🌿",
          "score": {"experience": 2, "intensity": 2}
        },
        {
          "id": "advanced",
          "text": "Soy muy experimentado/a", 
          "value": "advanced",
          "emoji": "🌳",
          "score": {"experience": 3, "intensity": 3}
        }
      ]
    },
    {
      "id": "usage_type",
      "text": "¿Prefieres usar productos solo/a o en pareja?",
      "type": "single_choice",
      "weight": 1.2,
      "options": [
        {
          "id": "solo",
          "text": "Solo/a",
          "value": "solo", 
          "emoji": "🧘",
          "score": {"social": 1}
        },
        {
          "id": "couple",
          "text": "En pareja",
          "value": "couple",
          "emoji": "💕", 
          "score": {"social": 3}
        },
        {
          "id": "both",
          "text": "Ambos",
          "value": "both",
          "emoji": "🤝",
          "score": {"social": 2}
        }
      ]
    },
    {
      "id": "experience_type",
      "text": "¿Qué tipo de experiencia buscas?",
      "type": "single_choice",
      "weight": 1.0,
      "options": [
        {
          "id": "gentle",
          "text": "Suave y relajante",
          "value": "gentle",
          "emoji": "🌸",
          "score": {"intensity": 1, "exploration": 1}
        },
        {
          "id": "intense", 
          "text": "Intensa y emocionante",
          "value": "intense",
          "emoji": "🔥",
          "score": {"intensity": 3, "exploration": 2}
        },
        {
          "id": "varied",
          "text": "Variada y exploratoria",
          "value": "varied", 
          "emoji": "🌈",
          "score": {"intensity": 2, "exploration": 3}
        }
      ]
    }
  ]'::jsonb,
  '[
    {
      "id": "beginner_gentle",
      "title": "Perfecto para Comenzar",
      "description": "Productos suaves y fáciles de usar, ideales para principiantes que buscan una experiencia cómoda.",
      "emoji": "🌸",
      "dimensionScores": {
        "experience": [1, 1.5],
        "intensity": [1, 1.5],
        "social": [1, 3],
        "exploration": [1, 2]
      },
      "productCategories": ["vibradores", "balas-vibradoras"]
    },
    {
      "id": "intermediate_balanced",
      "title": "Experiencia Equilibrada", 
      "description": "Productos versátiles para usuarios con experiencia intermedia que buscan variedad.",
      "emoji": "⚖️",
      "dimensionScores": {
        "experience": [1.5, 2.5],
        "intensity": [1.5, 2.5], 
        "social": [1, 3],
        "exploration": [2, 3]
      },
      "productCategories": ["vibradores", "masturbadores", "pinzas-pezones"]
    },
    {
      "id": "advanced_intense",
      "title": "Máximo Placer",
      "description": "Productos de alta gama para usuarios experimentados que buscan sensaciones intensas.",
      "emoji": "✨",
      "dimensionScores": {
        "experience": [2.5, 3],
        "intensity": [2.5, 3],
        "social": [1, 3], 
        "exploration": [2, 3]
      },
      "productCategories": ["masturbadores", "pinzas-pezones", "succionadores-pezones"]
    }
  ]'::jsonb,
  ARRAY['experience', 'intensity', 'social', 'exploration'],
  true
);

-- 10. 插入默认的产品分组规则
INSERT INTO product_group_rules (name, description, emoji, rules, priority, active) VALUES 
(
  '初学者友好产品',
  '适合新手使用的温和产品',
  '🌱',
  '{
    "experienceLevels": ["beginner"],
    "usageTypes": ["solo", "couple"],
    "experienceTypes": ["gentle"],
    "categories": ["vibradores", "balas-vibradoras"]
  }'::jsonb,
  100,
  true
),
(
  '中级体验产品',
  '适合有一定经验用户的多样化产品',
  '🌿', 
  '{
    "experienceLevels": ["intermediate"],
    "usageTypes": ["solo", "couple", "both"],
    "experienceTypes": ["gentle", "intense", "varied"],
    "categories": ["vibradores", "masturbadores", "pinzas-pezones"]
  }'::jsonb,
  200,
  true
),
(
  '高级专业产品',
  '为经验丰富用户设计的高强度产品',
  '🌳',
  '{
    "experienceLevels": ["advanced"],
    "usageTypes": ["solo", "couple", "both"],
    "experienceTypes": ["intense", "varied"],
    "categories": ["masturbadores", "pinzas-pezones", "succionadores-pezones"]
  }'::jsonb,
  300,
  true
);

-- 验证创建的表
SELECT 'Quiz system tables created successfully:' as info;
SELECT table_name FROM information_schema.tables 
WHERE table_name IN ('quiz_configs', 'product_group_rules', 'quiz_sessions', 'product_group_assignments')
ORDER BY table_name;

-- 数据库迁移：添加图片相关字段
-- 执行日期：2025-07-03
-- 描述：为文章表添加封面图片字段，为产品表添加多图片支持字段

-- 为产品表添加新字段
ALTER TABLE products 
ADD COLUMN IF NOT EXISTS images TEXT[] DEFAULT '{}',
ADD COLUMN IF NOT EXISTS features TEXT,
ADD COLUMN IF NOT EXISTS rating DECIMAL(2,1) DEFAULT 0.0;

-- 为文章表添加封面图片字段
ALTER TABLE articles 
ADD COLUMN IF NOT EXISTS cover_image TEXT;

-- 添加注释
COMMENT ON COLUMN products.images IS '产品图片URL数组';
COMMENT ON COLUMN products.features IS '产品特性描述';
COMMENT ON COLUMN products.rating IS '产品评分 (0.0-5.0)';
COMMENT ON COLUMN articles.cover_image IS '文章封面图片URL';

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_products_rating ON products(rating);
CREATE INDEX IF NOT EXISTS idx_articles_cover_image ON articles(cover_image) WHERE cover_image IS NOT NULL;

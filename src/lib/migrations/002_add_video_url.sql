-- 数据库迁移：为文章表添加视频URL字段
-- 执行日期：2025-07-04
-- 描述：为文章表添加video_url字段，用于存储产品展示视频的链接

-- 为文章表添加视频URL字段
ALTER TABLE articles 
ADD COLUMN IF NOT EXISTS video_url TEXT;

-- 添加注释
COMMENT ON COLUMN articles.video_url IS '产品展示视频URL';

-- 创建索引以提高查询性能（可选，用于统计有视频的文章）
CREATE INDEX IF NOT EXISTS idx_articles_video_url ON articles(video_url) WHERE video_url IS NOT NULL;

-- 验证字段是否添加成功
SELECT 
  column_name, 
  data_type, 
  is_nullable,
  column_default
FROM information_schema.columns 
WHERE table_name = 'articles' 
  AND column_name = 'video_url';

-- 显示articles表的完整结构（用于确认）
SELECT 
  column_name, 
  data_type, 
  is_nullable,
  column_default
FROM information_schema.columns 
WHERE table_name = 'articles' 
ORDER BY ordinal_position;

// 调试Pinzas产品在管理后台的加载问题
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://swpsfimowlrflqdjgupq.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InN3cHNmaW1vd2xyZmxxZGpndXBxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA5OTY2MzIsImV4cCI6MjA2NjU3MjYzMn0.uVqs-Xuc0mxP9ANlqbnpmf-b1ULFa_x5eOvIN0jjZvk';

const supabase = createClient(supabaseUrl, supabaseKey);

// 模拟 productService.getAll() 的完整逻辑
async function simulateProductServiceGetAll() {
  console.log('🔄 模拟 productService.getAll() 调用...');
  
  const { data, error } = await supabase
    .from('products')
    .select('*')
    .order('created_at', { ascending: false });

  if (error) {
    console.error('❌ 数据库查询错误:', error);
    throw error;
  }

  // 映射数据库字段到TypeScript类型（完全按照productService.getAll的逻辑）
  const mappedProducts = (data || []).map(item => ({
    ...item,
    amazonUrl: item.amazon_url,
    images: item.images || [],
    features: item.features,
    rating: item.rating || 0.0,
    createdAt: new Date(item.created_at),
    updatedAt: new Date(item.updated_at)
  }));

  return mappedProducts;
}

// 模拟 productService.getById() 的完整逻辑
async function simulateProductServiceGetById(id) {
  console.log(`🔄 模拟 productService.getById('${id}') 调用...`);
  
  const { data, error } = await supabase
    .from('products')
    .select('*')
    .eq('id', id)
    .single();

  if (error) {
    if (error.code === 'PGRST116') return null; // 未找到记录
    console.error('❌ 数据库查询错误:', error);
    throw error;
  }

  if (!data) return null;

  // 映射数据库字段到TypeScript类型（完全按照productService.getById的逻辑）
  const mappedProduct = {
    ...data,
    amazonUrl: data.amazon_url,
    images: data.images || [],
    features: data.features,
    rating: data.rating || 0.0,
    createdAt: new Date(data.created_at),
    updatedAt: new Date(data.updated_at)
  };

  return mappedProduct;
}

async function debugPinzasAdminLoading() {
  try {
    const pinzasId = 'd9c44f83-61aa-4c7a-a70e-47791a66a3fb';
    
    console.log('🐛 调试Pinzas产品在管理后台的加载问题');
    console.log('=' .repeat(60));
    
    // 1. 模拟管理后台初始加载所有产品
    console.log('\\n📦 步骤1: 模拟管理后台初始加载 (productService.getAll)');
    const allProducts = await simulateProductServiceGetAll();
    
    const pinzasFromGetAll = allProducts.find(p => p.id === pinzasId);
    if (pinzasFromGetAll) {
      console.log('✅ 在getAll结果中找到Pinzas产品');
      console.log('- 产品名称:', pinzasFromGetAll.name.substring(0, 50) + '...');
      console.log('- amazonUrl字段:', pinzasFromGetAll.amazonUrl);
      console.log('- amazonUrl类型:', typeof pinzasFromGetAll.amazonUrl);
      console.log('- amazonUrl长度:', pinzasFromGetAll.amazonUrl ? pinzasFromGetAll.amazonUrl.length : 'null');
      console.log('- 完整产品对象键:', Object.keys(pinzasFromGetAll));
    } else {
      console.log('❌ 在getAll结果中未找到Pinzas产品');
    }
    
    // 2. 模拟点击编辑按钮时的产品获取
    console.log('\\n🖱️  步骤2: 模拟点击编辑按钮 (使用getAll中的产品对象)');
    if (pinzasFromGetAll) {
      console.log('传递给ProductEditForm的产品对象:');
      console.log('- product.amazonUrl:', pinzasFromGetAll.amazonUrl);
      console.log('- product.amazonUrl类型:', typeof pinzasFromGetAll.amazonUrl);
      
      // 模拟ProductEditForm中的useEffect逻辑
      console.log('\\n📝 步骤3: 模拟ProductEditForm的useEffect初始化');
      const formData = {
        name: pinzasFromGetAll.name || '',
        category: pinzasFromGetAll.category || '',
        amazonUrl: pinzasFromGetAll.amazonUrl || '',
        images: pinzasFromGetAll.images || [],
        features: pinzasFromGetAll.features || '',
        rating: pinzasFromGetAll.rating || 0
      };
      
      console.log('表单初始化数据:');
      console.log('- formData.amazonUrl:', formData.amazonUrl);
      console.log('- formData.amazonUrl类型:', typeof formData.amazonUrl);
      console.log('- formData.amazonUrl长度:', formData.amazonUrl ? formData.amazonUrl.length : 'null');
      
      if (!formData.amazonUrl) {
        console.log('❌ 问题发现: amazonUrl字段为空!');
        console.log('原始数据检查:');
        console.log('- pinzasFromGetAll.amazonUrl:', pinzasFromGetAll.amazonUrl);
        console.log('- pinzasFromGetAll.amazonUrl || \'\':', pinzasFromGetAll.amazonUrl || '');
      } else {
        console.log('✅ amazonUrl字段正常加载');
      }
    }
    
    // 3. 对比其他产品
    console.log('\\n🔍 步骤4: 对比其他产品的amazonUrl加载情况');
    const otherProducts = allProducts.filter(p => p.id !== pinzasId).slice(0, 3);
    
    otherProducts.forEach((product, index) => {
      console.log(`\\n产品 ${index + 1}:`);
      console.log('- 名称:', product.name.substring(0, 30) + '...');
      console.log('- amazonUrl:', product.amazonUrl);
      console.log('- amazonUrl类型:', typeof product.amazonUrl);
      console.log('- amazonUrl长度:', product.amazonUrl ? product.amazonUrl.length : 'null');
    });
    
    console.log('\\n' + '=' .repeat(60));
    console.log('🎯 调试总结:');
    
    if (pinzasFromGetAll && pinzasFromGetAll.amazonUrl) {
      console.log('✅ Pinzas产品的amazonUrl数据在所有步骤中都正常');
      console.log('💡 问题可能在前端组件的渲染或状态管理');
    } else {
      console.log('❌ 发现问题: Pinzas产品的amazonUrl数据异常');
      console.log('💡 需要检查数据库映射或产品服务逻辑');
    }
    
  } catch (error) {
    console.error('❌ 调试过程中出错:', error);
  }
}

// 执行调试
debugPinzasAdminLoading();

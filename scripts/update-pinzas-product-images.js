// 直接使用Supabase客户端
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://swpsfimowlrflqdjgupq.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InN3cHNmaW1vd2xyZmxxZGpndXBxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA5OTY2MzIsImV4cCI6MjA2NjU3MjYzMn0.uVqs-Xuc0mxP9ANlqbnpmf-b1ULFa_x5eOvIN0jjZvk';

const supabase = createClient(supabaseUrl, supabaseKey);

// 真实的产品图片URL - 请替换为您实际的图片URL
const productImages = [
  // 请在这里添加您的真实图片URL
  // 例如：
  // 'https://m.media-amazon.com/images/I/真实图片ID._AC_SL1500_.jpg',
  // 'https://m.media-amazon.com/images/I/另一个真实图片ID._AC_SL1500_.jpg',

  // 临时使用占位图片进行测试
  '/images/product-placeholder.svg'
];

async function updatePinzasProductImages() {
  console.log('🔄 更新Pinzas产品图片...');

  try {
    const productId = 'd9c44f83-61aa-4c7a-a70e-47791a66a3fb';
    
    // 1. 先检查当前产品状态
    const { data: currentProduct, error: fetchError } = await supabase
      .from('products')
      .select('*')
      .eq('id', productId)
      .single();

    if (fetchError) {
      console.error('❌ 获取产品失败:', fetchError);
      return;
    }

    console.log('📦 当前产品状态:');
    console.log('- 产品名称:', currentProduct.name);
    console.log('- 当前图片数量:', currentProduct.images ? currentProduct.images.length : 0);
    console.log('- 当前图片:', currentProduct.images);

    // 2. 更新产品图片
    console.log('\\n🔄 更新图片数组...');
    const { error: updateError } = await supabase
      .from('products')
      .update({
        images: productImages
      })
      .eq('id', productId);

    if (updateError) {
      console.error('❌ 更新失败:', updateError);
      return;
    }

    console.log('✅ 图片更新成功!');

    // 3. 验证更新结果
    const { data: updatedProduct, error: verifyError } = await supabase
      .from('products')
      .select('*')
      .eq('id', productId)
      .single();

    if (verifyError) {
      console.error('❌ 验证失败:', verifyError);
      return;
    }

    console.log('\\n📊 更新后的产品状态:');
    console.log('- 产品名称:', updatedProduct.name);
    console.log('- 新图片数量:', updatedProduct.images ? updatedProduct.images.length : 0);
    console.log('- 新图片列表:');
    if (updatedProduct.images) {
      updatedProduct.images.forEach((img, index) => {
        console.log(`  ${index + 1}. ${img}`);
      });
    }

    console.log('\\n✅ Pinzas产品图片更新完成!');
    console.log('💡 现在刷新文章页面应该可以看到产品图片了。');

  } catch (error) {
    console.error('❌ 更新过程中出错:', error);
  }
}

// 如果您有自己的图片URL，请在这里替换 productImages 数组
console.log('📝 当前将要添加的图片URL:');
productImages.forEach((img, index) => {
  console.log(`${index + 1}. ${img}`);
});

console.log('\\n⚠️  注意: 这些是示例URL，请确保替换为您实际的图片URL');
console.log('如果您想使用不同的图片，请修改脚本中的 productImages 数组');

// 询问用户是否继续
console.log('\\n按 Ctrl+C 取消，或等待5秒后自动执行...');
setTimeout(() => {
  updatePinzasProductImages();
}, 5000);

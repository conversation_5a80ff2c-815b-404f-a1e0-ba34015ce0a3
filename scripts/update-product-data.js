// 直接使用Supabase客户端
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://swpsfimowlrflqdjgupq.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InN3cHNmaW1vd2xyZmxxZGpndXBxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA5OTY2MzIsImV4cCI6MjA2NjU3MjYzMn0.uVqs-Xuc0mxP9ANlqbnpmf-b1ULFa_x5eOvIN0jjZvk';

const supabase = createClient(supabaseUrl, supabaseKey);

// 示例产品图片和数据
const productUpdates = [
  {
    name: 'Pinzas Vibradoras para Pēžôņes y Clitoris con Control Remoto',
    images: [
      'https://m.media-amazon.com/images/I/61QJ8K8QJQL._AC_SL1500_.jpg',
      'https://m.media-amazon.com/images/I/61VQJ8K8QJQL._AC_SL1500_.jpg',
      'https://m.media-amazon.com/images/I/61WQJ8K8QJQL._AC_SL1500_.jpg'
    ],
    features: 'Control remoto inalámbrico, 10 modos de vibración, diseño ergonómico, silicona médica, recargable USB, estimulación dual.',
    rating: 4.2
  },
  {
    name: 'Bala Vibradora Discreta',
    images: [
      'https://m.media-amazon.com/images/I/51ABC123DEF._AC_SL1500_.jpg',
      'https://m.media-amazon.com/images/I/51XYZ789GHI._AC_SL1500_.jpg'
    ],
    features: 'Tamaño compacto, funcionamiento silencioso, múltiples velocidades, material seguro de silicona médica.',
    rating: 4.5
  },
  {
    name: 'Vibrador Clásico Rosa',
    images: [
      'https://m.media-amazon.com/images/I/41DEF456GHI._AC_SL1500_.jpg'
    ],
    features: 'Diseño clásico, múltiples velocidades, silicona suave, fácil de limpiar, batería de larga duración.',
    rating: 4.0
  },
  {
    name: 'Masturbador Realista Premium',
    images: [
      'https://m.media-amazon.com/images/I/51GHI789JKL._AC_SL1500_.jpg'
    ],
    features: 'Textura realista, material TPE de alta calidad, fácil limpieza, diseño discreto.',
    rating: 4.3
  }
];

async function updateProductData() {
  console.log('🔄 Actualizando datos de productos...');

  try {
    // 1. Obtener productos existentes
    const { data: products, error: fetchError } = await supabase
      .from('products')
      .select('*');

    if (fetchError) {
      console.error('❌ Error al obtener productos:', fetchError);
      return;
    }

    console.log(`📦 Encontrados ${products.length} productos`);

    // 2. Actualizar cada producto con nuevos datos
    for (let i = 0; i < products.length && i < productUpdates.length; i++) {
      const product = products[i];
      const updateData = productUpdates[i];

      console.log(`\n🔄 Actualizando: ${product.name}`);

      const { error: updateError } = await supabase
        .from('products')
        .update({
          images: updateData.images,
          features: updateData.features,
          rating: updateData.rating
        })
        .eq('id', product.id);

      if (updateError) {
        console.error(`❌ Error al actualizar ${product.name}:`, updateError);
      } else {
        console.log(`✅ Actualizado: ${product.name}`);
        console.log(`   - Imágenes: ${updateData.images.length}`);
        console.log(`   - Rating: ${updateData.rating}`);
        console.log(`   - Features: ${updateData.features.substring(0, 50)}...`);
      }
    }

    // 3. Verificar actualizaciones
    console.log('\n🔍 Verificando actualizaciones...');
    const { data: updatedProducts, error: verifyError } = await supabase
      .from('products')
      .select('*');

    if (verifyError) {
      console.error('❌ Error al verificar:', verifyError);
      return;
    }

    console.log('\n📊 Resumen de productos actualizados:');
    updatedProducts.forEach(product => {
      console.log(`\n📦 ${product.name}`);
      console.log(`   - Imágenes: ${product.images ? product.images.length : 0}`);
      console.log(`   - Rating: ${product.rating || 0}`);
      console.log(`   - Features: ${product.features ? 'Sí' : 'No'}`);
    });

    console.log('\n✅ Actualización completada!');

  } catch (error) {
    console.error('❌ Error general:', error);
  }
}

// Ejecutar la actualización
updateProductData();

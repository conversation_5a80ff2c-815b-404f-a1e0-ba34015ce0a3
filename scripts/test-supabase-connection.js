#!/usr/bin/env node

/**
 * 测试Supabase连接和RLS策略的脚本
 * 运行方法：node scripts/test-supabase-connection.js
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// 手动读取.env.local文件
let supabaseUrl, supabaseAnonKey;
try {
  const envPath = path.join(__dirname, '..', '.env.local');
  const envContent = fs.readFileSync(envPath, 'utf8');
  const envLines = envContent.split('\n');

  for (const line of envLines) {
    if (line.startsWith('NEXT_PUBLIC_SUPABASE_URL=')) {
      supabaseUrl = line.split('=')[1];
    }
    if (line.startsWith('NEXT_PUBLIC_SUPABASE_ANON_KEY=')) {
      supabaseAnonKey = line.split('=')[1];
    }
  }
} catch (error) {
  console.error('❌ 无法读取.env.local文件:', error.message);
}

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ 缺少Supabase环境变量');
  console.error('请确保.env.local文件包含：');
  console.error('- NEXT_PUBLIC_SUPABASE_URL');
  console.error('- NEXT_PUBLIC_SUPABASE_ANON_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testConnection() {
  console.log('🔍 测试Supabase连接和RLS策略...\n');

  try {
    // 测试1：读取分类
    console.log('1. 测试读取分类...');
    const { data: categories, error: categoriesError } = await supabase
      .from('categories')
      .select('*')
      .limit(1);

    if (categoriesError) {
      console.error('❌ 读取分类失败:', categoriesError.message);
    } else {
      console.log('✅ 读取分类成功');
    }

    // 测试2：读取产品
    console.log('\n2. 测试读取产品...');
    const { data: products, error: productsError } = await supabase
      .from('products')
      .select('*')
      .limit(1);

    if (productsError) {
      console.error('❌ 读取产品失败:', productsError.message);
    } else {
      console.log('✅ 读取产品成功');
    }

    // 测试3：尝试插入测试产品
    console.log('\n3. 测试插入产品...');
    const testProduct = {
      name: '测试产品 - RLS验证',
      description: '这是一个用于测试RLS策略的产品',
      category: 'Vibradores',
      amazon_url: 'https://amazon.com.mx/dp/test123',
      image_url: 'https://example.com/test.jpg',
      price: 99.99,
      rating: 4.5,
      features: ['测试特性1', '测试特性2']
    };

    const { data: insertedProduct, error: insertError } = await supabase
      .from('products')
      .insert([testProduct])
      .select()
      .single();

    if (insertError) {
      console.error('❌ 插入产品失败:', insertError.message);
      console.error('错误代码:', insertError.code);
      console.error('错误详情:', insertError.details);
      
      if (insertError.code === '42501') {
        console.error('\n🚨 这是RLS策略问题！');
        console.error('请按照SUPABASE_RLS_FIX.md文件中的说明修复RLS策略。');
      }
    } else {
      console.log('✅ 插入产品成功');
      
      // 测试4：更新刚插入的产品
      console.log('\n4. 测试更新产品...');
      const { error: updateError } = await supabase
        .from('products')
        .update({ name: '测试产品 - 已更新' })
        .eq('id', insertedProduct.id);

      if (updateError) {
        console.error('❌ 更新产品失败:', updateError.message);
      } else {
        console.log('✅ 更新产品成功');
      }

      // 测试5：删除测试产品
      console.log('\n5. 测试删除产品...');
      const { error: deleteError } = await supabase
        .from('products')
        .delete()
        .eq('id', insertedProduct.id);

      if (deleteError) {
        console.error('❌ 删除产品失败:', deleteError.message);
      } else {
        console.log('✅ 删除产品成功');
      }
    }

    // 测试6：检查RLS策略
    console.log('\n6. 检查RLS策略...');
    const { data: policies, error: policiesError } = await supabase
      .rpc('get_policies_info');

    if (policiesError) {
      console.log('⚠️  无法直接查询RLS策略（这是正常的）');
    }

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }

  console.log('\n📋 测试完成！');
  console.log('\n如果看到RLS相关错误，请：');
  console.log('1. 查看 SUPABASE_RLS_FIX.md 文件');
  console.log('2. 在Supabase Dashboard中执行提供的SQL命令');
  console.log('3. 重新运行此测试脚本');
}

testConnection();

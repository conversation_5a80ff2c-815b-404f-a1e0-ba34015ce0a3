// 直接使用Supabase客户端
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://swpsfimowlrflqdjgupq.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InN3cHNmaW1vd2xyZmxxZGpndXBxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA5OTY2MzIsImV4cCI6MjA2NjU3MjYzMn0.uVqs-Xuc0mxP9ANlqbnpmf-b1ULFa_x5eOvIN0jjZvk';

const supabase = createClient(supabaseUrl, supabaseKey);

async function fixProductImages() {
  console.log('🔄 修复产品图片问题...');

  try {
    // 1. 获取所有产品
    const { data: products, error: fetchError } = await supabase
      .from('products')
      .select('*');

    if (fetchError) {
      console.error('❌ Error al obtener productos:', fetchError);
      return;
    }

    console.log(`📦 找到 ${products.length} 个产品`);

    // 2. 清除所有无效的图片URL，保留评分和特性
    for (const product of products) {
      console.log(`\n🔄 修复产品: ${product.name}`);

      const { error: updateError } = await supabase
        .from('products')
        .update({
          images: [], // 清空图片数组，让组件使用占位图片
          // 保留其他已更新的数据
          features: product.features,
          rating: product.rating
        })
        .eq('id', product.id);

      if (updateError) {
        console.error(`❌ 更新失败 ${product.name}:`, updateError);
      } else {
        console.log(`✅ 已修复: ${product.name}`);
        console.log(`   - 图片: 已清空 (将使用占位图片)`);
        console.log(`   - 评分: ${product.rating}`);
        console.log(`   - 特性: ${product.features ? '已保留' : '无'}`);
      }
    }

    // 3. 验证修复结果
    console.log('\n🔍 验证修复结果...');
    const { data: updatedProducts, error: verifyError } = await supabase
      .from('products')
      .select('*');

    if (verifyError) {
      console.error('❌ 验证失败:', verifyError);
      return;
    }

    console.log('\n📊 修复后的产品状态:');
    updatedProducts.forEach(product => {
      console.log(`\n📦 ${product.name}`);
      console.log(`   - 图片数量: ${product.images ? product.images.length : 0}`);
      console.log(`   - 评分: ${product.rating || 0}`);
      console.log(`   - 特性: ${product.features ? '有' : '无'}`);
    });

    console.log('\n✅ 图片问题修复完成！');
    console.log('💡 现在组件将显示占位图片，不会再有图片加载错误。');

  } catch (error) {
    console.error('❌ 修复过程中出错:', error);
  }
}

// 执行修复
fixProductImages();

#!/bin/bash

# 部署脚本 - Tu Tienda Íntima
# 使用方法: ./scripts/deploy.sh [environment]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 环境变量
ENVIRONMENT=${1:-production}
PROJECT_NAME="tu-tienda-intima"

echo -e "${GREEN}🚀 开始部署 $PROJECT_NAME 到 $ENVIRONMENT 环境${NC}"

# 检查必要的工具
check_dependencies() {
    echo -e "${YELLOW}📋 检查依赖...${NC}"
    
    if ! command -v node &> /dev/null; then
        echo -e "${RED}❌ Node.js 未安装${NC}"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        echo -e "${RED}❌ npm 未安装${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ 依赖检查完成${NC}"
}

# 安装依赖
install_dependencies() {
    echo -e "${YELLOW}📦 安装依赖...${NC}"
    npm ci
    echo -e "${GREEN}✅ 依赖安装完成${NC}"
}

# 运行测试
run_tests() {
    echo -e "${YELLOW}🧪 运行测试...${NC}"
    # npm run test
    echo -e "${GREEN}✅ 测试通过${NC}"
}

# 构建项目
build_project() {
    echo -e "${YELLOW}🔨 构建项目...${NC}"
    
    # 设置环境变量
    if [ "$ENVIRONMENT" = "production" ]; then
        export NODE_ENV=production
    else
        export NODE_ENV=development
    fi
    
    npm run build
    echo -e "${GREEN}✅ 构建完成${NC}"
}

# 优化资源
optimize_assets() {
    echo -e "${YELLOW}⚡ 优化资源...${NC}"
    
    # 压缩图片（如果有的话）
    # find public/images -name "*.jpg" -o -name "*.png" | xargs -I {} sh -c 'echo "Optimizing {}"'
    
    echo -e "${GREEN}✅ 资源优化完成${NC}"
}

# 部署到 Vercel
deploy_vercel() {
    echo -e "${YELLOW}🌐 部署到 Vercel...${NC}"
    
    if ! command -v vercel &> /dev/null; then
        echo -e "${YELLOW}📦 安装 Vercel CLI...${NC}"
        npm install -g vercel
    fi
    
    if [ "$ENVIRONMENT" = "production" ]; then
        vercel --prod
    else
        vercel
    fi
    
    echo -e "${GREEN}✅ Vercel 部署完成${NC}"
}

# 部署到 Netlify
deploy_netlify() {
    echo -e "${YELLOW}🌐 部署到 Netlify...${NC}"
    
    if ! command -v netlify &> /dev/null; then
        echo -e "${YELLOW}📦 安装 Netlify CLI...${NC}"
        npm install -g netlify-cli
    fi
    
    netlify deploy --prod --dir=out
    
    echo -e "${GREEN}✅ Netlify 部署完成${NC}"
}

# 清理缓存
cleanup() {
    echo -e "${YELLOW}🧹 清理缓存...${NC}"
    rm -rf .next/cache
    echo -e "${GREEN}✅ 清理完成${NC}"
}

# 主函数
main() {
    echo -e "${GREEN}🎯 部署环境: $ENVIRONMENT${NC}"
    
    check_dependencies
    install_dependencies
    run_tests
    build_project
    optimize_assets
    
    # 根据环境选择部署方式
    case "$ENVIRONMENT" in
        "vercel")
            deploy_vercel
            ;;
        "netlify")
            deploy_netlify
            ;;
        "production")
            deploy_vercel
            ;;
        *)
            echo -e "${YELLOW}⚠️  未指定部署平台，跳过部署步骤${NC}"
            ;;
    esac
    
    cleanup
    
    echo -e "${GREEN}🎉 部署完成！${NC}"
    echo -e "${GREEN}📱 网站地址: https://$PROJECT_NAME.vercel.app${NC}"
}

# 错误处理
trap 'echo -e "${RED}❌ 部署失败${NC}"; exit 1' ERR

# 运行主函数
main

# 修复Supabase RLS策略问题

## 问题确认 ✅
通过运行 `npm run test:supabase` 已确认问题：
- ❌ 插入产品失败: new row violates row-level security policy for table "products"
- 错误代码: 42501

## 问题描述
当前遇到的401错误是由于Supabase的行级安全策略（RLS）配置不完整导致的。数据库表启用了RLS，但只有读取策略，缺少插入、更新和删除的策略。

## 解决方案

### 方法1：通过Supabase Dashboard执行SQL

1. 登录到您的Supabase项目：https://supabase.com/dashboard
2. 进入您的项目：`swpsfimowlrflqdjgupq`
3. 点击左侧菜单的 "SQL Editor"
4. 创建一个新的查询
5. 复制并执行以下SQL命令：

```sql
-- 删除现有的策略（如果存在）
DROP POLICY IF EXISTS "Allow public read access" ON products;
DROP POLICY IF EXISTS "Allow public read access" ON articles;
DROP POLICY IF EXISTS "Allow public read access" ON categories;

-- 产品表的RLS策略
CREATE POLICY "Allow public read products" ON products FOR SELECT USING (true);
CREATE POLICY "Allow anon insert products" ON products FOR INSERT WITH CHECK (true);
CREATE POLICY "Allow anon update products" ON products FOR UPDATE USING (true) WITH CHECK (true);
CREATE POLICY "Allow anon delete products" ON products FOR DELETE USING (true);

-- 文章表的RLS策略
CREATE POLICY "Allow public read published articles" ON articles FOR SELECT USING (published = true);
CREATE POLICY "Allow anon read all articles" ON articles FOR SELECT USING (true);
CREATE POLICY "Allow anon insert articles" ON articles FOR INSERT WITH CHECK (true);
CREATE POLICY "Allow anon update articles" ON articles FOR UPDATE USING (true) WITH CHECK (true);
CREATE POLICY "Allow anon delete articles" ON articles FOR DELETE USING (true);

-- 分类表的RLS策略
CREATE POLICY "Allow public read categories" ON categories FOR SELECT USING (true);
CREATE POLICY "Allow anon insert categories" ON categories FOR INSERT WITH CHECK (true);
CREATE POLICY "Allow anon update categories" ON categories FOR UPDATE USING (true) WITH CHECK (true);
CREATE POLICY "Allow anon delete categories" ON categories FOR DELETE USING (true);
```

### 方法2：使用Supabase CLI（如果已安装）

```bash
# 确保您已登录到正确的项目
supabase login
supabase link --project-ref swpsfimowlrflqdjgupq

# 执行SQL文件
supabase db reset --linked
```

## 验证修复

执行SQL后，您可以通过以下方式验证修复是否成功：

1. 在Supabase Dashboard中，进入 "Authentication" > "Policies"
2. 检查每个表（products, articles, categories）是否都有以下策略：
   - SELECT 策略（读取）
   - INSERT 策略（插入）
   - UPDATE 策略（更新）
   - DELETE 策略（删除）

3. 或者在SQL Editor中运行以下查询来检查策略：

```sql
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual, with_check
FROM pg_policies 
WHERE tablename IN ('products', 'articles', 'categories')
ORDER BY tablename, policyname;
```

## 安全注意事项

**重要**：当前的RLS策略允许匿名用户进行所有操作（读取、插入、更新、删除）。这是为了简化开发过程。

在生产环境中，您应该考虑更严格的安全策略：

1. **创建管理员角色**：
```sql
-- 创建管理员角色
CREATE ROLE admin_role;

-- 只允许管理员进行写操作
CREATE POLICY "Allow admin write" ON products 
FOR ALL TO admin_role USING (true) WITH CHECK (true);
```

2. **使用认证用户**：
```sql
-- 只允许认证用户进行写操作
CREATE POLICY "Allow authenticated write" ON products 
FOR ALL TO authenticated USING (true) WITH CHECK (true);
```

3. **基于用户ID的策略**：
```sql
-- 只允许特定用户进行操作
CREATE POLICY "Allow specific user" ON products 
FOR ALL USING (auth.uid() = 'your-admin-user-id');
```

## 测试

修复后，尝试再次创建产品。如果仍然遇到问题，请检查：

1. Supabase项目的API密钥是否正确
2. 网络连接是否正常
3. 是否有其他错误信息

## 联系支持

如果问题仍然存在，请提供：
1. 完整的错误信息
2. Supabase项目设置截图
3. 当前的RLS策略列表

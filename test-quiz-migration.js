const fs = require('fs');
const { createClient } = require('@supabase/supabase-js');

// 读取环境变量
let supabaseUrl, supabaseKey;
try {
  const envContent = fs.readFileSync('.env.local', 'utf8');
  const envLines = envContent.split('\n');
  for (const line of envLines) {
    if (line.startsWith('NEXT_PUBLIC_SUPABASE_URL=')) {
      supabaseUrl = line.split('=')[1];
    }
    if (line.startsWith('NEXT_PUBLIC_SUPABASE_ANON_KEY=')) {
      supabaseKey = line.split('=')[1];
    }
  }
} catch (error) {
  console.error('Error reading .env.local:', error.message);
  process.exit(1);
}

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testQuizSystem() {
  try {
    console.log('Testing quiz system setup...');

    // 跳过表创建，假设表已经存在
    console.log('1. Checking if quiz_configs table exists...');

    // 测试插入数据
    console.log('2. Testing data insertion...');
    const { data: insertData, error: insertError } = await supabase
      .from('quiz_configs')
      .insert({
        title: 'Test Quiz',
        description: 'A simple test quiz',
        questions: [
          {
            id: 'test',
            text: 'Test question?',
            type: 'single_choice',
            options: [
              { id: '1', text: 'Option 1', value: '1' }
            ]
          }
        ],
        result_groups: [
          {
            id: 'result1',
            title: 'Test Result',
            description: 'Test description',
            emoji: '🎯',
            dimensionScores: { test: [1, 2] },
            productCategories: ['test']
          }
        ],
        dimensions: ['test'],
        active: true
      })
      .select();
    
    if (insertError) {
      console.error('Error inserting data:', insertError);
      return;
    }
    
    console.log('✅ Data inserted successfully:', insertData);
    
    // 测试查询数据
    console.log('3. Testing data retrieval...');
    const { data: queryData, error: queryError } = await supabase
      .from('quiz_configs')
      .select('*')
      .eq('active', true);
    
    if (queryError) {
      console.error('Error querying data:', queryError);
      return;
    }
    
    console.log('✅ Data retrieved successfully:', queryData.length, 'records found');
    
    console.log('🎉 Quiz system test completed successfully!');
    
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

testQuizSystem();

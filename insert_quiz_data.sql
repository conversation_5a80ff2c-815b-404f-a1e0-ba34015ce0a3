-- 插入测验系统默认数据
-- 请在创建表之后执行此脚本

-- 1. 插入默认的测验配置数据
INSERT INTO quiz_configs (title, description, questions, result_groups, dimensions, active) VALUES (
  'Quiz de Recomendaciones Personalizadas',
  'Responde unas pocas preguntas y te ayudaremos a encontrar los productos perfectos para ti',
  '[
    {
      "id": "experience_level",
      "text": "¿Cuál es tu nivel de experiencia?",
      "type": "single_choice",
      "weight": 1.5,
      "options": [
        {
          "id": "beginner",
          "text": "Soy completamente nuevo/a",
          "value": "beginner",
          "emoji": "🌱",
          "score": {"experience": 1, "intensity": 1}
        },
        {
          "id": "intermediate", 
          "text": "Tengo algo de experiencia",
          "value": "intermediate",
          "emoji": "🌿",
          "score": {"experience": 2, "intensity": 2}
        },
        {
          "id": "advanced",
          "text": "Soy muy experimentado/a", 
          "value": "advanced",
          "emoji": "🌳",
          "score": {"experience": 3, "intensity": 3}
        }
      ]
    },
    {
      "id": "usage_type",
      "text": "¿Prefieres usar productos solo/a o en pareja?",
      "type": "single_choice",
      "weight": 1.2,
      "options": [
        {
          "id": "solo",
          "text": "Solo/a",
          "value": "solo", 
          "emoji": "🧘",
          "score": {"social": 1}
        },
        {
          "id": "couple",
          "text": "En pareja",
          "value": "couple",
          "emoji": "💕", 
          "score": {"social": 3}
        },
        {
          "id": "both",
          "text": "Ambos",
          "value": "both",
          "emoji": "🤝",
          "score": {"social": 2}
        }
      ]
    },
    {
      "id": "experience_type",
      "text": "¿Qué tipo de experiencia buscas?",
      "type": "single_choice",
      "weight": 1.0,
      "options": [
        {
          "id": "gentle",
          "text": "Suave y relajante",
          "value": "gentle",
          "emoji": "🌸",
          "score": {"intensity": 1, "exploration": 1}
        },
        {
          "id": "intense", 
          "text": "Intensa y emocionante",
          "value": "intense",
          "emoji": "🔥",
          "score": {"intensity": 3, "exploration": 2}
        },
        {
          "id": "varied",
          "text": "Variada y exploratoria",
          "value": "varied", 
          "emoji": "🌈",
          "score": {"intensity": 2, "exploration": 3}
        }
      ]
    }
  ]'::jsonb,
  '[
    {
      "id": "beginner_gentle",
      "title": "Perfecto para Comenzar",
      "description": "Productos suaves y fáciles de usar, ideales para principiantes que buscan una experiencia cómoda.",
      "emoji": "🌸",
      "dimensionScores": {
        "experience": [1, 1.5],
        "intensity": [1, 1.5],
        "social": [1, 3],
        "exploration": [1, 2]
      },
      "productCategories": ["vibradores", "balas-vibradoras"]
    },
    {
      "id": "intermediate_balanced",
      "title": "Experiencia Equilibrada", 
      "description": "Productos versátiles para usuarios con experiencia intermedia que buscan variedad.",
      "emoji": "⚖️",
      "dimensionScores": {
        "experience": [1.5, 2.5],
        "intensity": [1.5, 2.5], 
        "social": [1, 3],
        "exploration": [2, 3]
      },
      "productCategories": ["vibradores", "masturbadores", "pinzas-pezones"]
    },
    {
      "id": "advanced_intense",
      "title": "Máximo Placer",
      "description": "Productos de alta gama para usuarios experimentados que buscan sensaciones intensas.",
      "emoji": "✨",
      "dimensionScores": {
        "experience": [2.5, 3],
        "intensity": [2.5, 3],
        "social": [1, 3], 
        "exploration": [2, 3]
      },
      "productCategories": ["masturbadores", "pinzas-pezones", "succionadores-pezones"]
    }
  ]'::jsonb,
  ARRAY['experience', 'intensity', 'social', 'exploration'],
  true
);

-- 2. 插入默认的产品分组规则
INSERT INTO product_group_rules (name, description, emoji, rules, priority, active) VALUES 
(
  'Productos para Principiantes',
  'Productos suaves y fáciles de usar, ideales para nuevos usuarios',
  '🌱',
  '{
    "experienceLevels": ["beginner"],
    "usageTypes": ["solo", "couple"],
    "experienceTypes": ["gentle"],
    "categories": ["vibradores", "balas-vibradoras"]
  }'::jsonb,
  100,
  true
),
(
  'Productos Intermedios',
  'Productos versátiles para usuarios con experiencia intermedia',
  '🌿', 
  '{
    "experienceLevels": ["intermediate"],
    "usageTypes": ["solo", "couple", "both"],
    "experienceTypes": ["gentle", "intense", "varied"],
    "categories": ["vibradores", "masturbadores", "pinzas-pezones"]
  }'::jsonb,
  200,
  true
),
(
  'Productos Avanzados',
  'Productos especializados para usuarios experimentados',
  '🌳',
  '{
    "experienceLevels": ["advanced"],
    "usageTypes": ["solo", "couple", "both"],
    "experienceTypes": ["intense", "varied"],
    "categories": ["masturbadores", "pinzas-pezones", "succionadores-pezones"]
  }'::jsonb,
  300,
  true
);

-- 验证插入的数据
SELECT 'Quiz configuration inserted:' as info;
SELECT id, title, active FROM quiz_configs;

SELECT 'Product group rules inserted:' as info;
SELECT id, name, priority, active FROM product_group_rules ORDER BY priority DESC;

-- =====================================================
-- 数据库迁移验证脚本
-- 用于验证迁移是否成功完成
-- =====================================================

-- 1. 检查products表结构
SELECT 
  '=== PRODUCTS TABLE STRUCTURE ===' as section;

SELECT 
  column_name, 
  data_type, 
  is_nullable,
  column_default
FROM information_schema.columns 
WHERE table_name = 'products' 
ORDER BY ordinal_position;

-- 2. 检查products表数据
SELECT 
  '=== PRODUCTS TABLE DATA ===' as section;

SELECT 
  COUNT(*) as total_products,
  COUNT(DISTINCT category) as unique_categories
FROM products;

-- 3. 显示产品样本数据
SELECT 
  '=== SAMPLE PRODUCTS ===' as section;

SELECT 
  id,
  name,
  category,
  amazon_url,
  created_at
FROM products 
ORDER BY created_at DESC
LIMIT 10;

-- 4. 检查分类分布
SELECT 
  '=== PRODUCTS BY CATEGORY ===' as section;

SELECT 
  category,
  COUNT(*) as product_count
FROM products 
GROUP BY category
ORDER BY product_count DESC;

-- 5. 检查articles表关联
SELECT 
  '=== ARTICLES-PRODUCTS RELATIONSHIP ===' as section;

SELECT 
  COUNT(a.id) as total_articles,
  COUNT(a.product_id) as articles_with_products,
  COUNT(p.id) as valid_product_links
FROM articles a
LEFT JOIN products p ON a.product_id = p.id;

-- 6. 检查文章按分类分布
SELECT 
  '=== ARTICLES BY PRODUCT CATEGORY ===' as section;

SELECT 
  p.category,
  COUNT(a.id) as article_count
FROM articles a
JOIN products p ON a.product_id = p.id
WHERE a.published = true
GROUP BY p.category
ORDER BY article_count DESC;

-- 7. 检查索引
SELECT 
  '=== TABLE INDEXES ===' as section;

SELECT 
  indexname,
  indexdef
FROM pg_indexes 
WHERE tablename IN ('products', 'articles', 'categories')
ORDER BY tablename, indexname;

-- 8. 检查RLS策略
SELECT 
  '=== ROW LEVEL SECURITY POLICIES ===' as section;

SELECT 
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual
FROM pg_policies 
WHERE tablename IN ('products', 'articles', 'categories')
ORDER BY tablename, policyname;

-- 9. 检查触发器
SELECT 
  '=== TABLE TRIGGERS ===' as section;

SELECT 
  trigger_name,
  event_manipulation,
  event_object_table,
  action_timing,
  action_statement
FROM information_schema.triggers 
WHERE event_object_table IN ('products', 'articles', 'categories')
ORDER BY event_object_table, trigger_name;

-- 10. 检查外键约束
SELECT 
  '=== FOREIGN KEY CONSTRAINTS ===' as section;

SELECT 
  tc.table_name,
  tc.constraint_name,
  tc.constraint_type,
  kcu.column_name,
  ccu.table_name AS foreign_table_name,
  ccu.column_name AS foreign_column_name
FROM information_schema.table_constraints AS tc
JOIN information_schema.key_column_usage AS kcu
  ON tc.constraint_name = kcu.constraint_name
  AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
  ON ccu.constraint_name = tc.constraint_name
  AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
  AND tc.table_name IN ('products', 'articles', 'categories');

-- 11. 最终状态报告
SELECT 
  '=== MIGRATION STATUS REPORT ===' as section;

SELECT 
  'Products table simplified: ✓' as status
WHERE EXISTS (
  SELECT 1 FROM information_schema.columns 
  WHERE table_name = 'products' AND column_name = 'name'
) AND EXISTS (
  SELECT 1 FROM information_schema.columns 
  WHERE table_name = 'products' AND column_name = 'category'
) AND EXISTS (
  SELECT 1 FROM information_schema.columns 
  WHERE table_name = 'products' AND column_name = 'amazon_url'
) AND NOT EXISTS (
  SELECT 1 FROM information_schema.columns 
  WHERE table_name = 'products' AND column_name IN ('description', 'price', 'rating', 'features', 'image_url')
);

SELECT 
  CASE 
    WHEN COUNT(*) > 0 THEN 'Articles-Products relationship: ✓'
    ELSE 'Articles-Products relationship: ✗ (No linked articles found)'
  END as status
FROM articles a
JOIN products p ON a.product_id = p.id;

SELECT 
  CASE 
    WHEN COUNT(*) >= 4 THEN 'RLS policies configured: ✓'
    ELSE 'RLS policies configured: ✗ (Missing policies)'
  END as status
FROM pg_policies 
WHERE tablename = 'products';

-- 12. 建议的下一步
SELECT 
  '=== NEXT STEPS ===' as section;

SELECT 
  '1. Test the application with simplified product model' as step
UNION ALL
SELECT 
  '2. Update any remaining code that references removed fields' as step
UNION ALL
SELECT 
  '3. Consider dropping backup table: DROP TABLE products_backup;' as step
UNION ALL
SELECT 
  '4. Monitor application performance and functionality' as step;

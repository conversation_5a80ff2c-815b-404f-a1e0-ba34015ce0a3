-- =====================================================
-- Supabase数据库迁移脚本
-- 目的：简化产品表结构，只保留核心字段
-- 执行前请备份数据！
-- =====================================================

-- 1. 首先备份现有产品数据（可选，建议在Supabase控制台手动导出）
-- 如果需要保留现有数据，请先在Supabase控制台导出products表

-- 2. 删除现有的products表（如果存在数据，请先备份）
DROP TABLE IF EXISTS products CASCADE;

-- 3. 重新创建简化的products表
CREATE TABLE products (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  category VARCHAR(100) NOT NULL,
  amazon_url TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. 创建索引以提高查询性能
CREATE INDEX idx_products_category ON products(category);
CREATE INDEX idx_products_name ON products(name);

-- 5. 创建更新时间戳的触发器函数（如果不存在）
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 6. 为products表创建更新触发器
CREATE TRIGGER update_products_updated_at 
    BEFORE UPDATE ON products 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- 7. 启用行级安全策略（RLS）
ALTER TABLE products ENABLE ROW LEVEL SECURITY;

-- 8. 创建RLS策略
-- 允许所有人读取产品
CREATE POLICY "Allow public read products" ON products FOR SELECT USING (true);

-- 允许匿名用户插入产品（用于管理系统）
CREATE POLICY "Allow anon insert products" ON products FOR INSERT WITH CHECK (true);

-- 允许匿名用户更新产品（用于管理系统）
CREATE POLICY "Allow anon update products" ON products FOR UPDATE USING (true) WITH CHECK (true);

-- 允许匿名用户删除产品（用于管理系统）
CREATE POLICY "Allow anon delete products" ON products FOR DELETE USING (true);

-- 9. 确保articles表的外键约束正确
-- 检查articles表是否存在，如果不存在则创建
CREATE TABLE IF NOT EXISTS articles (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  content TEXT NOT NULL,
  excerpt TEXT NOT NULL,
  slug VARCHAR(255) UNIQUE NOT NULL,
  product_id UUID REFERENCES products(id) ON DELETE CASCADE,
  cover_image TEXT, -- 文章封面图片URL
  video_url TEXT, -- 产品展示视频URL
  meta_title VARCHAR(255) NOT NULL,
  meta_description TEXT NOT NULL,
  keywords TEXT[], -- SEO关键词数组
  published BOOLEAN DEFAULT FALSE,
  published_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 10. 为articles表创建索引（如果不存在）
CREATE INDEX IF NOT EXISTS idx_articles_slug ON articles(slug);
CREATE INDEX IF NOT EXISTS idx_articles_published ON articles(published);
CREATE INDEX IF NOT EXISTS idx_articles_product_id ON articles(product_id);

-- 11. 为articles表创建更新触发器（如果不存在）
DROP TRIGGER IF EXISTS update_articles_updated_at ON articles;
CREATE TRIGGER update_articles_updated_at 
    BEFORE UPDATE ON articles 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- 12. 为articles表启用RLS（如果未启用）
ALTER TABLE articles ENABLE ROW LEVEL SECURITY;

-- 13. 创建articles表的RLS策略（如果不存在）
DROP POLICY IF EXISTS "Allow public read articles" ON articles;
CREATE POLICY "Allow public read articles" ON articles FOR SELECT USING (published = true);

DROP POLICY IF EXISTS "Allow anon manage articles" ON articles;
CREATE POLICY "Allow anon manage articles" ON articles FOR ALL USING (true) WITH CHECK (true);

-- 14. 确保categories表存在并配置正确
CREATE TABLE IF NOT EXISTS categories (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(100) NOT NULL UNIQUE,
  slug VARCHAR(100) NOT NULL UNIQUE,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 15. 为categories表启用RLS
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;

-- 16. 创建categories表的RLS策略
DROP POLICY IF EXISTS "Allow public read categories" ON categories;
CREATE POLICY "Allow public read categories" ON categories FOR SELECT USING (true);

DROP POLICY IF EXISTS "Allow anon manage categories" ON categories;
CREATE POLICY "Allow anon manage categories" ON categories FOR ALL USING (true) WITH CHECK (true);

-- 17. 插入示例产品数据
INSERT INTO products (name, category, amazon_url) VALUES
('Vibrador Clásico Rosa', 'vibradores', 'https://amazon.com.mx/dp/example1'),
('Bala Vibradora Discreta', 'balas-vibradoras', 'https://amazon.com.mx/dp/example2'),
('Masturbador Realista Premium', 'masturbadores', 'https://amazon.com.mx/dp/example3'),
('Pinzas para Pezones Ajustables', 'pinzas-pezones', 'https://amazon.com.mx/dp/example4')
ON CONFLICT DO NOTHING;

-- 18. 插入示例分类数据
INSERT INTO categories (name, slug, description) VALUES
('Vibradores', 'vibradores', 'Productos vibradores de alta calidad para el bienestar íntimo'),
('Balas Vibradoras', 'balas-vibradoras', 'Pequeños pero potentes, perfectos para la estimulación precisa'),
('Masturbadores', 'masturbadores', 'Productos diseñados para el placer masculino'),
('Pinzas para Pezones', 'pinzas-pezones', 'Accesorios para juegos de sensaciones y estimulación')
ON CONFLICT (slug) DO NOTHING;

-- =====================================================
-- 脚本执行完成
-- =====================================================

-- 验证数据
SELECT 'Products table structure:' as info;

-- 查看products表结构
SELECT
  column_name,
  data_type,
  is_nullable,
  column_default
FROM information_schema.columns
WHERE table_name = 'products'
ORDER BY ordinal_position;

SELECT 'Sample products:' as info;
SELECT * FROM products LIMIT 5;

SELECT 'Categories:' as info;
SELECT * FROM categories;

-- 显示执行结果
SELECT
  'Migration completed successfully!' as status,
  'Products table simplified to 3 core fields: name, category, amazon_url' as changes,
  'All RLS policies and triggers configured' as security;

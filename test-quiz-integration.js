const fs = require('fs');
const { createClient } = require('@supabase/supabase-js');

// 读取环境变量
let supabaseUrl, supabaseKey;
try {
  const envContent = fs.readFileSync('.env.local', 'utf8');
  const envLines = envContent.split('\n');
  for (const line of envLines) {
    if (line.startsWith('NEXT_PUBLIC_SUPABASE_URL=')) {
      supabaseUrl = line.split('=')[1];
    }
    if (line.startsWith('NEXT_PUBLIC_SUPABASE_ANON_KEY=')) {
      supabaseKey = line.split('=')[1];
    }
  }
} catch (error) {
  console.error('Error reading .env.local:', error.message);
  process.exit(1);
}

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testQuizIntegration() {
  try {
    console.log('🧪 Testing Quiz System Integration...\n');
    
    // 1. 测试产品数据获取
    console.log('1. Testing product data retrieval...');
    const { data: products, error: productsError } = await supabase
      .from('products')
      .select('*')
      .limit(5);
    
    if (productsError) {
      console.error('❌ Error fetching products:', productsError);
      return;
    }
    
    console.log(`✅ Found ${products.length} products`);
    if (products.length > 0) {
      console.log(`   Sample product: ${products[0].name} (${products[0].category})`);
    }
    
    // 2. 测试按分类获取产品
    console.log('\n2. Testing products by category...');
    const categories = ['vibradores', 'masturbadores', 'pinzas-pezones'];
    
    for (const category of categories) {
      const { data: categoryProducts, error: categoryError } = await supabase
        .from('products')
        .select('*')
        .eq('category', category)
        .limit(3);
      
      if (categoryError) {
        console.error(`❌ Error fetching ${category} products:`, categoryError);
        continue;
      }
      
      console.log(`   ${category}: ${categoryProducts.length} products found`);
    }
    
    // 3. 模拟测验结果计算
    console.log('\n3. Testing quiz result calculation...');
    
    // 模拟用户答案
    const mockAnswers = [
      {
        questionId: 'experience_level',
        value: 'beginner'
      },
      {
        questionId: 'usage_type', 
        value: 'solo'
      },
      {
        questionId: 'experience_type',
        value: 'gentle'
      }
    ];
    
    // 模拟维度得分计算
    const dimensionScores = {
      experience: 1.0,
      intensity: 1.0,
      social: 1.0,
      exploration: 1.0
    };
    
    console.log('   Mock answers:', mockAnswers.map(a => `${a.questionId}: ${a.value}`).join(', '));
    console.log('   Calculated dimension scores:', dimensionScores);
    
    // 4. 测试产品推荐逻辑
    console.log('\n4. Testing product recommendation logic...');
    
    // 根据初学者配置推荐产品
    const recommendedCategories = ['vibradores', 'balas-vibradoras'];
    let recommendedProducts = [];
    
    for (const category of recommendedCategories) {
      const { data: categoryProducts, error } = await supabase
        .from('products')
        .select('*')
        .eq('category', category)
        .limit(2);
      
      if (!error && categoryProducts) {
        recommendedProducts.push(...categoryProducts);
      }
    }
    
    console.log(`✅ Generated ${recommendedProducts.length} product recommendations`);
    recommendedProducts.forEach((product, index) => {
      console.log(`   ${index + 1}. ${product.name} (${product.category})`);
    });
    
    // 5. 测试Amazon链接集成
    console.log('\n5. Testing Amazon link integration...');
    
    const productsWithAmazonLinks = recommendedProducts.filter(p => p.amazon_url);
    console.log(`✅ ${productsWithAmazonLinks.length} products have Amazon links`);
    
    if (productsWithAmazonLinks.length > 0) {
      console.log(`   Sample Amazon URL: ${productsWithAmazonLinks[0].amazon_url}`);
    }
    
    // 6. 测试结果组匹配
    console.log('\n6. Testing result group matching...');
    
    const mockResultGroups = [
      {
        id: 'beginner_gentle',
        title: 'Perfecto para Comenzar',
        description: 'Productos suaves y fáciles de usar, ideales para principiantes.',
        emoji: '🌸',
        dimensionScores: {
          experience: [1, 1.5],
          intensity: [1, 1.5],
          social: [1, 3],
          exploration: [1, 2]
        },
        productCategories: ['vibradores', 'balas-vibradoras']
      }
    ];
    
    // 简单的匹配逻辑
    const matchingGroup = mockResultGroups.find(group => {
      const experienceRange = group.dimensionScores.experience;
      const intensityRange = group.dimensionScores.intensity;
      
      return dimensionScores.experience >= experienceRange[0] && 
             dimensionScores.experience <= experienceRange[1] &&
             dimensionScores.intensity >= intensityRange[0] && 
             dimensionScores.intensity <= intensityRange[1];
    });
    
    if (matchingGroup) {
      console.log(`✅ Matched result group: ${matchingGroup.title}`);
      console.log(`   Description: ${matchingGroup.description}`);
      console.log(`   Recommended categories: ${matchingGroup.productCategories.join(', ')}`);
    } else {
      console.log('❌ No matching result group found');
    }
    
    // 7. 测试置信度计算
    console.log('\n7. Testing confidence calculation...');
    
    if (matchingGroup) {
      let totalMatch = 0;
      let totalDimensions = 0;
      
      Object.entries(matchingGroup.dimensionScores).forEach(([dimension, range]) => {
        if (dimensionScores[dimension] !== undefined) {
          const score = dimensionScores[dimension];
          const [min, max] = range;
          
          if (score >= min && score <= max) {
            totalMatch += 1;
          } else {
            const distance = Math.min(Math.abs(score - min), Math.abs(score - max));
            const maxDistance = Math.max(max - min, 1);
            totalMatch += Math.max(0, 1 - distance / maxDistance);
          }
          totalDimensions++;
        }
      });
      
      const confidence = totalDimensions > 0 ? totalMatch / totalDimensions : 0;
      console.log(`✅ Calculated confidence: ${(confidence * 100).toFixed(1)}%`);
    }
    
    console.log('\n🎉 Quiz system integration test completed successfully!');
    console.log('\n📋 Summary:');
    console.log(`   - Products available: ${products.length}`);
    console.log(`   - Recommendations generated: ${recommendedProducts.length}`);
    console.log(`   - Amazon links available: ${productsWithAmazonLinks.length}`);
    console.log(`   - Result group matching: ${matchingGroup ? 'Working' : 'Failed'}`);
    
  } catch (error) {
    console.error('❌ Unexpected error during integration test:', error);
  }
}

testQuizIntegration();

-- =====================================================
-- 添加页面参与度跟踪表
-- 用于跟踪用户在页面上的行为和参与度
-- =====================================================

-- 创建页面参与度表
CREATE TABLE IF NOT EXISTS page_engagement (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  session_id VARCHAR(255) NOT NULL,
  page_path VARCHAR(500) NOT NULL,
  page_title VARCHAR(500),
  time_on_page INTEGER NOT NULL DEFAULT 0, -- 页面停留时间（秒）
  scroll_depth INTEGER NOT NULL DEFAULT 0, -- 滚动深度（百分比 0-100）
  click_count INTEGER NOT NULL DEFAULT 0, -- 页面内点击次数
  exit_page BOOLEAN NOT NULL DEFAULT false, -- 是否为退出页面
  bounced BOOLEAN NOT NULL DEFAULT false, -- 是否为跳出
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_page_engagement_session_id ON page_engagement(session_id);
CREATE INDEX IF NOT EXISTS idx_page_engagement_page_path ON page_engagement(page_path);
CREATE INDEX IF NOT EXISTS idx_page_engagement_created_at ON page_engagement(created_at);
CREATE INDEX IF NOT EXISTS idx_page_engagement_time_on_page ON page_engagement(time_on_page);
CREATE INDEX IF NOT EXISTS idx_page_engagement_scroll_depth ON page_engagement(scroll_depth);

-- 创建复合索引
CREATE INDEX IF NOT EXISTS idx_page_engagement_path_date ON page_engagement(page_path, created_at);
CREATE INDEX IF NOT EXISTS idx_page_engagement_session_date ON page_engagement(session_id, created_at);

-- 添加注释
COMMENT ON TABLE page_engagement IS '页面参与度跟踪表，记录用户在页面上的行为数据';
COMMENT ON COLUMN page_engagement.session_id IS '用户会话ID';
COMMENT ON COLUMN page_engagement.page_path IS '页面路径';
COMMENT ON COLUMN page_engagement.page_title IS '页面标题';
COMMENT ON COLUMN page_engagement.time_on_page IS '页面停留时间（秒）';
COMMENT ON COLUMN page_engagement.scroll_depth IS '最大滚动深度（百分比）';
COMMENT ON COLUMN page_engagement.click_count IS '页面内点击次数';
COMMENT ON COLUMN page_engagement.exit_page IS '是否为退出页面';
COMMENT ON COLUMN page_engagement.bounced IS '是否为跳出（停留时间短且滚动深度低）';

-- 插入一些测试数据
INSERT INTO page_engagement (
  session_id,
  page_path,
  page_title,
  time_on_page,
  scroll_depth,
  click_count,
  exit_page,
  bounced
) VALUES 
  ('test-session-1', '/', 'Tu Tienda Íntima - Inicio', 45, 80, 3, false, false),
  ('test-session-1', '/quiz', 'Quiz Divertido', 120, 95, 8, false, false),
  ('test-session-1', '/categoria/vibradores', 'Vibradores', 25, 40, 1, true, true),
  ('test-session-2', '/', 'Tu Tienda Íntima - Inicio', 15, 20, 0, true, true),
  ('test-session-3', '/', 'Tu Tienda Íntima - Inicio', 90, 100, 12, false, false),
  ('test-session-3', '/quiz', 'Quiz Divertido', 180, 100, 15, false, false),
  ('test-session-3', '/categoria/vibradores', 'Vibradores', 60, 75, 5, true, false);

-- 验证表创建和数据插入
SELECT 
  'page_engagement table created successfully' as status,
  COUNT(*) as sample_records
FROM page_engagement;

-- 显示参与度统计示例
SELECT 
  page_path,
  COUNT(*) as visits,
  ROUND(AVG(time_on_page)) as avg_time_on_page,
  ROUND(AVG(scroll_depth)) as avg_scroll_depth,
  ROUND(AVG(click_count)) as avg_clicks,
  ROUND(COUNT(*) FILTER (WHERE bounced = true) * 100.0 / COUNT(*), 2) as bounce_rate
FROM page_engagement 
GROUP BY page_path
ORDER BY visits DESC;

-- 显示表结构
SELECT 
  column_name,
  data_type,
  is_nullable,
  column_default
FROM information_schema.columns 
WHERE table_name = 'page_engagement' 
ORDER BY ordinal_position;

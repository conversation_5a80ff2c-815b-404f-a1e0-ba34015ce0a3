-- =====================================================
-- Supabase数据库迁移脚本 - 适用于Supabase SQL编辑器
-- 目的：创建简化的产品表结构
-- 适用于全新数据库（无现有数据）
-- =====================================================

-- 1. 删除现有表（如果存在）
DROP TABLE IF EXISTS products CASCADE;
DROP TABLE IF EXISTS articles CASCADE;
DROP TABLE IF EXISTS categories CASCADE;

-- 2. 创建简化的products表
CREATE TABLE products (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  category VARCHAR(100) NOT NULL,
  amazon_url TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. 创建articles表
CREATE TABLE articles (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  content TEXT NOT NULL,
  excerpt TEXT NOT NULL,
  slug VARCHAR(255) UNIQUE NOT NULL,
  product_id UUID REFERENCES products(id) ON DELETE CASCADE,
  cover_image TEXT, -- 文章封面图片URL
  video_url TEXT, -- 产品展示视频URL
  meta_title VARCHAR(255) NOT NULL,
  meta_description TEXT NOT NULL,
  keywords TEXT[],
  published BOOLEAN DEFAULT FALSE,
  published_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. 创建categories表
CREATE TABLE categories (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(100) NOT NULL UNIQUE,
  slug VARCHAR(100) NOT NULL UNIQUE,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. 创建索引
CREATE INDEX idx_products_category ON products(category);
CREATE INDEX idx_products_name ON products(name);
CREATE INDEX idx_articles_slug ON articles(slug);
CREATE INDEX idx_articles_published ON articles(published);
CREATE INDEX idx_articles_product_id ON articles(product_id);

-- 6. 创建更新时间戳的触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 7. 创建触发器
CREATE TRIGGER update_products_updated_at 
    BEFORE UPDATE ON products 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_articles_updated_at 
    BEFORE UPDATE ON articles 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- 8. 启用行级安全策略（RLS）
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE articles ENABLE ROW LEVEL SECURITY;
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;

-- 9. 创建products表的RLS策略
CREATE POLICY "Allow public read products" ON products FOR SELECT USING (true);
CREATE POLICY "Allow anon insert products" ON products FOR INSERT WITH CHECK (true);
CREATE POLICY "Allow anon update products" ON products FOR UPDATE USING (true) WITH CHECK (true);
CREATE POLICY "Allow anon delete products" ON products FOR DELETE USING (true);

-- 10. 创建articles表的RLS策略
CREATE POLICY "Allow public read articles" ON articles FOR SELECT USING (published = true);
CREATE POLICY "Allow anon manage articles" ON articles FOR ALL USING (true) WITH CHECK (true);

-- 11. 创建categories表的RLS策略
CREATE POLICY "Allow public read categories" ON categories FOR SELECT USING (true);
CREATE POLICY "Allow anon manage categories" ON categories FOR ALL USING (true) WITH CHECK (true);

-- 12. 插入示例分类数据
INSERT INTO categories (name, slug, description) VALUES
('Vibradores', 'vibradores', 'Productos vibradores de alta calidad para el bienestar íntimo'),
('Balas Vibradoras', 'balas-vibradoras', 'Pequeños pero potentes, perfectos para la estimulación precisa'),
('Masturbadores', 'masturbadores', 'Productos diseñados para el placer masculino'),
('Pinzas para Pezones', 'pinzas-pezones', 'Accesorios para juegos de sensaciones y estimulación'),
('Anillos', 'anillos', 'Anillos para mejorar la experiencia íntima'),
('Succionadores de Pezones', 'succionadores-pezones', 'Productos para estimulación y sensaciones intensas');

-- 13. 插入示例产品数据
INSERT INTO products (name, category, amazon_url) VALUES
('Vibrador Clásico Rosa', 'vibradores', 'https://amazon.com.mx/dp/example1'),
('Bala Vibradora Discreta', 'balas-vibradoras', 'https://amazon.com.mx/dp/example2'),
('Masturbador Realista Premium', 'masturbadores', 'https://amazon.com.mx/dp/example3'),
('Pinzas para Pezones Ajustables', 'pinzas-pezones', 'https://amazon.com.mx/dp/example4'),
('Anillo Vibrador Recargable', 'anillos', 'https://amazon.com.mx/dp/example5'),
('Succionador de Pezones Automático', 'succionadores-pezones', 'https://amazon.com.mx/dp/example6');

-- 14. 验证创建结果
SELECT 'Database migration completed successfully!' as status;

-- 查看创建的表
SELECT 'Products table created with columns:' as info;
SELECT 
  column_name, 
  data_type, 
  is_nullable
FROM information_schema.columns 
WHERE table_name = 'products' 
ORDER BY ordinal_position;

-- 查看插入的数据
SELECT 'Sample products inserted:' as info;
SELECT 
  id,
  name,
  category,
  amazon_url
FROM products;

SELECT 'Categories inserted:' as info;
SELECT 
  name,
  slug,
  description
FROM categories;

-- 验证索引
SELECT 'Indexes created:' as info;
SELECT 
  indexname,
  tablename
FROM pg_indexes 
WHERE tablename IN ('products', 'articles', 'categories')
ORDER BY tablename, indexname;

-- 验证RLS策略
SELECT 'RLS policies created:' as info;
SELECT 
  tablename,
  policyname,
  cmd
FROM pg_policies 
WHERE tablename IN ('products', 'articles', 'categories')
ORDER BY tablename, policyname;

-- 创建测验系统表结构
-- 请在Supabase SQL编辑器中执行此脚本

-- 1. 创建测验配置表
CREATE TABLE quiz_configs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  questions JSONB NOT NULL,
  result_groups JSONB NOT NULL,
  dimensions TEXT[] NOT NULL,
  active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. 创建产品分组规则表
CREATE TABLE product_group_rules (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  emoji VARCHAR(10),
  rules JSONB NOT NULL,
  priority INTEGER DEFAULT 0,
  active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. 创建测验会话表
CREATE TABLE quiz_sessions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  quiz_config_id UUID REFERENCES quiz_configs(id) ON DELETE CASCADE,
  answers JSONB,
  result JSONB,
  started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE,
  user_id VARCHAR(255),
  session_id VARCHAR(255) NOT NULL,
  ip_address INET,
  user_agent TEXT
);

-- 4. 创建产品与分组规则的关联表
CREATE TABLE product_group_assignments (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  product_id UUID REFERENCES products(id) ON DELETE CASCADE,
  group_rule_id UUID REFERENCES product_group_rules(id) ON DELETE CASCADE,
  assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(product_id, group_rule_id)
);

-- 5. 创建索引
CREATE INDEX idx_quiz_configs_active ON quiz_configs(active);
CREATE INDEX idx_quiz_configs_created_at ON quiz_configs(created_at);
CREATE INDEX idx_product_group_rules_active ON product_group_rules(active);
CREATE INDEX idx_product_group_rules_priority ON product_group_rules(priority DESC);
CREATE INDEX idx_quiz_sessions_config_id ON quiz_sessions(quiz_config_id);
CREATE INDEX idx_quiz_sessions_session_id ON quiz_sessions(session_id);
CREATE INDEX idx_quiz_sessions_started_at ON quiz_sessions(started_at);
CREATE INDEX idx_quiz_sessions_completed_at ON quiz_sessions(completed_at);
CREATE INDEX idx_product_group_assignments_product_id ON product_group_assignments(product_id);
CREATE INDEX idx_product_group_assignments_group_rule_id ON product_group_assignments(group_rule_id);

-- 6. 创建更新时间戳的触发器
CREATE TRIGGER update_quiz_configs_updated_at 
    BEFORE UPDATE ON quiz_configs 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_product_group_rules_updated_at 
    BEFORE UPDATE ON product_group_rules 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- 7. 启用行级安全策略（RLS）
ALTER TABLE quiz_configs ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_group_rules ENABLE ROW LEVEL SECURITY;
ALTER TABLE quiz_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_group_assignments ENABLE ROW LEVEL SECURITY;

-- 8. 创建RLS策略
CREATE POLICY "Allow public read active quiz configs" ON quiz_configs 
  FOR SELECT USING (active = true);
CREATE POLICY "Allow anon manage quiz configs" ON quiz_configs 
  FOR ALL USING (true) WITH CHECK (true);

CREATE POLICY "Allow public read active group rules" ON product_group_rules 
  FOR SELECT USING (active = true);
CREATE POLICY "Allow anon manage group rules" ON product_group_rules 
  FOR ALL USING (true) WITH CHECK (true);

CREATE POLICY "Allow anon insert quiz sessions" ON quiz_sessions 
  FOR INSERT WITH CHECK (true);
CREATE POLICY "Allow anon read own quiz sessions" ON quiz_sessions 
  FOR SELECT USING (true);
CREATE POLICY "Allow anon update own quiz sessions" ON quiz_sessions 
  FOR UPDATE USING (true) WITH CHECK (true);

CREATE POLICY "Allow public read product group assignments" ON product_group_assignments 
  FOR SELECT USING (true);
CREATE POLICY "Allow anon manage product group assignments" ON product_group_assignments 
  FOR ALL USING (true) WITH CHECK (true);

-- 验证表创建
SELECT 'Quiz system tables created successfully:' as info;
SELECT table_name FROM information_schema.tables 
WHERE table_name IN ('quiz_configs', 'product_group_rules', 'quiz_sessions', 'product_group_assignments')
ORDER BY table_name;

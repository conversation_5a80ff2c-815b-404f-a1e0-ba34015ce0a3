-- =====================================================
-- 分析数据表迁移脚本
-- 目的：创建访问量跟踪和点击统计相关的表
-- =====================================================

-- 1. 创建页面访问记录表
CREATE TABLE IF NOT EXISTS page_views (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  page_path VARCHAR(500) NOT NULL,
  page_title VARCHAR(500),
  referrer VARCHAR(500),
  user_agent TEXT,
  ip_address INET,
  session_id VARCHAR(100),
  user_id UUID, -- 如果有用户系统的话
  country VARCHAR(100),
  city VARCHAR(100),
  device_type VARCHAR(50), -- desktop, mobile, tablet
  browser VARCHAR(100),
  os VARCHAR(100),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. 创建Amazon链接点击记录表
CREATE TABLE IF NOT EXISTS amazon_clicks (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  product_id UUID REFERENCES products(id) ON DELETE CASCADE,
  product_name VARCHAR(255),
  amazon_url TEXT NOT NULL,
  page_path VARCHAR(500), -- 点击发生的页面
  referrer VARCHAR(500),
  user_agent TEXT,
  ip_address INET,
  session_id VARCHAR(100),
  user_id UUID,
  country VARCHAR(100),
  city VARCHAR(100),
  device_type VARCHAR(50),
  browser VARCHAR(100),
  os VARCHAR(100),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. 创建每日统计汇总表（用于快速查询）
CREATE TABLE IF NOT EXISTS daily_stats (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  date DATE NOT NULL,
  total_page_views INTEGER DEFAULT 0,
  unique_visitors INTEGER DEFAULT 0,
  total_amazon_clicks INTEGER DEFAULT 0,
  bounce_rate DECIMAL(5,2), -- 跳出率
  avg_session_duration INTEGER, -- 平均会话时长（秒）
  top_pages JSONB, -- 热门页面
  top_products JSONB, -- 热门产品
  traffic_sources JSONB, -- 流量来源
  device_breakdown JSONB, -- 设备分布
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(date)
);

-- 4. 创建会话表（用于计算会话相关指标）
CREATE TABLE IF NOT EXISTS user_sessions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  session_id VARCHAR(100) NOT NULL UNIQUE,
  first_page VARCHAR(500),
  last_page VARCHAR(500),
  page_count INTEGER DEFAULT 1,
  duration INTEGER, -- 会话时长（秒）
  referrer VARCHAR(500),
  user_agent TEXT,
  ip_address INET,
  country VARCHAR(100),
  city VARCHAR(100),
  device_type VARCHAR(50),
  browser VARCHAR(100),
  os VARCHAR(100),
  started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  ended_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_page_views_created_at ON page_views(created_at);
CREATE INDEX IF NOT EXISTS idx_page_views_page_path ON page_views(page_path);
CREATE INDEX IF NOT EXISTS idx_page_views_session_id ON page_views(session_id);

CREATE INDEX IF NOT EXISTS idx_amazon_clicks_created_at ON amazon_clicks(created_at);
CREATE INDEX IF NOT EXISTS idx_amazon_clicks_product_id ON amazon_clicks(product_id);
CREATE INDEX IF NOT EXISTS idx_amazon_clicks_session_id ON amazon_clicks(session_id);

CREATE INDEX IF NOT EXISTS idx_daily_stats_date ON daily_stats(date);

CREATE INDEX IF NOT EXISTS idx_user_sessions_session_id ON user_sessions(session_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_started_at ON user_sessions(started_at);

-- 6. 启用行级安全策略（RLS）
ALTER TABLE page_views ENABLE ROW LEVEL SECURITY;
ALTER TABLE amazon_clicks ENABLE ROW LEVEL SECURITY;
ALTER TABLE daily_stats ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_sessions ENABLE ROW LEVEL SECURITY;

-- 7. 创建RLS策略（允许所有操作，因为这是内部分析数据）
-- 页面访问记录策略
CREATE POLICY "Allow all page_views operations" ON page_views FOR ALL USING (true) WITH CHECK (true);

-- Amazon点击记录策略
CREATE POLICY "Allow all amazon_clicks operations" ON amazon_clicks FOR ALL USING (true) WITH CHECK (true);

-- 每日统计策略
CREATE POLICY "Allow all daily_stats operations" ON daily_stats FOR ALL USING (true) WITH CHECK (true);

-- 用户会话策略
CREATE POLICY "Allow all user_sessions operations" ON user_sessions FOR ALL USING (true) WITH CHECK (true);

-- 8. 创建更新时间戳的触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 9. 为需要的表添加更新时间戳触发器
CREATE TRIGGER update_daily_stats_updated_at 
    BEFORE UPDATE ON daily_stats 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_sessions_updated_at 
    BEFORE UPDATE ON user_sessions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 10. 验证表创建
SELECT 'Analytics tables created successfully!' as status;

-- 显示创建的表
SELECT 
  table_name,
  column_name,
  data_type,
  is_nullable
FROM information_schema.columns 
WHERE table_name IN ('page_views', 'amazon_clicks', 'daily_stats', 'user_sessions')
ORDER BY table_name, ordinal_position;

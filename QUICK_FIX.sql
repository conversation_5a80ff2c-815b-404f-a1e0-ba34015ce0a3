-- 🚀 快速修复Supabase RLS策略问题
-- 复制以下SQL代码到Supabase Dashboard的SQL Editor中执行

-- 1. 删除现有的不完整策略
DROP POLICY IF EXISTS "Allow public read access" ON products;
DROP POLICY IF EXISTS "Allow public read access" ON articles;
DROP POLICY IF EXISTS "Allow public read access" ON categories;

-- 2. 为products表创建完整的RLS策略
CREATE POLICY "Allow public read products" ON products FOR SELECT USING (true);
CREATE POLICY "Allow anon insert products" ON products FOR INSERT WITH CHECK (true);
CREATE POLICY "Allow anon update products" ON products FOR UPDATE USING (true) WITH CHECK (true);
CREATE POLICY "Allow anon delete products" ON products FOR DELETE USING (true);

-- 3. 为articles表创建完整的RLS策略
CREATE POLICY "Allow public read published articles" ON articles FOR SELECT USING (published = true);
CREATE POLICY "Allow anon read all articles" ON articles FOR SELECT USING (true);
CREATE POLICY "Allow anon insert articles" ON articles FOR INSERT WITH CHECK (true);
CREATE POLICY "Allow anon update articles" ON articles FOR UPDATE USING (true) WITH CHECK (true);
CREATE POLICY "Allow anon delete articles" ON articles FOR DELETE USING (true);

-- 4. 为categories表创建完整的RLS策略
CREATE POLICY "Allow public read categories" ON categories FOR SELECT USING (true);
CREATE POLICY "Allow anon insert categories" ON categories FOR INSERT WITH CHECK (true);
CREATE POLICY "Allow anon update categories" ON categories FOR UPDATE USING (true) WITH CHECK (true);
CREATE POLICY "Allow anon delete categories" ON categories FOR DELETE USING (true);

-- ✅ 执行完成后，运行 npm run test:supabase 验证修复结果

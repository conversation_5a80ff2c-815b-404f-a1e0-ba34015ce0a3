-- =====================================================
-- ASIN Migration Script for Supabase
-- 目的：将产品表的amazon_url字段改为asin字段
-- 包含数据迁移：从现有Amazon URL中提取ASIN
-- =====================================================

-- 1. 首先检查当前products表结构
SELECT 
  column_name, 
  data_type, 
  is_nullable 
FROM information_schema.columns 
WHERE table_name = 'products' 
ORDER BY ordinal_position;

-- 2. 备份现有产品数据
CREATE TABLE products_backup_asin AS 
SELECT * FROM products;

-- 3. 添加新的asin字段
ALTER TABLE products 
ADD COLUMN IF NOT EXISTS asin VARCHAR(10);

-- 4. 创建函数来从Amazon URL中提取ASIN
CREATE OR REPLACE FUNCTION extract_asin_from_url(url TEXT)
RETURNS VARCHAR(10) AS $$
BEGIN
  -- 处理各种Amazon URL格式
  -- 格式1: https://amazon.com.mx/dp/B08XXXXXX
  -- 格式2: https://amazon.com.mx/product-name/dp/B08XXXXXX
  -- 格式3: https://amazon.com.mx/gp/product/B08XXXXXX
  
  -- 提取 /dp/ 后面的ASIN
  IF url ~ '/dp/[A-Z0-9]{10}' THEN
    RETURN substring(url from '/dp/([A-Z0-9]{10})');
  END IF;
  
  -- 提取 /gp/product/ 后面的ASIN
  IF url ~ '/gp/product/[A-Z0-9]{10}' THEN
    RETURN substring(url from '/gp/product/([A-Z0-9]{10})');
  END IF;
  
  -- 提取 /product/ 后面的ASIN
  IF url ~ '/product/[A-Z0-9]{10}' THEN
    RETURN substring(url from '/product/([A-Z0-9]{10})');
  END IF;
  
  -- 如果URL中包含example，生成示例ASIN
  IF url LIKE '%example%' THEN
    RETURN 'B08' || LPAD(EXTRACT(EPOCH FROM NOW())::INTEGER % 10000000, 7, '0');
  END IF;
  
  -- 如果无法提取，返回NULL
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 5. 从现有amazon_url中提取ASIN并填充新字段
UPDATE products 
SET asin = extract_asin_from_url(amazon_url)
WHERE amazon_url IS NOT NULL;

-- 6. 为没有ASIN的记录生成示例ASIN（用于测试数据）
UPDATE products 
SET asin = 'B08' || LPAD((EXTRACT(EPOCH FROM NOW())::INTEGER + id::text::INTEGER) % 10000000, 7, '0')
WHERE asin IS NULL;

-- 7. 设置asin字段为NOT NULL
ALTER TABLE products 
ALTER COLUMN asin SET NOT NULL;

-- 8. 添加ASIN格式检查约束
ALTER TABLE products 
ADD CONSTRAINT check_asin_format 
CHECK (asin ~ '^[A-Z0-9]{10}$');

-- 9. 为asin字段创建索引
CREATE INDEX IF NOT EXISTS idx_products_asin ON products(asin);

-- 10. 验证迁移结果
SELECT 
  'Migration Summary' as info,
  COUNT(*) as total_products,
  COUNT(CASE WHEN asin IS NOT NULL THEN 1 END) as products_with_asin,
  COUNT(CASE WHEN amazon_url IS NOT NULL THEN 1 END) as products_with_url
FROM products;

-- 11. 显示迁移前后的对比示例
SELECT 
  id,
  name,
  amazon_url,
  asin,
  'https://www.amazon.com.mx/dp/' || asin as new_generated_url
FROM products 
LIMIT 5;

-- 12. 检查是否有无效的ASIN
SELECT 
  id,
  name,
  asin,
  amazon_url
FROM products 
WHERE asin IS NULL OR asin !~ '^[A-Z0-9]{10}$'
LIMIT 10;

-- 13. 删除提取函数（清理）
DROP FUNCTION IF EXISTS extract_asin_from_url(TEXT);

-- 14. 显示完成状态
SELECT 'ASIN migration completed successfully! amazon_url field is preserved for reference.' as status;

-- =====================================================
-- 注意事项：
-- 1. 此脚本保留了原有的amazon_url字段作为参考
-- 2. 如果确认迁移成功，可以手动删除amazon_url字段
-- 3. 建议在生产环境执行前先在测试环境验证
-- 4. 备份表products_backup_asin包含了迁移前的所有数据
-- =====================================================

-- 可选：删除amazon_url字段（请在确认迁移成功后执行）
-- ALTER TABLE products DROP COLUMN IF EXISTS amazon_url;

-- =====================================================
-- 安全的Supabase数据库迁移脚本
-- 目的：保留现有数据的同时简化产品表结构
-- 这个脚本会保留现有的产品数据
-- =====================================================

-- 1. 首先检查当前products表结构
SELECT 
  column_name, 
  data_type, 
  is_nullable 
FROM information_schema.columns 
WHERE table_name = 'products' 
ORDER BY ordinal_position;

-- 2. 备份现有产品数据到临时表
CREATE TABLE products_backup AS 
SELECT * FROM products;

-- 3. 创建新的简化products表
CREATE TABLE products_new (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  category VARCHAR(100) NOT NULL,
  amazon_url TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. 迁移现有数据到新表（只保留需要的字段）
INSERT INTO products_new (id, name, category, amazon_url, created_at, updated_at)
SELECT 
  id,
  name,
  category,
  COALESCE(amazon_url, 'https://amazon.com.mx/dp/placeholder') as amazon_url,
  created_at,
  updated_at
FROM products
WHERE name IS NOT NULL AND category IS NOT NULL;

-- 5. 检查数据迁移结果
SELECT 
  'Original products count:' as info,
  COUNT(*) as count
FROM products
UNION ALL
SELECT 
  'Migrated products count:' as info,
  COUNT(*) as count
FROM products_new;

-- 6. 如果数据迁移成功，替换原表
-- 注意：这会删除原表，请确保数据迁移正确
BEGIN;

-- 删除依赖的外键约束
ALTER TABLE IF EXISTS articles DROP CONSTRAINT IF EXISTS articles_product_id_fkey;

-- 删除原表
DROP TABLE IF EXISTS products CASCADE;

-- 重命名新表
ALTER TABLE products_new RENAME TO products;

-- 重新创建外键约束
ALTER TABLE articles 
ADD CONSTRAINT articles_product_id_fkey 
FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE;

COMMIT;

-- 7. 创建索引
CREATE INDEX idx_products_category ON products(category);
CREATE INDEX idx_products_name ON products(name);

-- 8. 创建更新触发器
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_products_updated_at 
    BEFORE UPDATE ON products 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- 9. 启用RLS并创建策略
ALTER TABLE products ENABLE ROW LEVEL SECURITY;

-- 删除旧策略（如果存在）
DROP POLICY IF EXISTS "Allow public read products" ON products;
DROP POLICY IF EXISTS "Allow anon insert products" ON products;
DROP POLICY IF EXISTS "Allow anon update products" ON products;
DROP POLICY IF EXISTS "Allow anon delete products" ON products;

-- 创建新策略
CREATE POLICY "Allow public read products" ON products FOR SELECT USING (true);
CREATE POLICY "Allow anon insert products" ON products FOR INSERT WITH CHECK (true);
CREATE POLICY "Allow anon update products" ON products FOR UPDATE USING (true) WITH CHECK (true);
CREATE POLICY "Allow anon delete products" ON products FOR DELETE USING (true);

-- 10. 验证迁移结果
SELECT 'Final verification:' as status;

SELECT 
  'Products table structure after migration:' as info;

\d products;

SELECT 
  'Sample migrated products:' as info;

SELECT 
  id,
  name,
  category,
  amazon_url,
  created_at
FROM products 
LIMIT 5;

-- 11. 检查articles表的关联是否正常
SELECT 
  'Articles with product associations:' as info,
  COUNT(*) as count
FROM articles a
JOIN products p ON a.product_id = p.id;

-- 12. 清理备份表（可选，建议保留一段时间）
-- DROP TABLE products_backup;

SELECT 
  'Migration completed successfully!' as status,
  'Backup table "products_backup" preserved for safety' as note,
  'You can drop it later: DROP TABLE products_backup;' as cleanup_command;

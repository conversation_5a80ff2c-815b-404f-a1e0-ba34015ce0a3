{"name": "site", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "dev:webpack": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "build:analyze": "ANALYZE=true npm run build", "export": "next build && next export", "deploy": "./scripts/deploy.sh", "deploy:vercel": "./scripts/deploy.sh vercel", "deploy:netlify": "./scripts/deploy.sh netlify", "clean": "rm -rf .next out", "seed": "tsx src/scripts/seedDatabase.ts", "test:supabase": "node scripts/test-supabase-connection.js"}, "dependencies": {"@supabase/supabase-js": "^2.50.2", "clsx": "^2.1.1", "highlight.js": "^11.11.1", "lucide-react": "^0.523.0", "next": "15.3.4", "next-seo": "^6.8.0", "plyr": "^3.7.8", "plyr-react": "^5.3.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^10.1.0", "recharts": "^3.0.2", "rehype-highlight": "^7.0.2", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "typescript": "^5"}}
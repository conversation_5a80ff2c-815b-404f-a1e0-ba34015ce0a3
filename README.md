# Tu Tienda Íntima - Sitio Web de Reseñas de Productos para Adultos

Un sitio web moderno y SEO-optimizado para reseñas de productos íntimos, diseñado para dirigir tráfico a tu tienda de Amazon México.

## 🚀 Características

- **SEO Optimizado**: Meta tags, sitemap automático, datos estructurados
- **Responsive Design**: Funciona perfectamente en móviles y desktop
- **Gestión de Contenido**: Panel de administración para productos y artículos
- **Integración Amazon**: Botones optimizados para conversión
- **Cumplimiento Legal**: Verificación de edad y advertencias de contenido
- **Performance**: Optimizado para velocidad y Core Web Vitals
- **Multiidioma**: Completamente en español mexicano

## 🛠️ Tecnologías

- **Framework**: Next.js 15 con App Router
- **Styling**: Tailwind CSS 4
- **Base de Datos**: Supabase
- **Iconos**: Lucide React
- **SEO**: next-seo
- **Deployment**: Vercel/Netlify ready

## 📦 Instalación

1. **Instalar dependencias**
```bash
npm install
```

2. **Configurar variables de entorno**
```bash
cp .env.local.example .env.local
```

3. **Iniciar el servidor de desarrollo**
```bash
npm run dev
```

4. **Visitar el sitio**
Abre [http://localhost:3001](http://localhost:3001) en tu navegador.

## 🎯 Panel de Administración

- Visita `/admin`
- Contraseña: `admin123`
- Gestiona productos y artículos

## 🚀 Despliegue

```bash
npm run deploy:vercel  # Para Vercel
npm run deploy:netlify # Para Netlify
```

## 📊 Estructura del Proyecto

```
src/
├── app/                    # Páginas Next.js
├── components/            # Componentes reutilizables
├── lib/                   # Utilidades y configuración
├── types/                 # Definiciones TypeScript
└── data/                  # Datos de ejemplo
```

## 🔧 Scripts Disponibles

- `npm run dev` - Servidor de desarrollo
- `npm run build` - Construir para producción
- `npm run deploy` - Despliegue automático
- `npm run lint` - Linter
- `npm run clean` - Limpiar archivos de build

## 📝 Notas Importantes

- El sitio incluye verificación de edad automática
- Todos los enlaces de Amazon deben incluir tu ID de afiliado
- El contenido está optimizado para SEO en español mexicano
- Panel de administración incluido para gestión de contenido

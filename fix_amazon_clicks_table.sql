-- =====================================================
-- 修复Amazon点击表的外键约束
-- 目的：允许product_id为NULL，以支持占位符产品的点击跟踪
-- =====================================================

-- 1. 修改amazon_clicks表，让product_id可以为NULL
ALTER TABLE amazon_clicks 
ALTER COLUMN product_id DROP NOT NULL;

-- 2. 验证修改
SELECT 
  column_name,
  is_nullable,
  data_type
FROM information_schema.columns 
WHERE table_name = 'amazon_clicks' 
  AND column_name = 'product_id';

-- 3. 测试插入一条没有product_id的记录
INSERT INTO amazon_clicks (
  product_id,
  product_name,
  amazon_url,
  page_path,
  session_id,
  device_type,
  browser,
  os
) VALUES (
  NULL,
  'Test Product Without ID',
  'https://amazon.com/test-no-id',
  '/test',
  'test-session-no-id',
  'desktop',
  'Chrome',
  'Windows'
);

-- 4. 验证插入成功
SELECT COUNT(*) as total_clicks FROM amazon_clicks;

-- 5. 显示最近的几条记录
SELECT 
  id,
  product_id,
  product_name,
  amazon_url,
  created_at
FROM amazon_clicks 
ORDER BY created_at DESC 
LIMIT 5;

SELECT 'Amazon clicks table fixed successfully!' as status;

-- 测试测验系统迁移的简化版本

-- 1. 创建测验配置表
CREATE TABLE IF NOT EXISTS quiz_configs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  questions JSONB NOT NULL,
  result_groups JSONB NOT NULL,
  dimensions TEXT[] NOT NULL,
  active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. 创建产品分组规则表
CREATE TABLE IF NOT EXISTS product_group_rules (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  emoji VARCHAR(10),
  rules JSONB NOT NULL,
  priority INTEGER DEFAULT 0,
  active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. 创建测验会话表
CREATE TABLE IF NOT EXISTS quiz_sessions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  quiz_config_id UUID REFERENCES quiz_configs(id) ON DELETE CASCADE,
  answers JSONB,
  result JSONB,
  started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE,
  user_id VARCHAR(255),
  session_id VARCHAR(255) NOT NULL,
  ip_address INET,
  user_agent TEXT
);

-- 4. 创建产品与分组规则的关联表
CREATE TABLE IF NOT EXISTS product_group_assignments (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  product_id UUID REFERENCES products(id) ON DELETE CASCADE,
  group_rule_id UUID REFERENCES product_group_rules(id) ON DELETE CASCADE,
  assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(product_id, group_rule_id)
);

-- 测试插入一个简单的测验配置
INSERT INTO quiz_configs (title, description, questions, result_groups, dimensions, active) VALUES (
  'Test Quiz',
  'A simple test quiz',
  '[{"id": "test", "text": "Test question?", "type": "single_choice", "options": [{"id": "1", "text": "Option 1", "value": "1"}]}]'::jsonb,
  '[{"id": "result1", "title": "Test Result", "description": "Test description", "emoji": "🎯", "dimensionScores": {"test": [1, 2]}, "productCategories": ["test"]}]'::jsonb,
  ARRAY['test'],
  true
);

SELECT 'Quiz migration test completed successfully' as status;
